<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "titles".
 *
 * @property int $id
 * @property string $elementid
 * @property string $title
 */
class Titles extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'titles';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'string'],
            [['elementid'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'elementid' => 'Elementid',
            'title' => 'Title',
        ];
    }
}
