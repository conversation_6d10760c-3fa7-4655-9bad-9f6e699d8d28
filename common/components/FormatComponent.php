<?php

namespace app\common\components;

use yii\i18n\Formatter;

class FormatComponent extends Formatter
{
    public function init()
    {
        parent::init();
    }

    public function str2float($value)
    {
        return $this->string2float($value);
    }

    public function string2float($value)
    {
        if (!is_float($value)) {
            $value = (double)str_replace(",", ".", str_replace(["#", "€", " "], "", $value));
        }
        return $value;
    }

    public function s2f($value)
    {
        return $this->string2float($value);
    }

    public function f2s($value)
    {
        return $this->float2string($value);
    }

    public function float2string($value)
    {
        return str_replace(".", ",", round($value, 4));
    }

    public function float2str($value)
    {
        return $this->float2string($value);
    }

}
