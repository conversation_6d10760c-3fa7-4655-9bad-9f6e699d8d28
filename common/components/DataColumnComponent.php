<?php

namespace app\common\components;

use \yii\grid\DataColumn;
use yii\helpers\Html;

class DataColumnComponent extends DataColumn
{

    /*
     * Fügt ein Titel beim Link ein (Für die Barrierefreiheit)
     *
     * */
    protected function renderHeaderCellContent()
    {
        if ($this->header !== null || $this->label === null && $this->attribute === null) {
            return parent::renderHeaderCellContent();
        }

        $label = $this->getHeaderCellLabel();
        if ($this->encodeLabel) {
            $label = Html::encode($label);
        }

        if ($this->attribute !== null && $this->enableSorting &&
            ($sort = $this->grid->dataProvider->getSort()) !== false && $sort->hasAttribute($this->attribute)) {
            $sortStatus = "Aufsteigend";

            if($sort->getAttributeOrder($this->attribute) === 3)
            {
                $sortStatus = "Aufsteigend";
            }

            if($sort->getAttributeOrder($this->attribute) === 4)
            {
                $sortStatus = "Absteigend";
            }

            return $sort->link($this->attribute, array_merge($this->sortLinkOptions, ['label' => $label, 'title' => "Sortiere ${label} ${sortStatus}"]));
        }

        return $label;
    }
}
