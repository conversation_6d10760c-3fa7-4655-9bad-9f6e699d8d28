<?php

namespace app\common\components;

use yii\base\Component;
use yii\base\ErrorException;

/*
 * Diese Komponente stellt die grundsätzliche LDAP Funktionalität bereit.
 *
 * Die Options werden in config/web.php gesetzt!
 *
 */

class LdapComponent extends Component
{
    public $conn;
    public $options = [];
    public $ldap_errno = 0;
    public $ldap_errstr = "";
    public $ldap_ext_errno = 0;
    public $ldap_ext_errstr = "";
    public $errno;
    public $errstr;
    public $result = [];
    public $stoptrying = false;

    public function init()
    {
        putenv("LDAPTLS_REQCERT=never");
        if (!empty($this->conn)) {
            ldap_close($this->conn);
        }
        if (isset($this->options['ldaps']) && $this->options['ldaps'] == true) {
            $connectstring = "ldaps://";
        } else {
            $connectstring = "ldap://";
        }
        $connectstring .= $this->options['server'];
        if (isset($this->options['port'])) {
            $connectstring .= ":" . $this->options["port"] . "/";
        } else {
            $connectstring .= "/";
        }

        if (empty($this->options['admin_password'])) {
            throw new \Exception("Das Admin Passwort für den LDAP Server ist nicht gesetzt!");
        }

        try {
            $this->conn = ldap_connect($connectstring);
        } catch (ErrorException $ex) {
            $this->setErrors($ex);
            die("Keine Verbindung zum LDAP Server!");
        }
        ldap_set_option($this->conn, LDAP_OPT_PROTOCOL_VERSION, 3); // Recommended for AD
        ldap_set_option($this->conn, LDAP_OPT_REFERRALS, 0);
        ldap_set_option($this->conn, LDAP_OPT_NETWORK_TIMEOUT, 2);

        parent::init();
    }

    public function setErrors($ex)
    {
        $this->unsetErrors();
        $this->errstr = $ex->getMessage();
        $this->errno = $ex->getCode();
        $this->setExtErrors();
    }

    public function unsetErrors()
    {
        $this->errno = 0;
        $this->errstr = "";
        $this->ldap_errstr = "";
        $this->ldap_errno = 0;
        $this->ldap_ext_errno = 0;
        $this->ldap_ext_errstr = "";
    }

    public function setExtErrors()
    {
        ldap_get_option($this->conn, LDAP_OPT_ERROR_NUMBER, $this->ldap_errno);
        ldap_get_option($this->conn, LDAP_OPT_ERROR_STRING, $this->ldap_errstr);
        /*
         * Erweiterer Errorcode
          const PWD_EXPIRED = 1330;
          const PWD_MUST_CHANGE = 1907;
          const ACCOUNT_LOCKED = 1909;
          const ACCOUNT_DISABLED = 1331;
         */
        if (!empty($this->ldap_errstr) && preg_match('/, data (\d+),?/', $this->ldap_errstr, $matches)) {
            $this->ldap_ext_errno = hexdec(intval($matches[1]));
            if ($this->ldap_ext_errno === 1330) {
                $this->ldap_ext_errstr = "Ihr Passwort ist abgelaufen, bitte aktualisieren Sie Ihr Windows Passwort und versuchen Sie es erneut.";
                $this->stoptrying = true;
            } else if ($this->ldap_ext_errno === 1907) {
                $this->ldap_ext_errstr = "Ihr Passwort ist abgelaufen, bitte aktualisieren Sie Ihr Windows Passwort und versuchen Sie es erneut.";
                $this->stoptrying = true;
            } else if ($this->ldap_ext_errno === 1909) {
                $this->ldap_ext_errstr = "Ihr Account ist gesperrt, bitte wenden Sie sich an die Hotline!";
            } else if ($this->ldap_ext_errno === 1331) {
                $this->ldap_ext_errstr = "Ihr Account ist deaktiviert, bitte wenden Sie sich an die Hotline!";
            } else if ($this->ldap_ext_errno === 82) {
                $this->ldap_ext_errstr = 'Unbekannter Windows Benutzer oder falsches Passwort!';
            } else {
                $this->ldap_ext_errstr = "Der Login ist fehlgeschlagen (" . $this->ldap_errstr . ")";
            }
        }
    }

    public function getLasterrno()
    {
        if (!empty($this->ldap_ext_errno)) {
            return $this->ldap_ext_errno;
        } else if (!empty($this->ldap_errno)) {
            return $this->ldap_errno;
        } else if (!empty($this->errno)) {
            return $this->errno;
        } else {
            return 0;
        }
    }

    public function getLasterrstr()
    {
        if (!empty($this->ldap_ext_errstr)) {
            return $this->ldap_ext_errstr;
        } else if (!empty($this->ldap_errstr)) {
            return $this->ldap_errstr;
        } else if (!empty($this->errstr)) {
            return $this->errstr;
        } else {
            return "";
        }
    }

    public function bind($username, $pass)
    {
        try {
            if (empty($username)) {
                $this->ldap_ext_errno = 667;
                $this->ldap_ext_errstr = "Der Benutzername kann nicht leer sein!";
                return false;
            } else if (empty($pass)) {
                $this->ldap_ext_errno = 666;
                $this->ldap_ext_errstr = "Das Passwort kann nicht leer sein!";
                return false;
            } else if (ldap_bind($this->conn, $username, $pass)) {
                return true;
            } else {
                $this->setExtErrors();
                return false;
            }
        } catch (\yii\base\ErrorException $ex) {
            $this->setErrors($ex);
            return false;
        }
    }

    public function bind_anonym()
    {
        try {
            if (ldap_bind($this->conn)) {
                return true;
            } else {
                $this->setExtErrors();
                return false;
            }
        } catch (\yii\base\ErrorException $ex) {
            $this->setErrors($ex);
            return false;
        }
    }

    public function bind_admin()
    {
        try {
            if (ldap_bind($this->conn, $this->options['admin_username'], $this->options['admin_password'])) {
                return true;
            } else {
                $this->setExtErrors();
                return false;
            }
        } catch (\yii\base\ErrorException $ex) {
            $this->setErrors($ex);
            return false;
        }
    }

    public function search_person($name)
    {
        $name = str_ireplace(['*', ',', '.', '"', '\'', '\\', '&', '#'], '', $name);
        $filter = "(|";
        $filter .= "(cn=*" . trim($name ?? "") . "*)";
        $namensteile = explode(' ', $name);
        $vornamename = $name;
        // Bitte nicht auf der Space Taste einschlafen!
        foreach ($namensteile as $key => $nt) {
            if (empty($nt)) {
                unset($namensteile[$key]);
            }
        }
        if (count($namensteile) > 1) {
            $vornamename = array_pop($namensteile) . '*' . implode(' ', $namensteile);
        }
        if (trim($name ?? "") != trim($vornamename ?? "")) {
            $filter .= "(cn=*" . $vornamename . "*)";
        }
        $filter .= ")";
        try {
            $results = ldap_search($this->conn, $this->options['base_dn'], $filter, $this->options["search_attributes"]);
            $this->result = ldap_get_entries($this->conn, $results);
            return true;
        } catch (\yii\base\ErrorException $ex) {
            $this->setErrors($ex);
            return false;
        }
    }

    public function search_account($name)
    {
        if (substr_count($name, '@') > 0) {
            $filter = "(|(mail=" . $name . ")(userprincipalname=" . $name . "))";
        } else {
            $filter = "(samaccountname=" . $name . ")";
        }
        $base_dn = $this->options['base_dn'];
        try {
            $results = ldap_search($this->conn, $base_dn, $filter, $this->options["search_attributes"]);
            $this->result = ldap_get_entries($this->conn, $results);
            return true;
        } catch (\yii\base\ErrorException $ex) {
            $this->setErrors($ex);
            return false;
        }
    }

    public function dumpError()
    {
        if (!empty($this->errno)) {
            echo "<BR />Ein Fehler ist aufgetreten: " . $this->errstr . " (" . $this->errno . ")";
        }
        if (!empty($this->ldap_errno)) {
            echo "<BR />Intern: " . $this->ldap_errstr . " (" . $this->ldap_errno . ")";
        }
        if (!empty($this->ldap_ext_errno)) {
            echo "<BR />LDAP: " . $this->ldap_ext_errstr . " (" . $this->ldap_ext_errno . ")";
        }
    }

    public function getResultArray($entry = null)
    {
        $retEntry = [];
        if (empty($this->result)) {
            return [];
        }
        if (empty($entry)) {
            $entry = $this->result;
        }
        for ($i = 0; $i < $entry['count']; $i++) {
            if (is_array($entry[$i])) {
                $subtree = $entry[$i];
                $key = $subtree['dn'];
                $retEntry[$key] = $this->getResultArray($subtree);
            } else {
                $attribute = $entry[$i];
                if ($entry[$attribute]['count'] == 1) {
                    $retEntry[$attribute] = $entry[$attribute][0];
                } else {
                    for ($j = 0; $j < $entry[$attribute]['count']; $j++) {
                        $retEntry[$attribute][] = $entry[$attribute][$j];
                    }
                }
            }
        }
        return $retEntry;
    }
}
