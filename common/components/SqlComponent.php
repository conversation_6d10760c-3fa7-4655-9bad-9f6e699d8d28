<?php

namespace app\common\components;

use Yii;
use yii\base\Component;

class SqlComponent extends Component {

    public $conn;
    public $log;

    public function init() {
        $this->conn = Yii::$app->getDb();
        return parent::init();
    }

    public function parsefile($filePath) {

        if (!isset($filePath))
            return false;

        if (!file_exists($filePath))
            throw new \Exception("'$filePath' is not a file");

        $time = microtime(true);
        $data = file($filePath);

        if (empty($data))
            throw new Exception("'$filePath' is not a file or empty");

        $sql = '';
        foreach ($data as $row) {
            $row = trim($row);
            // Ignore line if empty line or comment
            if (empty($row) || substr($row, 0, 2) == '--')
                continue;
            $sql .= $row . ' ';
            if (strpos($row, ';')) {
                try {
                    $command = $this->conn->createCommand($sql);
                    $result = $command->execute();
                    $this->log.=$sql;
                    $sql = "";
                } catch (Exception $e) {
                    var_dump($sql);
                }
            }
        }
        return true;
    }
}

?>