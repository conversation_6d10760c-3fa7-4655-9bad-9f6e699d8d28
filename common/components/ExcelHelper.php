<?php
namespace app\common\components;

use PhpOffice\PhpSpreadsheet\IOFactory;
use Yii;

class ExcelHelper
{

    public static function _readExcel($inputFileName)
    {

        $returndata = Array();

        $inputFileType = IOFactory::identify($inputFileName);
        $objReader = IOFactory::createReader($inputFileType);
        $objPHPExcel = $objReader->load($inputFileName);

        $sheet = $objPHPExcel->getSheet(0);

        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

        $tables = Array();

        for ($row = 1; $row <= $highestRow; $row++) {
            $rowdata = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, NULL, TRUE, FALSE);
            if (!empty($rowdata))
                $returndata[] = $rowdata[0];
        }

        return $returndata;

    }
}


