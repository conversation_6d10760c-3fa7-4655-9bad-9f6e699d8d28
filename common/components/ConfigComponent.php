<?php

namespace app\common\components;

/*
 * Diese Komponente hilft beim <PERSON> und E<PERSON><PERSON><PERSON> von Konfig Parametern
 *
 */

use app\models\Konfiguration;
use yii\base\Component;
use yii\base\Exception;

class ConfigComponent extends Component
{

    public function init()
    {
        parent::init();
    }

    public function exists($name)
    {
        $query = Konfiguration::find()->where(["name" => $name, "deprecated" => 0]);
        return $query->count() >= 1;
    }

    public function get($name)
    {
        $query = Konfiguration::find()->where(["name" => $name, "deprecated" => 0]);
        if ($query->count() > 1) {
            throw new Exception("Der Parameterwert " . $name . " ist mehrfach gesetzt, bitte kontaktieren Sie den Administrator der Anwendung!");
        } else if ($query->count() == 0) {
            throw new Exception("Der Parameterwert " . $name . " ist nicht gesetzt, bitte kontaktieren Sie den Administrator der Anwendung!");
        } else {
            $parameter = $query->one();
        }
        return $parameter->wert;
    }

    public function disable($name)
    {
        $query = Konfiguration::find()->where(["name" => $name, "deprecated" => 0]);
        foreach ($query->all() as $param) {
            $param->deprecated = 1;
            $param->save();
        }

    }

    public function set($name, $wert, $beschreibung = "", $deprecated = 0)
    {
        if ($this->exists($name)) {
            $this->disable($name);
        }
        $parameter = new Konfiguration();
        $parameter->name = $name;
        $parameter->wert = $wert;
        $parameter->beschreibung = $beschreibung;
        $parameter->deprecated = $deprecated;
        if (!$parameter->save()) {
            //var_dump($parameter->errors);
            throw new Exception("Der Parameterwert " . $name . " mit dem Wert " . $wert . " kann nicht gesetzt werden!");
        } else {
            return true;
        }
    }
}
