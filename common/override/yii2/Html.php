<?php

namespace yii\helpers;

class Html extends BaseHtml
{
    /**
     * @inheritDoc
     *
     * @param array $options
     * @param array|string $class
     */
    public static function addCssClass(&$options, $class)
    {
        if (!is_array($class)) {
            $class = preg_split('/\s+/', $class, -1, PREG_SPLIT_NO_EMPTY);
        }
        if (isset($options['class'])) {
            if (is_array($options['class'])) {
                $options['class'] = self::mergeCssClasses($options['class'], $class);
            } else {
                $classes = preg_split('/\s+/', $options['class'], -1, PREG_SPLIT_NO_EMPTY);
                $options['class'] = implode(' ', self::mergeCssClasses($classes, $class));
            }
        } else {
            $options['class'] = $class;
        }
    }

    private static function mergeCssClasses(array $existingClasses, array $additionalClasses)
    {
        foreach ($additionalClasses as $key => $class) {
            if (is_int($key) && !in_array($class, $existingClasses)) {
                $existingClasses[] = $class;
            } else if (!isset($existingClasses[$key])) {
                $existingClasses[$key] = $class;
            }
        }

        return static::$normalizeClassAttribute ? array_unique($existingClasses) : $existingClasses;
    }

}
