<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\common\override\yii2;

/**
 * File system helper.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class FileHelper extends \yii\helpers\FileHelper
{

    public static function rmr($dir)
    {
        // Wenn der Input ein Ordner ist, dann Überprüfung des Inhaltes beginnen
        if (is_dir($dir)) {
            // Ordnerinhalt auflisten und jedes Element nacheinander überprüfen
            $dircontent = scandir($dir);
            foreach ($dircontent as $c) {
                // Wenn es sich um einen Ordner handelt, die Funktion rmr(); aufrufen
                if ($c != '.' and $c != '..' and is_dir($dir . '/' . $c)) {
                    static::rmr($dir . '/' . $c);
                    // Wenn es eine Datei ist, diese löschen
                } else if ($c != '.' and $c != '..') {
                    @unlink($dir . '/' . $c);
                }
            }
            // Den nun leeren Ordner löschen
            rmdir($dir);
            // Wenn es sich um eine Datei handelt, diese löschen
        } else {
            @unlink($dir);
        }
    }

}
