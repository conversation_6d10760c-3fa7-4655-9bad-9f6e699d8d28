<?php

namespace app\common\override\yii2;

use app\models\LabelManager;
use Yii;
use yii\helpers\ArrayHelper;

class ActiveField extends \yii\bootstrap4\ActiveField
{
    public $readonly = false;
    public $print = false;

    public function hiddenInput($options = [])
    {
        $attr = $this->attribute;
        if ($this->print) {
            return "";
        } else {
            return Yii::$app->controller->renderPartial('../site/_hiddeninput', [
                'attribute' => $attr,
                'modelname' => $this->model->formName(),
                'required' => $this->model->isAttributeRequired($this->attribute),
                'readonly' => $this->readonly,
                'print' => $this->print,
                'value' => $this->model->$attr,
                'label' => $this->model->getAttributeLabel($attr),
            ]);
        }
    }

    /**
     * {@inheritdoc}
     * @return string|ActiveField the created ActiveField object
     */
    public function input($type, $options = [])
    {
        if ($this->readonly) {
            $options = array_merge($options, ['class' => 'readonly', 'readonly' => 'readonly']);
        } else if ($this->print) {
            $attr = $this->attribute;
            $value = $this->model->$attr;
            return Yii::$app->controller->renderPartial('../site/_printfield', ['attribute' => $attr, 'value' => $value, 'label' => $this->model->getAttributeLabel($attr)]);
        }
        return parent::input($type, $options);
    }

    /**
     * Liefert die Werte aus dem Labelmanager zu $liste
     * {@inheritdoc}
     *
     * @return ActiveField the created ActiveField object
     */
    public function dropDownListLM($liste, $options = [])
    {
        $itemList = LabelManager::find()->where(['liste' => $liste, 'deprecated' => 0])->orderBy(['sortorder' => SORT_ASC, 'value' => SORT_ASC])->all();
        return $this->dropDownList(ArrayHelper::map($itemList, 'value', 'label'), $options);
    }

    /**
     * {@inheritdoc}
     * @return string|ActiveField the created ActiveField object
     */
    public function dropDownList($items, $options = [])
    {
        if ($this->readonly) {
            $options = array_merge($options, ['class' => 'readonly', 'readonly' => 'readonly']);
        } else if ($this->print) {
            $attr = $this->attribute;
            $value = LabelManager::translateValueToLabel($this->model->$attr);
            return Yii::$app->controller->renderPartial('../site/_printfield', ['attribute' => $attr, 'value' => $value, 'label' => $this->model->getAttributeLabel($attr)]);
        }
        return parent::dropDownList($items, $options);
    }

    /**
     * {@inheritdoc}
     * @return string|ActiveField the created ActiveField object
     */
    public function textarea($options = [])
    {
        if ($this->readonly) {
            $options = array_merge($options, ['class' => 'readonly', 'readonly' => 'readonly']);
        } else if ($this->print) {
            $attr = $this->attribute;
            return Yii::$app->controller->renderPartial('../site/_printfield', ['attribute' => $attr, 'value' => $this->model->$attr, 'label' => $this->model->getAttributeLabel($attr)]);
        }
        return parent::textarea($options);
    }

    /**
     * {@inheritdoc}
     * @return string|ActiveField the created ActiveField object
     */
    public function datepicker($options = [])
    {
        if ($this->readonly) {
            $options = array_merge($options, ['class' => 'readonly', 'readonly' => 'readonly']);
        } else if ($this->print) {
            $attr = $this->attribute;
            return Yii::$app->controller->renderPartial('../site/_printfield', ['attribute' => $attr, 'value' => $this->model->$attr, 'label' => $this->model->getAttributeLabel($attr)]);
        } else {
            if (empty($options["dp_class"])) {
                $dp_class = "datepicker";
            } else {
                $dp_class = $options["dp_class"];
            }
            if (empty($options['class'])) {
                $options['class'] = "form-control " . $dp_class;
            } else {
                $options['class'] .= " " . $dp_class;
            }
        }
        return parent::textInput($options);
    }

    /**
     * {@inheritdoc}
     * @return string|ActiveField the created ActiveField object
     */
    public function autocomplete($options = [])
    {
        if ($this->readonly) {
            $options = array_merge($options, ['class' => 'readonly', 'readonly' => 'readonly']);
        } else if ($this->print) {
            $attr = $this->attribute;
            return Yii::$app->controller->renderPartial('../site/_printfield', ['attribute' => $attr, 'value' => $this->model->$attr, 'label' => $this->model->getAttributeLabel($attr)]);
        } else {
            if (empty($options['class'])) {
                $options['class'] = "form-control autocomplete";
            } else {
                $options['class'] .= " autocomplete";
            }
        }
        return parent::textInput($options);
    }

    /**
     * {@inheritdoc}
     * @return string|ActiveField the created ActiveField object
     * @throws \yii\base\InvalidConfigException
     */
    public function ldappicker($options = [])
    {
        $attr = $this->attribute;
        if ($this->readonly) {
            return $this->textInput($options);
        } else if ($this->print) {
            $attr = $this->attribute;
            return Yii::$app->controller->renderPartial('../site/_printfield', ['model' => $this->model, 'attribute' => $attr, 'value' => $this->model->$attr, 'label' => $this->model->getAttributeLabel($attr)]);
        } else {
            return Yii::$app->controller->renderPartial('../site/_ldappicker', [
                'model' => $this->model,
                'attribute' => $attr,
                'readonly' => $this->readonly,
                'print' => $this->print,
            ]);
        }
    }

    /**
     * {@inheritdoc}
     * @return string|ActiveField the created ActiveField object
     */
    public function textInput($options = [])
    {
        if ($this->readonly) {
            $options = array_merge($options, ['class' => 'readonly', 'readonly' => 'readonly']);
        } else if ($this->print) {
            $attr = $this->attribute;
            $value = $this->model->$attr;
            return Yii::$app->controller->renderPartial('../site/_printfield', ['attribute' => $attr, 'value' => $value, 'label' => $this->model->getAttributeLabel($attr)]);
        }
        return parent::textinput($options);
    }
}
