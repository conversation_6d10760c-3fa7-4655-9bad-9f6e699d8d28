<?php

namespace app\common\override\yii2;

class ActiveForm extends \yii\bootstrap4\ActiveForm
{
    public $col_small = [
        'label' => 'col-sm-4',
        'offset' => 'col-sm-offset-4',
        'wrapper' => 'col-sm-8',
        'error' => '',
        'hint' => '',
    ];
    public $col_large = [
        'label' => 'col-sm-2',
        'offset' => 'col-sm-offset-2',
        'wrapper' => 'col-sm-10',
        'error' => '',
        'hint' => '',
    ];
    public $readonly = false;
    public $print = false;
    public $fieldClass = 'app\common\override\yii2\ActiveField';

    /**
     * @param array $config
     *
     * @return \yii\bootstrap4\ActiveForm
     */
    public static function begin($config = [])
    {
        return parent::begin($config);
    }

    /**
     * {@inheritdoc}
     */
    public function field($model, $attribute, $options = [])
    {
        if (empty($options['horizontalCssClasses'])) {
            $options['horizontalCssClasses'] = $this->col_large;
        }
        if (!empty($options['size'])) {
            $col = "col_" . $options['size'];
            $options['horizontalCssClasses'] = $this->$col;
            unset($options["size"]);
        }
        if ($this->readonly) {
            $options = array_merge($options, ['readonly' => true]);
        }
        if ($this->print) {
            $options = array_merge($options, ['print' => true]);
        }
        return parent::field($model, $attribute, $options);
    }
}
