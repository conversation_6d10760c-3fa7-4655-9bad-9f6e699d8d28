<?php

namespace app\common\override\yii2;

use yii\base\Model;
use yii\helpers\Html;

class DataColumn extends \yii\grid\DataColumn
{
    public $aktiv = true;
    public $toggleable = true;

    protected function renderFilterCellContent()
    {
        if (is_string($this->filter)) {
            return $this->filter;
        }

        $model = $this->grid->filterModel;

        if ($this->filter !== false && $model instanceof Model && $this->attribute !== null && $model->isAttributeActive($this->attribute)) {
            if ($model->hasErrors($this->attribute)) {
                Html::addCssClass($this->filterOptions, 'has-error');
                $error = ' ' . Html::error($model, $this->attribute, $this->grid->filterErrorOptions);
            } else {
                $error = '';
            }
            if (empty($this->filterInputOptions["id"])) {
                $this->filterInputOptions["id"] = "filter-" . $this->attribute;
            }
            if (!empty($this->filterInputOptions["class"])) {
                $this->filterInputOptions["class"] .= " filterfield";
            } else {
                $this->filterInputOptions["class"] = "filterfield";
            }
            if (is_array($this->filter)) {
                $options = array_merge(['prompt' => 'alle'], $this->filterInputOptions);
                return Html::activeDropDownList($model, $this->attribute, $this->filter, $options) . $error;
            } else if ($this->format === 'boolean') {
                $options = array_merge(['prompt' => ''], $this->filterInputOptions);
                return Html::activeDropDownList($model, $this->attribute, [
                        1 => $this->grid->formatter->booleanFormat[1],
                        0 => $this->grid->formatter->booleanFormat[0],
                    ], $options) . $error;
            }
            $this->filterInputOptions["data-attribute"] = $this->attribute;
            return Html::activeTextInput($model, $this->attribute, $this->filterInputOptions) . $error;
        }

        return parent::renderFilterCellContent();
    }

}
