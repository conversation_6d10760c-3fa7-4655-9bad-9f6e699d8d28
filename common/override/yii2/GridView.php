<?php

namespace app\common\override\yii2;

class GridView extends \yii\grid\GridView
{
    public $tableOptions = ['class' => 'table table-striped'];
    public $dataColumnClass = 'app\common\override\yii2\DataColumn';

    public $layout = "{items}<div class='row'><div class='col-sm-8'>{pager}</div><div class='col-sm-4 text-right' style='padding:20px'>{summary}</div></div>";

    public $pager = [
        'firstPageLabel' => '<span class="fa fa-step-backward"></span>',
        'lastPageLabel' => '<span class="fa fa-step-forward"></span>',
        'prevPageLabel' => '<span class="fa fa-backward"></span>',
        'nextPageLabel' => '<span class="fa fa-forward"></span>',
    ];

}
