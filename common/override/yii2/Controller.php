<?php

namespace app\common\override\yii2;


use app\models\Basismodel;
use yii\web\NotFoundHttpException;

class Controller extends \yii\web\Controller
{
	public function actionAjaxdelete()
	{
		$model = $this->findModel(\Yii::$app->request->post("id"));
		if ($model->delete()) {
			$err = 0;
			$msg = "Datensatz erfolgreich gelöscht";
		} else {
			$err = 1;
			$msg = "Datensatz kann nicht gelöscht werden";
		}
		$errors = $model->errors;
		return $this->asJson(["err" => $err, "msg" => $msg, "errors" => $errors]);
	}

	/**
	 * Finds the Anschrift model based on its primary key value.
	 * If the model is not found, a 404 HTTP exception will be thrown.
	 *
	 * @param integer $id
	 *
	 * @return Basismodel the loaded model
	 * @throws NotFoundHttpException if the model cannot be found
	 */
	protected function findModel($id)
	{
		if (($model = Basismodel::findOne($id)) !== null) {
			return $model;
		}

		throw new NotFoundHttpException('The requested page does not exist.');
	}

}