<?php

namespace app\common\override\yii2;

use Yii;

class User extends \yii\web\User
{

	public function getIsAdmin()
	{
		return !Yii::$app->user->isGuest && Yii::$app->user->identity->isAdmin();
	}

	public function hasRole($role)
	{
		return !Yii::$app->user->isGuest && Yii::$app->user->identity->hatRolle($role);
	}

	public function hasRoleId($role_id)
	{
		return !Yii::$app->user->isGuest && in_array($role_id, Yii::$app->user->identity->rollenIds);
	}

	public function hatRolle($rolle)
	{
		return $this->hasRole($rolle);
	}

}