#!/bin/bash
set -e
test -f .env && source .env
docker compose down
docker compose up -d
docker exec ${CONTAINER_NAME:-mentoring} composer install
docker exec ${CONTAINER_NAME:-mentoring} php yii migrate --interactive=0
docker exec ${CONTAINER_NAME:-mentoring} mkdir -p runtime web/assets
docker exec ${CONTAINER_NAME:-mentoring} chown -R www-data:users runtime web/assets
docker exec ${CONTAINER_NAME:-mentoring} chmod -R g+w runtime web/assets
docker exec mentoring php yii cache/flush-all