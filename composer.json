{"name": "yiisoft/yii-basic-ndr", "description": "NDR Yii 2 Basic Project Template", "keywords": ["ndr"], "homepage": "http://www.ndr.de/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "minimum-stability": "stable", "require": {"php": ">=8.1", "yiisoft/yii2": "~2.0.14", "yiisoft/yii2-bootstrap4": "~2.0.0", "yiisoft/yii2-symfonymailer": "~2.0.0 || ~2.1.0", "phpoffice/phpspreadsheet": "*", "rmrevin/yii2-fontawesome": "3.2", "yiisoft/yii2-jui": "~2.0.0", "microinginer/yii2-dropdown-action-column": "*", "npm-asset/tinymce": "*", "npm-asset/bootstrap4-dialog": "*", "npm-asset/jquery-ui-dist": "*", "ext-ldap": "*", "ext-json": "*", "janisto/yii2-timepicker": "*", "kartik-v/yii2-grid": "dev-master", "aneeshikmat/yii2-time-down-counter": "v1.1.0-stable", "kartik-v/yii2-widget-switchinput": "dev-master"}, "require-dev": {"yiisoft/yii2-debug": "~2.1.0", "yiisoft/yii2-gii": "*", "yiisoft/yii2-faker": "~2.0.0", "codeception/codeception": "^4.0", "codeception/verify": "~0.5.0 || ~1.1.0", "codeception/specify": "~0.4.6", "symfony/browser-kit": ">=2.7 <=4.2.4", "codeception/module-filesystem": "^1.0.0", "codeception/module-yii2": "^1.0.0", "codeception/module-asserts": "^1.0.0"}, "config": {"process-timeout": 1800, "fxp-asset": {"enabled": false}, "allow-plugins": {"yiisoft/yii2-composer": true}}, "scripts": {"post-install-cmd": ["yii\\composer\\Installer::postInstall"], "post-create-project-cmd": ["yii\\composer\\Installer::postCreateProject", "yii\\composer\\Installer::postInstall"]}, "extra": {"yii\\composer\\Installer::postCreateProject": {"setPermission": [{"runtime": "0777", "web/assets": "0777", "yii": "0755"}]}, "yii\\composer\\Installer::postInstall": {"generateCookieValidationKey": ["config/web.php"]}}, "repositories": [{"type": "composer", "url": "https://asset-packagist.org"}]}