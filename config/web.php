<?php

function loadconfig($configfile)
{
    return file_exists(__DIR__ . '/' . $configfile . '_local.php') ? require(__DIR__ . '/' . $configfile . '_local.php') : require(__DIR__ . '/' . $configfile . '.php');
}

$config = [
    'id' => 'basis',
    'name' => 'Mentoringprogramm',
    'language' => 'de',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'defaultRoute' => "site/index",
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm' => '@vendor/npm-asset',
    ],
    'container' => [
        'definitions' => [
            \yii\widgets\LinkPager::class => \yii\bootstrap4\LinkPager::class,
        ],
    ],
    'components' => [
        'request' => [
            'cookieValidationKey' => 'einsecretkeyeinseasdfga asetrghwerth srthwsrt srthsrsdfcretkeyisdollisdollundso',
            'enableCsrfValidation' => true,
            'csrfCookie' => [
                'name' => '_backend',
                'httpOnly' => true,
                'secure' => true,
                'sameSite' => 'None'
            ]
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'config' => [
            'class' => 'app\common\components\ConfigComponent',
        ],
        'user' => [
            'class' => 'app\common\override\yii2\User',
            'identityClass' => 'app\models\Benutzer',
            'enableAutoLogin' => true,
            'enableSession' => true,
            'identityCookie' => [
                'name' => '_identity',
                'httpOnly' => true,
                'secure' => true,
                'sameSite' => 'None'
            ]

        ],
        'errorHandler' => [
            'errorAction' => 'site/error',
        ],
        'mailer' => loadconfig("mailer"),
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => YII_DEBUG ? ['error', 'warning', 'info', 'trace'] : ['error'],
                    'logVars' => [],
                ],
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => YII_DEBUG ? ['error', 'warning', 'info', 'trace'] : ['error'],
                    'categories' => ['ldap'],
                    'logFile' => '@app/runtime/logs/ldap.log',
                    'logVars' => [],
                ],
                [
                    'class' => 'yii\log\EmailTarget',
                    'mailer' => 'mailer',
                    'categories' => ['mailtarget'],
                    'message' => [
                        'from' => ['<EMAIL>'],
                        'to' => ['<EMAIL>'],
                        'subject' => 'Es ist ein Systemfehler aufgetreten',
                    ],
                ],
            ],
        ],
        'db' => loadconfig("db"),
        'ldap' => loadconfig("ldap"),
        'sql' => [
            'class' => 'app\common\components\SqlComponent',
        ],
        'urlManager' => [
            'class' => 'yii\web\UrlManager',
            // Hide index.php
            'showScriptName' => false,
            // Use pretty URLs
            'enablePrettyUrl' => true,
        ],
        'formatter' => [
            'class' => 'app\common\components\FormatComponent',
            'dateFormat' => 'dd.MM.yyyy',
            'decimalSeparator' => ',',
            'thousandSeparator' => '.',
            'currencyCode' => 'EUR',
            'nullDisplay' => '',
        ],
        'assetManager' => [
            'appendTimestamp' => true,
            'bundles' => ['kartik\form\ActiveFormAsset' => [
                'bsDependencyEnabled' => false // do not load bootstrap assets for a specific asset bundle
            ],
            ],
        ],
    ],
    'params' => loadconfig("params"),

    'as beforeRequest' => [
        'class' => \yii\filters\Cors::class,
        'cors' => [
            'Origin' => ['http://localhost:3000'],
            'Access-Control-Request-Method' => ['GET', 'POST', 'PUT', 'DELETE'],
            'Access-Control-Allow-Credentials' => true,
            'Access-Control-Max-Age' => 86400,
            'Access-Control-Allow-Headers' => ['Content-Type', 'Authorization', 'X-CSRF-Token'],
        ],
    ],
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        'allowedIPs' => ['127.0.0.1', '::1', '**********', '*'],
    ];
    
    $config['bootstrap'][] = 'gii';
//    $config['modules']['gii'] = require(__DIR__ . '/gii.php');
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
    $config['modules']['gii']['allowedIPs'] = ['*']; // adjust this to your needs
}

return $config;
