<?php

function loadconfig($configfile)
{
    return file_exists(__DIR__ . '/' . $configfile . '_local.php') ? require(__DIR__ . '/' . $configfile . '_local.php') : require(__DIR__ . '/' . $configfile . '.php');
}

$config = [
    'id' => 'basis-console',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'controllerNamespace' => 'app\commands',
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'components' => [
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'log' => [
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],
        'db' => loadconfig("db"),
        'sql' => [
            'class' => 'app\common\components\SqlComponent',
        ],
    ],
    'params' => loadconfig("params"),
    /*
    'controllerMap' => [
        'fixture' => [ // Fixture generation command line.
            'class' => 'yii\faker\FixtureController',
        ],
    ],
    */
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'gii';
	$config['modules']['gii'] = require(__DIR__ . '/gii.php');

}

return $config;
