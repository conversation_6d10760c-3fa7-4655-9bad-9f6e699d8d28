-- Insert statements for the 'profil' table
-- Required fields: benutzer_id, rolle, generation, geschlecht, standort, tätig<PERSON><PERSON>feld, führungskraft
-- Optional fields: personality traits and hobbies (can be NULL or 1-5)

-- Insert 1
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `t<PERSON><PERSON><PERSON><PERSON>feld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (101, '<PERSON>tor', '<PERSON><PERSON><PERSON> bis Jahrgang 1964', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>H', '<PERSON><PERSON><PERSON>zi<PERSON><PERSON>', 'Ja', 4, 5, 4, 5, 3, 4, 5, 4, 5, 3, 4, 3, 4, 5, 4, 3, 2, NULL, 4, '<PERSON><PERSON><PERSON>, <PERSON>en');

-- Insert 2
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (102, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Nein', 3, 4, 5, 3, 4, 5, 3, 4, 5, 4, 5, 5, 4, 5, 5, 4, 3, 5, NULL, 'Fotografie, Kochen');

-- Insert 3
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (103, 'Mentor', 'Generation X bis Jahrgang 1980', 'Divers', 'NDS', 'Produktionsspezifisch', 'Ja', 5, 5, 5, 4, 5, 4, 5, 4, 5, 5, 4, 4, 3, 5, 5, 5, 4, 3, 5, 'Gärtnern, DIY-Projekte');

-- Insert 4
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (104, 'Mentee', 'Generation Y bis Jahrgang 1995', 'keine Angabe', 'SH', 'Programmspezifisch', 'Nein', 4, 3, 4, 3, 4, 5, 3, 4, 3, 4, 5, 5, 5, 5, 4, 3, 2, 1, 3, 'Gaming, Podcasts');

-- Insert 5
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (105, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Ja', 5, 4, 5, 5, 4, 3, 5, 4, 5, 4, 3, 2, 4, 3, 5, 2, 5, 4, 5, 'Sprachen lernen, Reiseplanung');

-- Insert 6
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (106, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 3, 4, 3, 4, 3, 5, 3, 4, 4, 5, 5, 5, 5, 5, 3, 4, 2, NULL, 2, 'Videoproduktion, Social Media');

-- Insert 7
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (107, 'Mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Programmspezifisch', 'Ja', 4, 5, 5, 4, 5, 4, 5, 4, 5, 4, 4, 3, 3, 4, 4, 5, 3, 5, 4, 'Meditation, Wandern');

-- Insert 8
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (108, 'Mentee', 'Generation Y bis Jahrgang 1995', 'Divers', 'NDS', 'Verwaltungsspezifisch', 'Nein', 3, 4, 5, 3, 3, 5, 4, 3, 5, 3, 5, 4, 5, 5, 5, 3, 4, 3, 5, 'Bloggen, Podcasting');

-- Insert 9
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (109, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'SH', 'Produktionsspezifisch', 'Ja', 5, 5, 4, 5, 5, 3, 5, 4, 5, 5, 3, 4, 3, 4, 3, 5, 4, 2, 5, 'Holzarbeiten, Angeln');

-- Insert 10
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (110, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'Auslandsstudio', 'Programmspezifisch', 'Nein', 3, 4, 4, 3, 3, 5, 3, 4, 4, 4, 5, 3, 5, 5, 5, 3, 4, 5, 3, 'Digitale Kunst, Coding');

-- Insert 11
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (111, 'Mentor', 'Generation Y bis Jahrgang 1995', 'keine Angabe', 'HH', 'Verwaltungsspezifisch', 'Ja', 4, 4, 5, 4, 4, 4, 4, 5, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 4, 'Networking, Mentoring');

-- Insert 12
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (112, 'Mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'Produktionsspezifisch', 'Nein', 4, 3, 4, 5, 4, 5, 4, 3, 4, 5, 3, 5, 4, 3, 4, 5, 2, NULL, 3, 'Fotografie, Filmproduktion');

-- Insert 13
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (113, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'NDS', 'Programmspezifisch', 'Ja', 5, 5, 5, 5, 5, 4, 5, 4, 5, 4, 3, 3, 3, 4, 4, 3, 5, 4, 5, 'Buchclub, Gartenarbeit');

-- Insert 14
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (114, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Divers', 'SH', 'Verwaltungsspezifisch', 'Nein', 3, 4, 5, 3, 3, 5, 3, 4, 5, 3, 5, 4, 5, 5, 4, 3, 4, 5, 3, 'Streaming, Social Media Management');

-- Insert 15
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (115, 'Mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'Auslandsstudio', 'Produktionsspezifisch', 'Ja', 4, 5, 4, 5, 4, 4, 5, 5, 4, 5, 4, 5, 3, 4, 5, 5, 3, 2, 4, 'Internationale Beziehungen, Sprachen');

-- Insert 16
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (116, 'Mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'HH', 'Programmspezifisch', 'Nein', 4, 4, 5, 3, 4, 5, 3, 4, 4, 4, 5, 4, 4, 5, 4, 3, 4, 5, 3, 'App-Entwicklung, UX Design');

-- Insert 17
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (117, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'keine Angabe', 'MV', 'Verwaltungsspezifisch', 'Ja', 5, 5, 4, 5, 5, 3, 5, 4, 5, 4, 3, 2, 3, 3, 3, 2, 4, 3, 5, 'Organisationsentwicklung, Coaching');

-- Insert 18
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (118, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'NDS', 'Produktionsspezifisch', 'Nein', 3, 3, 4, 3, 3, 5, 3, 3, 4, 5, 5, 5, 5, 5, 4, 5, 2, 1, 2, 'Videoschnitt, Sounddesign');

-- Insert 19
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (119, 'Mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'SH', 'Programmspezifisch', 'Ja', 4, 5, 5, 4, 4, 5, 4, 5, 5, 4, 5, 4, 3, 4, 4, 4, 3, 5, 4, 'Technologietrends, Nachhaltigkeit');

-- Insert 20
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (120, 'Mentee', 'Generation X bis Jahrgang 1980', 'Divers', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Nein', 4, 4, 5, 4, 4, 4, 4, 4, 5, 3, 4, 3, 4, 4, 5, 3, 5, 4, 5, 'Interkulturelle Kommunikation, Projektmanagement');

-- Insert 21
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (121, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'HH', 'Produktionsspezifisch', 'Ja', 5, 4, 4, 5, 5, 4, 5, 4, 4, 5, 3, 4, 3, 4, 3, 4, 3, NULL, 4, 'Dokumentarfilme, Journalismus');

-- Insert 22
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (122, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'MV', 'Programmspezifisch', 'Nein', 3, 4, 4, 3, 3, 5, 3, 4, 4, 4, 5, 4, 5, 5, 4, 3, 3, 4, 3, 'Webdesign, Content Creation');

-- Insert 23
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (123, 'Mentor', 'Generation X bis Jahrgang 1980', 'keine Angabe', 'NDS', 'Verwaltungsspezifisch', 'Ja', 4, 5, 4, 5, 4, 3, 5, 4, 5, 4, 3, 3, 3, 3, 4, 3, 4, 3, 5, 'Strategische Planung, Change Management');

-- Insert 24
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (124, 'Mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'SH', 'Produktionsspezifisch', 'Nein', 4, 3, 4, 4, 4, 5, 4, 3, 4, 5, 4, 5, 5, 4, 3, 5, 2, 2, 3, 'Audio Engineering, Kameraarbeit');

-- Insert 25
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, `weitere_hobbys`)
VALUES (125, 'Mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'Auslandsstudio', 'Programmspezifisch', 'Ja', 4, 5, 5, 4, 4, 5, 4, 5, 5, 4, 5, 4, 4, 5, 5, 4, 4, 5, 4, 'Digitale Transformation, Internationale Medien');