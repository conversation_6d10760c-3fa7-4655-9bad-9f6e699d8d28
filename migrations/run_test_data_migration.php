<?php
/**
 * Test Data Migration Script
 * 
 * This script executes the test data SQL file to populate the database
 * with 50 test entries for profil, kompetenzen, and tandempartner tables.
 * 
 * Usage:
 * 1. Run from command line: php run_test_data_migration.php
 * 2. Or include in a YII2 console command
 */

// Include YII2 framework if running as standalone script
if (!defined('YII_BEGIN_TIME')) {
    require_once(__DIR__ . '/../vendor/autoload.php');
    require_once(__DIR__ . '/../vendor/yiisoft/yii2/Yii.php');
    require_once(__DIR__ . '/../config/console.php');
    
    $application = new yii\console\Application($config);
}

try {
    echo "Starting test data migration...\n";
    
    // Read the SQL file
    $sqlFile = __DIR__ . '/insert_test_data.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL file not found: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("Could not read SQL file: $sqlFile");
    }
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $db = Yii::$app->db;
    $transaction = $db->beginTransaction();
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        try {
            $db->createCommand($statement)->execute();
            $successCount++;
            
            // Show progress for major operations
            if (strpos($statement, 'INSERT INTO `benutzer`') !== false) {
                echo "✓ Created test users in benutzer table\n";
            } elseif (strpos($statement, 'INSERT INTO `profil`') !== false) {
                echo "✓ Created test entries in profil table\n";
            } elseif (strpos($statement, 'INSERT INTO `kompetenzen`') !== false) {
                echo "✓ Created test entries in kompetenzen table\n";
            } elseif (strpos($statement, 'INSERT INTO `tandempartner`') !== false) {
                echo "✓ Created test entries in tandempartner table\n";
            }
            
        } catch (Exception $e) {
            $errorCount++;
            echo "⚠ Warning: " . $e->getMessage() . "\n";
            // Continue with other statements even if one fails
        }
    }
    
    $transaction->commit();
    
    echo "\n=== Migration Summary ===\n";
    echo "Successful statements: $successCount\n";
    echo "Failed statements: $errorCount\n";
    
    if ($errorCount === 0) {
        echo "✅ Test data migration completed successfully!\n";
    } else {
        echo "⚠ Test data migration completed with some warnings.\n";
    }
    
    // Verify the data was inserted
    echo "\n=== Data Verification ===\n";
    
    $benutzerCount = $db->createCommand("SELECT COUNT(*) FROM benutzer WHERE id BETWEEN 101 AND 150")->queryScalar();
    echo "Test users created: $benutzerCount/50\n";
    
    $profilCount = $db->createCommand("SELECT COUNT(*) FROM profil WHERE benutzer_id BETWEEN 101 AND 150")->queryScalar();
    echo "Profile entries created: $profilCount/50\n";
    
    $kompetenzenCount = $db->createCommand("SELECT COUNT(*) FROM kompetenzen WHERE benutzer_id BETWEEN 101 AND 150")->queryScalar();
    echo "Competency entries created: $kompetenzenCount/50\n";
    
    $tandempartnerCount = $db->createCommand("SELECT COUNT(*) FROM tandempartner WHERE benutzer_id BETWEEN 101 AND 150")->queryScalar();
    echo "Tandempartner entries created: $tandempartnerCount/50\n";
    
    echo "\n🎉 Test data is ready for use!\n";
    echo "\nTest user IDs: 101-150\n";
    echo "Test user emails: <EMAIL>\n";
    echo "Example: <EMAIL>, <EMAIL>, etc.\n";
    
} catch (Exception $e) {
    if (isset($transaction)) {
        $transaction->rollBack();
    }
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Migration failed and was rolled back.\n";
    exit(1);
}
?>
