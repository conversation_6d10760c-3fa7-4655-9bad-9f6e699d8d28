--
-- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> für Tabelle `Benutzer`
--
DROP TABLE IF EXISTS benutzer;
CREATE TABLE `benutzer`
(
    `id`               int(11)      NOT NULL,
    `benutzername`     varchar(255) NULL,
    `domaene_id`       int(11)           DEFAULT 0,
    `nachname`         varchar(255) NULL,
    `vorname`          varchar(255)      DEFAULT NULL,
    `email`            varchar(255)      DEFAULT NULL,
    `passwort`         varchar(255)      DEFAULT NULL,
    `email_alternativ` VARCHAR(255) NULL DEFAULT NULL,
    `employeeid`       VARCHAR(255) NULL DEFAULT NULL,
    `oid`           int(11)  NULL

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `benutzer`
    ADD PRIMARY KEY (`id`);

ALTER TABLE `benutzer`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

INSERT INTO `benutzer` (benutz<PERSON><PERSON>, domaene_id, passwort, vorname, nachname, email)
VALUES ('admin', 0, '$2y$13$YchLbPFsfAiOCvdhc8aeUehC02bC640QUuYYtJLnzOdEKNdGejyv.', 'Admin', 'Administrator',
        '<EMAIL>');

INSERT INTO `benutzer` (`id`, `benutzername`, `domaene_id`, `nachname`, `vorname`, `email`, `passwort`, `email_alternativ`, `employeeid`, `oid`) VALUES
    (3, 'riessd', 1, 'Rieß', 'David', '<EMAIL>', '$2y$13$jHUy86idD0heCK00T25eoepq.ijuF9EnTUFyZPHdxWQ/sUKm0M9GO', '', '53276', NULL);
INSERT INTO `benutzer` (`id`, `benutzername`, `domaene_id`, `nachname`, `vorname`, `email`, `passwort`, `email_alternativ`, `employeeid`, `oid`) VALUES ('2', 'koehrmanna', '0', 'Koehrmann', 'Arzu', '<EMAIL>', NULL, NULL, NULL, NULL),
                                                                                                                                                                                                                  ('4', 'schusterb', '0', 'Schuster', 'Björn', '<EMAIL>', NULL, NULL, NULL, NULL),
                                                                                                                                                                                                                  ('5', 'bansenf', '0', 'Bansen', 'Felipe', '<EMAIL>', NULL, NULL, NULL, NULL);
--
-- Tabellenstruktur für Tabelle `Domaene`
--
DROP TABLE IF EXISTS domaene;
CREATE TABLE `domaene`
(
    `id`      int(11)      NOT NULL,
    `domaene` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `domaene`
    ADD PRIMARY KEY (`id`);

ALTER TABLE `domaene`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

INSERT INTO domaene (domaene)
VALUES ('HH'),
       ('NI'),
       ('MV'),
       ('SH');

DROP TABLE IF EXISTS kompetenzen;
CREATE TABLE `kompetenzen` (
                               `id` int(11) NOT NULL AUTO_INCREMENT KEY,
                               `rolle` varchar(12) NOT NULL ,
                               `benutzer_id` INT(11) NOT NULL,
                               `internes_netzwerk` tinyint(1) ,
                               `ki` tinyint(1) ,
                               `ndr_knowhow` tinyint(1) ,
                               `externe_erfahrung` tinyint(1) ,
                               `digitalisierung` tinyint(1) ,
                               `externes_netzwerk` tinyint(1) ,
                               `socialmedia` tinyint(1) ,
                               `moderation` tinyint(1) ,
                               `worklifebalance` tinyint(1) ,
                               `präsentationen` tinyint(1) ,
                               `resilienz` tinyint(1) ,
                               `gender_diversity` tinyint(1)

    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `kompetenzen`
    ADD UNIQUE KEY `benutzer_rolle` (`benutzer_id`, rolle);
-- Kreuztabelle Benutzer <--> Rollen
--
DROP TABLE IF EXISTS `rolle_benutzer`;
CREATE TABLE `rolle_benutzer`
(
    `id`      INT(11) NOT NULL AUTO_INCREMENT,
    `rolle_id` INT(11) NOT NULL ,
    `benutzer_id` INT(11) NOT NULL ,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


INSERT INTO `rolle_benutzer` (rolle_id, benutzer_id)
VALUES (1, 1);
# INSERT INTO `rolle_benutzer` (rolle_id, benutzer_id)
# VALUES (1, 3);
# INSERT INTO `rolle_benutzer` (rolle_id, benutzer_id)
# VALUES (2, 3);
# INSERT INTO `rolle_benutzer` (rolle_id, benutzer_id)
# VALUES (3, 3);
# INSERT INTO `rolle_benutzer` (rolle_id, benutzer_id)
# VALUES (4, 3);
# INSERT INTO `rolle_benutzer` (rolle_id, benutzer_id)
# VALUES (5, 3);
-- -------
--
-- Rollentabelle
--
DROP TABLE IF EXISTS `rolle`;
CREATE TABLE `rolle`
(
    `id`               INT(11)      NOT NULL AUTO_INCREMENT,
    `rolle`        VARCHAR(255) NOT NULL ,
    `beschreibung` TEXT         NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

INSERT INTO `rolle` (`rolle`, `beschreibung`)
VALUES ('Admin', 'Admin Rolle (Entwickler)');

INSERT INTO `rolle` (`rolle`, `beschreibung`)
VALUES ('Benutzeradmin', 'Fachbereich Administrator, kann Benutzer sehen und bearbeiten');
INSERT INTO `rolle` (`rolle`, `beschreibung`)
VALUES ('Mentee', 'Mentee, Anwendungs Benutzer, der sich als Mentee anmeldet');
INSERT INTO `rolle` (`rolle`, `beschreibung`)
VALUES ('Mentor', 'Mentor, Anwendungs Benutzer, der sich als Mentor anmeldet');
INSERT INTO `rolle` (`rolle`, `beschreibung`)
VALUES ('MenteeMentor', 'MenteeMentor, Anwendungs Benutzer, der sich als Mentee und Mentor anmeldet');

--
-- Konfigurationstabelle
--
DROP TABLE IF EXISTS `konfiguration`;
CREATE TABLE `konfiguration`
(
    `id`           INT(11)       NOT NULL AUTO_INCREMENT,
    `name`         VARCHAR(255)  NULL DEFAULT NULL,
    `wert`         VARCHAR(1024) NULL DEFAULT NULL,
    `beschreibung` VARCHAR(1024) NULL DEFAULT NULL,
    `deprecated`   TINYINT(4)    NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Labelmanager
--
DROP TABLE IF EXISTS `labelmanager`;
CREATE TABLE `labelmanager`
(
    `id`         INT(11)      NOT NULL AUTO_INCREMENT,
    `liste`      VARCHAR(255) NULL DEFAULT NULL,
    `value`      int(11)  NULL DEFAULT NULL,
    `label`      VARCHAR(255) NULL DEFAULT NULL,
    `sortorder`  int(11)      NULL DEFAULT 0,
    `comment`    TEXT         NULL,
    `deprecated` TINYINT(4)   NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Table structure for table titles
--
DROP TABLE IF EXISTS `titles`;
CREATE TABLE titles (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `elementid` varchar(255) DEFAULT NULL,
    `title` text DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_german2_ci;
