-- Test data generation for profil, kompetenzen, and tandempartner tables
-- This script generates 50 test entries for each table

-- First, let's insert test users into benutzer table if they don't exist
INSERT IGNORE INTO `benutzer` (`id`, `vorname`, `nachname`, `email`, `benutzername`) VALUES
(101, '<PERSON>', '<PERSON>', '<EMAIL>', 'aschmi<PERSON>'),
(102, '<PERSON>', '<PERSON>', 'micha<PERSON>.<EMAIL>', 'mweber'),
(103, '<PERSON>', '<PERSON>', '<EMAIL>', 'sfischer'),
(104, '<PERSON>', '<PERSON>', '<EMAIL>', 'twagner'),
(105, '<PERSON>', '<PERSON>', '<EMAIL>', 'jbecker'),
(106, '<PERSON>', '<PERSON><PERSON><PERSON>', '<EMAIL>', 'dschulz'),
(107, '<PERSON>', '<PERSON>', 'l<PERSON><PERSON><PERSON><PERSON><PERSON>@ndr.de', '<PERSON><PERSON><PERSON>'),
(108, '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'mark<PERSON>.s<PERSON><PERSON><PERSON>@ndr.de', 'm<PERSON><PERSON><PERSON>'),
(109, '<PERSON>', '<PERSON>', '<EMAIL>', 'p<PERSON>ch'),
(110, '<PERSON>', '<PERSON>', 'stefan.rich<PERSON>@ndr.de', 's<PERSON><PERSON>'),
(111, '<PERSON>', '<PERSON>', 'nicole.k<PERSON>@ndr.de', 'n<PERSON>in'),
(112, '<PERSON>', '<PERSON>', '<EMAIL>', 'a<PERSON>'),
(113, '<PERSON>', '<PERSON>hr<PERSON><PERSON>', 'sa<PERSON>.<EMAIL>', 'sschroeder'),
(114, 'Christian', 'Neumann', '<EMAIL>', 'cneumann'),
(115, 'Martina', 'Schwarz', '<EMAIL>', 'mschwarz'),
(116, 'Jörg', 'Zimmermann', '<EMAIL>', 'jzimmermann'),
(117, 'Claudia', 'Braun', '<EMAIL>', 'cbraun'),
(118, 'Ralf', 'Krüger', '<EMAIL>', 'rkrueger'),
(119, 'Susanne', 'Hartmann', '<EMAIL>', 'shartmann'),
(120, 'Oliver', 'Lange', '<EMAIL>', 'olange'),
(121, 'Birgit', 'Schmitt', '<EMAIL>', 'bschmitt'),
(122, 'Frank', 'Werner', '<EMAIL>', 'fwerner'),
(123, 'Karin', 'Krause', '<EMAIL>', 'kkrause'),
(124, 'Uwe', 'Meier', '<EMAIL>', 'umeier'),
(125, 'Monika', 'Lehmann', '<EMAIL>', 'mlehmann'),
(126, 'Bernd', 'Müller', '<EMAIL>', 'bmueller'),
(127, 'Anja', 'Weiß', '<EMAIL>', 'aweiss'),
(128, 'Holger', 'Herrmann', '<EMAIL>', 'hherrmann'),
(129, 'Silke', 'König', '<EMAIL>', 'skoenig'),
(130, 'Dirk', 'Walter', '<EMAIL>', 'dwalter'),
(131, 'Gabi', 'Peters', '<EMAIL>', 'gpeters'),
(132, 'Torsten', 'Fuchs', '<EMAIL>', 'tfuchs'),
(133, 'Heike', 'Vogel', '<EMAIL>', 'hvogel'),
(134, 'Matthias', 'Keller', '<EMAIL>', 'mkeller'),
(135, 'Doris', 'Günther', '<EMAIL>', 'dguenther'),
(136, 'Carsten', 'Frank', '<EMAIL>', 'cfrank'),
(137, 'Ingrid', 'Möller', '<EMAIL>', 'imoeller'),
(138, 'Klaus', 'Berger', '<EMAIL>', 'kberger'),
(139, 'Renate', 'Huber', '<EMAIL>', 'rhuber'),
(140, 'Jürgen', 'Graf', '<EMAIL>', 'jgraf'),
(141, 'Christa', 'Schulze', '<EMAIL>', 'cschulze'),
(142, 'Wolfgang', 'Bauer', '<EMAIL>', 'wbauer'),
(143, 'Ursula', 'Dietrich', '<EMAIL>', 'udietrich'),
(144, 'Rainer', 'Maier', '<EMAIL>', 'rmaier'),
(145, 'Gabriele', 'Scholz', '<EMAIL>', 'gscholz'),
(146, 'Manfred', 'Gross', '<EMAIL>', 'mgross'),
(147, 'Helga', 'Roth', '<EMAIL>', 'hroth'),
(148, 'Dieter', 'Jung', '<EMAIL>', 'djung'),
(149, 'Elfriede', 'Hahn', '<EMAIL>', 'ehahn'),
(150, 'Günter', 'Lorenz', '<EMAIL>', 'glorenz');

-- Insert test data into profil table
INSERT INTO `profil` (`rolle`, `benutzer_id`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, 
                      `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, 
                      `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, 
                      `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, 
                      `weitere_hobbys`, `anzeigbar`) VALUES
('Mentor', 101, 'Generation X', 'weiblich', 'Hamburg', 'Redaktion', 'Ja', 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 'Lesen, Kochen', 1),
('Mentee', 102, 'Generation Y', 'männlich', 'Hannover', 'Technik', 'Nein', 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 'Gaming, Programmieren', 1),
('Mentor', 103, 'Generation Y', 'weiblich', 'Kiel', 'Marketing', 'Ja', 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 'Fotografie, Reisen', 1),
('Mentee', 104, 'Generation Z', 'männlich', 'Schwerin', 'Produktion', 'Nein', 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 'Fußball, Musik', 1),
('Mentor', 105, 'Generation X', 'weiblich', 'Hamburg', 'Verwaltung', 'Ja', 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Gartenarbeit, Lesen', 1),
('Mentee', 106, 'Generation Y', 'männlich', 'Hannover', 'IT', 'Nein', 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 1, 'Technologie, Innovation', 1),
('Mentor', 107, 'Generation X', 'weiblich', 'Kiel', 'Redaktion', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 'Schreiben, Theater', 1),
('Mentee', 108, 'Generation Z', 'männlich', 'Hamburg', 'Marketing', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 'Social Media, Design', 1),
('Mentor', 109, 'Baby Boomer', 'weiblich', 'Schwerin', 'Verwaltung', 'Ja', 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 'Handwerk, Familie', 1),
('Mentee', 110, 'Generation Y', 'männlich', 'Hannover', 'Technik', 'Nein', 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 'Elektronik, Basteln', 1),
('Mentor', 111, 'Generation X', 'weiblich', 'Kiel', 'Produktion', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 'Nähen, Backen', 1),
('Mentee', 112, 'Generation Z', 'männlich', 'Hamburg', 'IT', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Coding, Kryptowährung', 1),
('Mentor', 113, 'Baby Boomer', 'weiblich', 'Schwerin', 'Redaktion', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Journalismus, Politik', 1),
('Mentee', 114, 'Generation Y', 'männlich', 'Hannover', 'Marketing', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 'Werbung, Kreativität', 1),
('Mentor', 115, 'Generation X', 'weiblich', 'Hamburg', 'Verwaltung', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 'Organisation, Planung', 1),
('Mentee', 116, 'Generation Z', 'männlich', 'Kiel', 'Technik', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 'Drohnen, Robotik', 1),
('Mentor', 117, 'Generation Y', 'weiblich', 'Schwerin', 'Produktion', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Qualitätskontrolle', 1),
('Mentee', 118, 'Generation Y', 'männlich', 'Hamburg', 'Redaktion', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 'Podcasting, Audio', 1),
('Mentor', 119, 'Generation X', 'weiblich', 'Hannover', 'Marketing', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 'Eventmanagement', 1),
('Mentee', 120, 'Generation Z', 'männlich', 'Kiel', 'IT', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 'Machine Learning, KI', 1),
('Mentor', 121, 'Baby Boomer', 'weiblich', 'Schwerin', 'Verwaltung', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 'Buchhaltung, Finanzen', 1),
('Mentee', 122, 'Generation Y', 'männlich', 'Hamburg', 'Technik', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 'Broadcast Technology', 1),
('Mentor', 123, 'Generation X', 'weiblich', 'Hannover', 'Produktion', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Projektmanagement', 1),
('Mentee', 124, 'Generation Z', 'männlich', 'Kiel', 'Marketing', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Influencer Marketing', 1),
('Mentor', 125, 'Generation Y', 'weiblich', 'Schwerin', 'Redaktion', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Investigativer Journalismus', 1),
('Mentee', 126, 'Generation Y', 'männlich', 'Hamburg', 'Verwaltung', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Digitalisierung, Prozesse', 1),
('Mentor', 127, 'Generation X', 'weiblich', 'Hannover', 'IT', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 'Cybersecurity, Datenschutz', 1),
('Mentee', 128, 'Generation Z', 'männlich', 'Kiel', 'Technik', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 'Streaming Technology', 1),
('Mentor', 129, 'Baby Boomer', 'weiblich', 'Schwerin', 'Produktion', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Qualitätsmanagement', 1),
('Mentee', 130, 'Generation Y', 'männlich', 'Hamburg', 'Marketing', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Content Creation', 1),
('Mentor', 131, 'Generation X', 'weiblich', 'Hannover', 'Redaktion', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Dokumentarfilm', 1),
('Mentee', 132, 'Generation Z', 'männlich', 'Kiel', 'Verwaltung', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Agile Methoden', 1),
('Mentor', 133, 'Generation Y', 'weiblich', 'Schwerin', 'IT', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 'Software Development', 1),
('Mentee', 134, 'Generation Y', 'männlich', 'Hamburg', 'Technik', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 'Audio Engineering', 1),
('Mentor', 135, 'Generation X', 'weiblich', 'Hannover', 'Produktion', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Live-Produktion', 1),
('Mentee', 136, 'Generation Z', 'männlich', 'Kiel', 'Marketing', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Digital Marketing', 1),
('Mentor', 137, 'Baby Boomer', 'weiblich', 'Schwerin', 'Redaktion', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Politikjournalismus', 1),
('Mentee', 138, 'Generation Y', 'männlich', 'Hamburg', 'Verwaltung', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Change Management', 1),
('Mentor', 139, 'Generation X', 'weiblich', 'Hannover', 'IT', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 'Database Management', 1),
('Mentee', 140, 'Generation Z', 'männlich', 'Kiel', 'Technik', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 'Virtual Reality', 1),
('Mentor', 141, 'Generation Y', 'weiblich', 'Schwerin', 'Produktion', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Post-Production', 1),
('Mentee', 142, 'Generation Y', 'männlich', 'Hamburg', 'Marketing', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Brand Management', 1),
('Mentor', 143, 'Generation X', 'weiblich', 'Hannover', 'Redaktion', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Wissenschaftsjournalismus', 1),
('Mentee', 144, 'Generation Z', 'männlich', 'Kiel', 'Verwaltung', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Lean Management', 1),
('Mentor', 145, 'Baby Boomer', 'weiblich', 'Schwerin', 'IT', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 'Legacy Systems', 1),
('Mentee', 146, 'Generation Y', 'männlich', 'Hamburg', 'Technik', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 'Cloud Computing', 1),
('Mentor', 147, 'Generation X', 'weiblich', 'Hannover', 'Produktion', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Content Strategy', 1),
('Mentee', 148, 'Generation Z', 'männlich', 'Kiel', 'Marketing', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Performance Marketing', 1),
('Mentor', 149, 'Generation Y', 'weiblich', 'Schwerin', 'Redaktion', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Datenjournalismus', 1),
('Mentee', 150, 'Generation Y', 'männlich', 'Hamburg', 'Verwaltung', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Digital Transformation', 1);

-- Insert test data into kompetenzen table
INSERT INTO `kompetenzen` (`rolle`, `benutzer_id`, `internes_netzwerk`, `ki`, `ndr_knowhow`, `externe_erfahrung`,
                           `digitalisierung`, `externes_netzwerk`, `socialmedia`, `moderation`, `worklifebalance`,
                           `präsentationen`, `resilienz`, `gender_diversity`) VALUES
('Mentor', 101, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1),
('Mentee', 102, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0),
('Mentor', 103, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1),
('Mentee', 104, 0, 1, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1),
('Mentor', 105, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0),
('Mentee', 106, 0, 1, 0, 1, 1, 0, 1, 0, 0, 0, 1, 1),
('Mentor', 107, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1),
('Mentee', 108, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 0),
('Mentor', 109, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1),
('Mentee', 110, 0, 1, 0, 1, 1, 0, 0, 0, 0, 1, 0, 1),
('Mentor', 111, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0),
('Mentee', 112, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1),
('Mentor', 113, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1),
('Mentee', 114, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0),
('Mentor', 115, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1),
('Mentee', 116, 0, 1, 0, 1, 1, 0, 1, 0, 0, 0, 1, 0),
('Mentor', 117, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1),
('Mentee', 118, 0, 1, 0, 0, 1, 0, 1, 1, 1, 1, 1, 0),
('Mentor', 119, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1),
('Mentee', 120, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 0, 1),
('Mentor', 121, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0),
('Mentee', 122, 0, 1, 0, 0, 1, 0, 1, 0, 0, 1, 1, 1),
('Mentor', 123, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1),
('Mentee', 124, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 0),
('Mentor', 125, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1),
('Mentee', 126, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 1),
('Mentor', 127, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0),
('Mentee', 128, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1),
('Mentor', 129, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1),
('Mentee', 130, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0),
('Mentor', 131, 1, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1),
('Mentee', 132, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1),
('Mentor', 133, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0, 0),
('Mentee', 134, 0, 1, 0, 0, 1, 0, 1, 0, 0, 1, 1, 1),
('Mentor', 135, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0),
('Mentee', 136, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 0, 1),
('Mentor', 137, 1, 0, 1, 0, 0, 1, 1, 1, 1, 1, 1, 1),
('Mentee', 138, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 1, 0),
('Mentor', 139, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1),
('Mentee', 140, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 1),
('Mentor', 141, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0),
('Mentee', 142, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 1),
('Mentor', 143, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1),
('Mentee', 144, 0, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 0),
('Mentor', 145, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1),
('Mentee', 146, 0, 1, 0, 0, 1, 0, 1, 0, 0, 1, 0, 1),
('Mentor', 147, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0),
('Mentee', 148, 0, 1, 0, 0, 1, 1, 1, 0, 1, 0, 1, 1),
('Mentor', 149, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 1, 1),
('Mentee', 150, 0, 1, 0, 1, 1, 0, 0, 0, 1, 1, 0, 0);

-- Insert test data into tandempartner table
INSERT INTO `tandempartner` (`rolle`, `benutzer_id`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`,
                             `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`,
                             `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`,
                             `treffen_präsenz`, `treffen_online`, `treffen_häufigkeit`) VALUES
('Mentor', 101, 'Generation Y', 'egal', 'Hamburg', 'egal', 'Nein', 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 'wöchentlich'),
('Mentee', 102, 'Generation X', 'egal', 'egal', 'Redaktion', 'Ja', 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 'zweiwöchentlich'),
('Mentor', 103, 'Generation Z', 'männlich', 'egal', 'egal', 'egal', 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 'monatlich'),
('Mentee', 104, 'Generation X', 'weiblich', 'Hamburg', 'egal', 'Ja', 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 'wöchentlich'),
('Mentor', 105, 'Generation Y', 'egal', 'egal', 'Technik', 'Nein', 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
('Mentee', 106, 'Generation X', 'weiblich', 'Hannover', 'egal', 'egal', 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 'monatlich'),
('Mentor', 107, 'Generation Z', 'egal', 'Kiel', 'Marketing', 'egal', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
('Mentee', 108, 'Generation X', 'männlich', 'egal', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
('Mentor', 109, 'Generation Y', 'weiblich', 'egal', 'egal', 'Nein', 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
('Mentee', 110, 'Generation X', 'egal', 'Schwerin', 'Redaktion', 'egal', 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 'wöchentlich'),
('Mentor', 111, 'Generation Z', 'männlich', 'egal', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 'zweiwöchentlich'),
('Mentee', 112, 'Generation X', 'weiblich', 'Hamburg', 'Technik', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 'monatlich'),
('Mentor', 113, 'Generation Y', 'egal', 'egal', 'egal', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
('Mentee', 114, 'Generation X', 'männlich', 'Hannover', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
('Mentor', 115, 'Generation Z', 'weiblich', 'egal', 'Verwaltung', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
('Mentee', 116, 'Generation X', 'egal', 'Kiel', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
('Mentor', 117, 'Generation Y', 'männlich', 'egal', 'egal', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
('Mentee', 118, 'Generation X', 'weiblich', 'Schwerin', 'Produktion', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
('Mentor', 119, 'Generation Z', 'egal', 'egal', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 'wöchentlich'),
('Mentee', 120, 'Generation X', 'männlich', 'Hamburg', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
('Mentor', 121, 'Generation Y', 'weiblich', 'egal', 'IT', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
('Mentee', 122, 'Generation X', 'egal', 'Hannover', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
('Mentor', 123, 'Generation Z', 'männlich', 'egal', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
('Mentee', 124, 'Generation X', 'weiblich', 'Kiel', 'Marketing', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
('Mentor', 125, 'Generation Y', 'egal', 'egal', 'egal', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 'wöchentlich'),
('Mentee', 126, 'Generation X', 'männlich', 'Schwerin', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
('Mentor', 127, 'Generation Z', 'weiblich', 'egal', 'Verwaltung', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 'monatlich'),
('Mentee', 128, 'Generation X', 'egal', 'Hamburg', 'egal', 'egal', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
('Mentor', 129, 'Generation Y', 'männlich', 'egal', 'egal', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 'zweiwöchentlich'),
('Mentee', 130, 'Generation X', 'weiblich', 'Hannover', 'Technik', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
('Mentor', 131, 'Generation Z', 'egal', 'egal', 'egal', 'egal', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 'wöchentlich'),
('Mentee', 132, 'Generation X', 'männlich', 'Kiel', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
('Mentor', 133, 'Generation Y', 'weiblich', 'egal', 'Produktion', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
('Mentee', 134, 'Generation X', 'egal', 'Schwerin', 'egal', 'egal', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
('Mentor', 135, 'Generation Z', 'männlich', 'egal', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 'zweiwöchentlich'),
('Mentee', 136, 'Generation X', 'weiblich', 'Hamburg', 'IT', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
('Mentor', 137, 'Generation Y', 'egal', 'egal', 'egal', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 'wöchentlich'),
('Mentee', 138, 'Generation X', 'männlich', 'Hannover', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
('Mentor', 139, 'Generation Z', 'weiblich', 'egal', 'Marketing', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
('Mentee', 140, 'Generation X', 'egal', 'Kiel', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 'wöchentlich'),
('Mentor', 141, 'Generation Y', 'männlich', 'egal', 'egal', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
('Mentee', 142, 'Generation X', 'weiblich', 'Schwerin', 'Verwaltung', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
('Mentor', 143, 'Generation Z', 'egal', 'egal', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
('Mentee', 144, 'Generation X', 'männlich', 'Hamburg', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
('Mentor', 145, 'Generation Y', 'weiblich', 'egal', 'Technik', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
('Mentee', 146, 'Generation X', 'egal', 'Hannover', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
('Mentor', 147, 'Generation Z', 'männlich', 'egal', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
('Mentee', 148, 'Generation X', 'weiblich', 'Kiel', 'Produktion', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
('Mentor', 149, 'Generation Y', 'egal', 'egal', 'egal', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 'wöchentlich'),
('Mentee', 150, 'Generation X', 'männlich', 'Schwerin', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich');

-- Summary of test data created:
-- 50 test users (IDs 101-150) in benutzer table
-- 50 profile entries in profil table (25 Mentors, 25 Mentees)
-- 50 competency entries in kompetenzen table
-- 50 tandempartner preference entries in tandempartner table
--
-- Data includes realistic variations in:
-- - Generations (Baby Boomer, Generation X, Generation Y, Generation Z)
-- - Locations (Hamburg, Hannover, Kiel, Schwerin)
-- - Departments (Redaktion, Technik, Marketing, Produktion, Verwaltung, IT)
-- - Leadership roles (Ja/Nein/egal)
-- - Various competencies and preferences
-- - Meeting preferences (wöchentlich, zweiwöchentlich, monatlich)
--
-- To execute this script:
-- 1. Connect to your MySQL database
-- 2. Run: SOURCE /path/to/insert_test_data.sql;
-- 3. Or copy and paste the content into your MySQL client
--
-- Note: This script uses INSERT IGNORE for benutzer table to avoid conflicts
-- if some test users already exist.
