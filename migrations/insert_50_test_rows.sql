-- SQL statements to insert 50 rows into profil and tandempartner tables
-- This script generates realistic test data with correct field values

-- Insert 50 rows into profil table
INSERT INTO `profil` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätig<PERSON><PERSON>feld`, `führungskraft`, 
                      `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, 
                      `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, 
                      `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, 
                      `weitere_hobbys`, `anzeigbar`) VALUES
(1, 'mentor', 'Generation X bis Jahrgang 1980', '<PERSON><PERSON><PERSON>', 'HH', 'Programmspezifisch', 'Ja', 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, '<PERSON><PERSON>, <PERSON><PERSON>', 1),
(2, 'mentee', 'Generation Y bis Jahrgang 1995', '<PERSON><PERSON><PERSON><PERSON>', 'HH', '<PERSON>duktionsspezif<PERSON>', 'Nein', 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, '<PERSON>, <PERSON>mieren', 1),
(3, 'mentor', '<PERSON> Y bis Jahrgang 1995', '<PERSON>b<PERSON>', 'MV', 'Verwaltungsspezif<PERSON>', 'Ja', 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 'Fotografie, Reisen', 1),
(4, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Produktionsspezifisch', 'Nein', 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 'Fußball, Musik', 1),
(5, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Verwaltungsspezifisch', 'Ja', 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Gartenarbeit, Lesen', 1),
(6, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 1, 'Technologie, Innovation', 1),
(7, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Programmspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 'Schreiben, Theater', 1),
(8, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 'Social Media, Design', 1),
(9, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 'Handwerk, Familie', 1),
(10, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 'Elektronik, Basteln', 1),
(11, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 0, 1, 'Nähen, Backen', 1),
(12, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'HH', 'Programmspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Coding, Kryptowährung', 1),
(13, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'MV', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Journalismus, Politik', 1),
(14, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 'Werbung, Kreativität', 1),
(15, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Verwaltungsspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 'Organisation, Planung', 1),
(16, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Produktionsspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 'Drohnen, Robotik', 1),
(17, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Qualitätskontrolle', 1),
(18, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Programmspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 1, 0, 'Podcasting, Audio', 1),
(19, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Verwaltungsspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 'Eventmanagement', 1),
(20, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 0, 0, 1, 'Machine Learning, KI', 1),
(21, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 0, 1, 0, 1, 1, 1, 0, 'Buchhaltung, Finanzen', 1),
(22, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 'Broadcast Technology', 1),
(23, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Projektmanagement', 1),
(24, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Verwaltungsspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Influencer Marketing', 1),
(25, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Investigativer Journalismus', 1),
(26, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Digitalisierung, Prozesse', 1),
(27, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Programmspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 'Cybersecurity, Datenschutz', 1),
(28, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Produktionsspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 'Streaming Technology', 1),
(29, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Qualitätsmanagement', 1),
(30, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Content Creation', 1),
(31, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Programmspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Dokumentarfilm', 1),
(32, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Agile Methoden', 1),
(33, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Programmspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 'Software Development', 1),
(34, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 'Audio Engineering', 1),
(35, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Live-Produktion', 1),
(36, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Verwaltungsspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Digital Marketing', 1),
(37, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'MV', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Politikjournalismus', 1),
(38, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Change Management', 1),
(39, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Programmspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 'Database Management', 1),
(40, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Produktionsspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 'Virtual Reality', 1),
(41, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Post-Production', 1),
(42, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Brand Management', 1),
(43, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Wissenschaftsjournalismus', 1),
(44, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Lean Management', 1),
(45, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'MV', 'Programmspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 0, 0, 1, 'Legacy Systems', 1),
(46, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 0, 1, 1, 'Cloud Computing', 1),
(47, 'mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 'Content Strategy', 1),
(48, 'mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Verwaltungsspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 'Performance Marketing', 1),
(49, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 'Datenjournalismus', 1),
(50, 'mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Digital Transformation', 1);

-- Insert 50 rows into tandempartner table
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`,
                             `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`,
                             `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`,
                             `treffen_präsenz`, `treffen_online`, `treffen_häufigkeit`) VALUES
(1, 'mentor', 'Generation Y bis Jahrgang 1995', 'egal', 'HH', 'egal', 'Nein', 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 1, 'wöchentlich'),
(2, 'mentee', 'Generation X bis Jahrgang 1980', 'egal', 'egal', 'Programmspezifisch', 'Ja', 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 'zweiwöchentlich'),
(3, 'mentor', 'Generation Z bis Jahrgang 2010', 'Männlich', 'egal', 'egal', 'egal', 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 'monatlich'),
(4, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'egal', 'Ja', 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 0, 'wöchentlich'),
(5, 'mentor', 'Generation Y bis Jahrgang 1995', 'egal', 'egal', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
(6, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'egal', 'egal', 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 0, 1, 'monatlich'),
(7, 'mentor', 'Generation Z bis Jahrgang 2010', 'egal', 'MV', 'Verwaltungsspezifisch', 'egal', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
(8, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'egal', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
(9, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'egal', 'egal', 'Nein', 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
(10, 'mentee', 'Generation X bis Jahrgang 1980', 'egal', 'MV', 'Programmspezifisch', 'egal', 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 0, 'wöchentlich'),
(11, 'mentor', 'Generation Z bis Jahrgang 2010', 'Männlich', 'egal', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 'zweiwöchentlich'),
(12, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Produktionsspezifisch', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 'monatlich'),
(13, 'mentor', 'Generation Y bis Jahrgang 1995', 'egal', 'egal', 'egal', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
(14, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'HH', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
(15, 'mentor', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'egal', 'Verwaltungsspezifisch', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
(16, 'mentee', 'Generation X bis Jahrgang 1980', 'egal', 'MV', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
(17, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'egal', 'egal', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
(18, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Produktionsspezifisch', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
(19, 'mentor', 'Generation Z bis Jahrgang 2010', 'egal', 'egal', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 'wöchentlich'),
(20, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'HH', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
(21, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'egal', 'Programmspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
(22, 'mentee', 'Generation X bis Jahrgang 1980', 'egal', 'HH', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
(23, 'mentor', 'Generation Z bis Jahrgang 2010', 'Männlich', 'egal', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
(24, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
(25, 'mentor', 'Generation Y bis Jahrgang 1995', 'egal', 'egal', 'egal', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 'wöchentlich'),
(26, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
(27, 'mentor', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'egal', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 'monatlich'),
(28, 'mentee', 'Generation X bis Jahrgang 1980', 'egal', 'HH', 'egal', 'egal', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
(29, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'egal', 'egal', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 'zweiwöchentlich'),
(30, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
(31, 'mentor', 'Generation Z bis Jahrgang 2010', 'egal', 'egal', 'egal', 'egal', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 'wöchentlich'),
(32, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
(33, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'egal', 'Produktionsspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
(34, 'mentee', 'Generation X bis Jahrgang 1980', 'egal', 'MV', 'egal', 'egal', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
(35, 'mentor', 'Generation Z bis Jahrgang 2010', 'Männlich', 'egal', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 'zweiwöchentlich'),
(36, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Programmspezifisch', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
(37, 'mentor', 'Generation Y bis Jahrgang 1995', 'egal', 'egal', 'egal', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 'wöchentlich'),
(38, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'HH', 'egal', 'Ja', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
(39, 'mentor', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'egal', 'Verwaltungsspezifisch', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
(40, 'mentee', 'Generation X bis Jahrgang 1980', 'egal', 'MV', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 'wöchentlich'),
(41, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'egal', 'egal', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich'),
(42, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'egal', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
(43, 'mentor', 'Generation Z bis Jahrgang 2010', 'egal', 'egal', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
(44, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'HH', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
(45, 'mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'egal', 'Produktionsspezifisch', 'Nein', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'monatlich'),
(46, 'mentee', 'Generation X bis Jahrgang 1980', 'egal', 'HH', 'egal', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 'wöchentlich'),
(47, 'mentor', 'Generation Z bis Jahrgang 2010', 'Männlich', 'egal', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 'zweiwöchentlich'),
(48, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 'monatlich'),
(49, 'mentor', 'Generation Y bis Jahrgang 1995', 'egal', 'egal', 'egal', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 'wöchentlich'),
(50, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'egal', 'egal', 0, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 'zweiwöchentlich');

-- Summary:
-- This script inserts 50 rows into both profil and tandempartner tables
--
-- Profil table entries:
-- - 25 mentors and 25 mentees
-- - Mix of generations: Babyboomer, Generation X, Generation Y, Generation Z
-- - Locations: HH (Hamburg), MV (Mecklenburg-Vorpommern)
-- - Departments: Programmspezifisch, Produktionsspezifisch, Verwaltungsspezifisch
-- - Various personality traits and hobbies
--
-- Tandempartner table entries:
-- - Preferences for mentor/mentee matching
-- - Mix of generation preferences
-- - Location and department preferences (including 'egal' for flexible matching)
-- - Meeting preferences: wöchentlich, zweiwöchentlich, monatlich
-- - Various personality trait preferences
--
-- Usage:
-- Execute this script in your MySQL database to populate test data
-- Make sure the benutzer_id values (1-50) exist in your benutzer table first
