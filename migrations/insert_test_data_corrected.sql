-- Test data generation for profil, kompetenzen, and tandempartner tables
-- This script generates 50 test entries for each table with correct field values

-- First, let's insert test users into benutzer table if they don't exist
INSERT IGNORE INTO `benutzer` (`id`, `vorname`, `nachname`, `email`, `benutzername`) VALUES
(101, '<PERSON>', '<PERSON>', '<EMAIL>', 'aschmi<PERSON>'),
(102, '<PERSON>', '<PERSON>', 'micha<PERSON>.<EMAIL>', 'mweber'),
(103, '<PERSON>', '<PERSON>', '<EMAIL>', 'sfischer'),
(104, '<PERSON>', '<PERSON>', '<EMAIL>', 'twagner'),
(105, '<PERSON>', '<PERSON>', '<EMAIL>', 'jbecker'),
(106, '<PERSON>', '<PERSON><PERSON><PERSON>', '<EMAIL>', 'dschulz'),
(107, '<PERSON>', '<PERSON>', 'l<PERSON>.<PERSON><PERSON><PERSON>@ndr.de', '<PERSON><PERSON><PERSON>'),
(108, '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'mark<PERSON>.s<PERSON><PERSON><PERSON>@ndr.de', 'm<PERSON><PERSON><PERSON>'),
(109, '<PERSON>', '<PERSON>', '<EMAIL>', 'p<PERSON>ch'),
(110, '<PERSON>', '<PERSON>', 'ste<PERSON>.rich<PERSON>@ndr.de', 's<PERSON><PERSON>'),
(111, '<PERSON>', '<PERSON>', 'nicole.k<PERSON>@ndr.de', 'n<PERSON>in'),
(112, '<PERSON>', '<PERSON>', 'andrea<PERSON>.<EMAIL>', 'a<PERSON>'),
(113, '<PERSON>', '<PERSON>hr<PERSON><PERSON>', '<EMAIL>', 'sschroeder'),
(114, 'Christian', 'Neumann', '<EMAIL>', 'cneumann'),
(115, 'Martina', 'Schwarz', '<EMAIL>', 'mschwarz'),
(116, 'Jörg', 'Zimmermann', '<EMAIL>', 'jzimmermann'),
(117, 'Claudia', 'Braun', '<EMAIL>', 'cbraun'),
(118, 'Ralf', 'Krüger', '<EMAIL>', 'rkrueger'),
(119, 'Susanne', 'Hartmann', '<EMAIL>', 'shartmann'),
(120, 'Oliver', 'Lange', '<EMAIL>', 'olange'),
(121, 'Birgit', 'Schmitt', '<EMAIL>', 'bschmitt'),
(122, 'Frank', 'Werner', '<EMAIL>', 'fwerner'),
(123, 'Karin', 'Krause', '<EMAIL>', 'kkrause'),
(124, 'Uwe', 'Meier', '<EMAIL>', 'umeier'),
(125, 'Monika', 'Lehmann', '<EMAIL>', 'mlehmann'),
(126, 'Bernd', 'Müller', '<EMAIL>', 'bmueller'),
(127, 'Anja', 'Weiß', '<EMAIL>', 'aweiss'),
(128, 'Holger', 'Herrmann', '<EMAIL>', 'hherrmann'),
(129, 'Silke', 'König', '<EMAIL>', 'skoenig'),
(130, 'Dirk', 'Walter', '<EMAIL>', 'dwalter'),
(131, 'Gabi', 'Peters', '<EMAIL>', 'gpeters'),
(132, 'Torsten', 'Fuchs', '<EMAIL>', 'tfuchs'),
(133, 'Heike', 'Vogel', '<EMAIL>', 'hvogel'),
(134, 'Matthias', 'Keller', '<EMAIL>', 'mkeller'),
(135, 'Doris', 'Günther', '<EMAIL>', 'dguenther'),
(136, 'Carsten', 'Frank', '<EMAIL>', 'cfrank'),
(137, 'Ingrid', 'Möller', '<EMAIL>', 'imoeller'),
(138, 'Klaus', 'Berger', '<EMAIL>', 'kberger'),
(139, 'Renate', 'Huber', '<EMAIL>', 'rhuber'),
(140, 'Jürgen', 'Graf', '<EMAIL>', 'jgraf'),
(141, 'Christa', 'Schulze', '<EMAIL>', 'cschulze'),
(142, 'Wolfgang', 'Bauer', '<EMAIL>', 'wbauer'),
(143, 'Ursula', 'Dietrich', '<EMAIL>', 'udietrich'),
(144, 'Rainer', 'Maier', '<EMAIL>', 'rmaier'),
(145, 'Gabriele', 'Scholz', '<EMAIL>', 'gscholz'),
(146, 'Manfred', 'Gross', '<EMAIL>', 'mgross'),
(147, 'Helga', 'Roth', '<EMAIL>', 'hroth'),
(148, 'Dieter', 'Jung', '<EMAIL>', 'djung'),
(149, 'Elfriede', 'Hahn', '<EMAIL>', 'ehahn'),
(150, 'Günter', 'Lorenz', '<EMAIL>', 'glorenz');

-- Insert test data into profil table with correct field values
INSERT INTO `profil` (`rolle`, `benutzer_id`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, 
                      `geduld`, `fairness`, `empathie`, `loyalität`, `selbstdisziplin`, `humor`, `verlässlichkeit`, 
                      `kritikfähigkeit`, `hilfsbereitschaft`, `hands_on_mentalität`, `neugierde`, 
                      `sport`, `filme`, `musik`, `reisen`, `outdoor`, `kunst_und_kultur`, `yoga`, `ehrenamt`, 
                      `weitere_hobbys`, `anzeigbar`) VALUES
('mentor', 101, 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Programmspezifisch', 'Ja', 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 'Lesen, Kochen', 1),
('mentee', 102, 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 'Gaming, Programmieren', 1),
('mentor', 103, 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 'Fotografie, Reisen', 1),
('mentee', 104, 'Generation Z bis Jahrgang 2010', 'Männlich', 'MV', 'Produktionsspezifisch', 'Nein', 1, 0, 1, 1, 1, 1, 0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 'Fußball, Musik', 1),
('mentor', 105, 'Generation X bis Jahrgang 1980', 'Weiblich', 'HH', 'Verwaltungsspezifisch', 'Ja', 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 1, 1, 0, 'Gartenarbeit, Lesen', 1),
('mentee', 106, 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 1, 1, 0, 0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0, 1, 0, 0, 0, 1, 'Technologie, Innovation', 1),
('mentor', 107, 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Programmspezifisch', 'Nein', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, 'Schreiben, Theater', 1),
('mentee', 108, 'Generation Z bis Jahrgang 2010', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 'Social Media, Design', 1),
('mentor', 109, 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 1, 1, 1, 1, 0, 0, 1, 0, 1, 1, 0, 0, 1, 0, 1, 0, 1, 1, 1, 'Handwerk, Familie', 1),
('mentee', 110, 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 1, 0, 0, 1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0, 1, 'Elektronik, Basteln', 1);
