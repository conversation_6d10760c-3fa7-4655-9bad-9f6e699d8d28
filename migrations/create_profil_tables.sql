DROP TABLE IF EXISTS `profil`;

CREATE TABLE `profil` (
                          `id` int(11) NOT NULL AUTO_INCREMENT,
                          `benutzer_id` int(11) NOT NULL,
                          `rolle` varchar(12) NOT NULL,
                          `generation` enum('Babyboomer bis Jahrgang 1964','Generation X bis Jahrgang 1980','Generation Y bis Jahrgang 1995','Generation Z bis Jahrgang 2010') NOT NULL,
                          `geschlecht` enum('<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','Divers','keine <PERSON>') NOT NULL,
                          `standort` enum('HH','MV','NDS','SH','Auslandsstudio') NOT NULL,
                          `tätigkeitsfeld` enum('Programmspezifisch','Verwaltungsspezifisch','Produktionsspezifisch') NOT NULL,
                          `führungskraft` enum('Ja','Nein') NOT NULL,
                          `geduld` tinyint(4) DEFAULT NULL,
                          `fairness` tinyint(4) DEFAULT NULL,
                          `empathie` tinyint(4) DEFAULT NULL,
                          `loyalität` tinyint(4) DEFAULT NULL,
                          `selbstdisziplin` tinyint(4) DEFAULT NULL,
                          `humor` tinyint(4) DEFAULT NULL,
                          `verlässlichkeit` tinyint(4) DEFAULT NULL,
                          `kritikfähigkeit` tinyint(4) DEFAULT NULL,
                          `hilfsbereitschaft` tinyint(4) DEFAULT NULL,
                          `hands_on_mentalität` tinyint(4) DEFAULT NULL,
                          `neugierde` tinyint(4) DEFAULT NULL,
                          `sport` tinyint(4) DEFAULT NULL,
                          `filme` tinyint(4) DEFAULT NULL,
                          `musik` tinyint(4) DEFAULT NULL,
                          `reisen` tinyint(4) DEFAULT NULL,
                          `outdoor` tinyint(4) DEFAULT NULL,
                          `kunst_und_kultur` tinyint(4) DEFAULT NULL,
                          `yoga` tinyint(4) DEFAULT NULL,
                          `ehrenamt` tinyint(4) DEFAULT NULL,
                          `weitere_hobbys` varchar(500) DEFAULT NULL,
                          PRIMARY KEY (`id`)
) ;
ALTER TABLE `profil`
    ADD UNIQUE KEY `benutzer_rolle` (`benutzer_id`, rolle);
#ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
