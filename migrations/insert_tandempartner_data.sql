-- Insert statements for the 'tandempartner' table
-- Required fields: benutzer_id, rolle, generation, geschlecht, standort, tätig<PERSON>itsfeld, führungskraft
-- Optional fields: treffen_art, treffen_frequenz (default to 'Egal' if not specified)

-- Insert 1
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätig<PERSON>itsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (101, 'Mentor', 'Babyboomer bis Jahrgang 1964', '<PERSON><PERSON><PERSON><PERSON>', 'HH', 'Programmspezifisch', 'Ja', 'Online', 'Regelmäßig 1x pro Monat');

-- Insert 2
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätig<PERSON><PERSON>feld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (102, 'Mentee', 'Generation Z bis Jahrgang 2010', '<PERSON><PERSON><PERSON>', 'MV', 'Verwaltungsspezifisch', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen');

-- Insert 3
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (103, 'Mentor', 'Generation X bis Jahrgang 1980', 'Divers', 'NDS', 'Produktionsspezifisch', 'Ja', 'Egal', 'Regelmäßig 1x pro Monat');

-- Insert 4
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (104, 'Mentee', 'Generation Y bis Jahrgang 1995', 'keine Angabe', 'SH', 'Programmspezifisch', 'Nein', 'Online', 'Egal');

-- Insert 5
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (105, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen');

-- Insert 6
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (106, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'HH', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1x pro Monat');

-- Insert 7
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (107, 'Mentor', 'Generation X bis Jahrgang 1980', 'Weiblich', 'MV', 'Programmspezifisch', 'Ja', 'Egal', 'Egal');

-- Insert 8
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (108, 'Mentee', 'Generation Y bis Jahrgang 1995', 'Divers', 'NDS', 'Verwaltungsspezifisch', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen');

-- Insert 9
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (109, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'SH', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1x pro Monat');

-- Insert 10
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (110, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'Auslandsstudio', 'Programmspezifisch', 'Nein', 'in Präsenz', 'Egal');

-- Insert 11
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (111, 'Mentor', 'Generation Y bis Jahrgang 1995', 'keine Angabe', 'HH', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Regelmäßig alle 2 Wochen');

-- Insert 12
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (112, 'Mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1x pro Monat');

-- Insert 13
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (113, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'Weiblich', 'NDS', 'Programmspezifisch', 'Ja', 'in Präsenz', 'Egal');

-- Insert 14
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (114, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Divers', 'SH', 'Verwaltungsspezifisch', 'Nein', 'Egal', 'Regelmäßig alle 2 Wochen');

-- Insert 15
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (115, 'Mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'Auslandsstudio', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1x pro Monat');

-- Insert 16
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (116, 'Mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'HH', 'Programmspezifisch', 'Nein', 'in Präsenz', 'Egal');

-- Insert 17
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (117, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'keine Angabe', 'MV', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Regelmäßig alle 2 Wochen');

-- Insert 18
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (118, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Männlich', 'NDS', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1x pro Monat');

-- Insert 19
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (119, 'Mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'SH', 'Programmspezifisch', 'Ja', 'in Präsenz', 'Egal');

-- Insert 20
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (120, 'Mentee', 'Generation X bis Jahrgang 1980', 'Divers', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Nein', 'Egal', 'Regelmäßig alle 2 Wochen');

-- Insert 21
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (121, 'Mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'HH', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1x pro Monat');

-- Insert 22
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (122, 'Mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'MV', 'Programmspezifisch', 'Nein', 'in Präsenz', 'Egal');

-- Insert 23
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (123, 'Mentor', 'Generation X bis Jahrgang 1980', 'keine Angabe', 'NDS', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Regelmäßig alle 2 Wochen');

-- Insert 24
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (124, 'Mentee', 'Generation Y bis Jahrgang 1995', 'Männlich', 'SH', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1x pro Monat');

-- Insert 25
INSERT INTO `tandempartner` (`benutzer_id`, `rolle`, `generation`, `geschlecht`, `standort`, `tätigkeitsfeld`, `führungskraft`, `treffen_art`, `treffen_frequenz`)
VALUES (125, 'Mentor', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'Auslandsstudio', 'Programmspezifisch', 'Ja', 'in Präsenz', 'Egal');
