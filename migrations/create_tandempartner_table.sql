DROP TABLE IF EXISTS `tandempartner`;

CREATE TABLE `tandempartner` (
                                 `id` int(11) NOT NULL,
                                 `benutzer_id` int(11) NOT NULL,
                                 `rolle` varchar(12) NOT NULL,
                                 `generation` enum('Babyboomer bis Jahrgang 1964','Generation X bis Jahrgang 1980','Generation Y bis Jahrgang 1995','Generation Z bis Jahrgang 2010','Egal') NOT NULL,
                                 `geschlecht` enum('<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON>','<PERSON><PERSON>','keine <PERSON>','<PERSON>gal') NOT NULL,
                                 `standort` enum('HH','MV','NDS','SH','Auslandsstudio','Egal') NOT NULL,
                                 `tätigkeitsfeld` enum('Programmspezifisch','Verwaltungsspezifisch','Produktionsspezifisch', 'Egal') NOT NULL,
                                 `führungskraft` enum('J<PERSON>','<PERSON><PERSON>','Egal') NOT NULL,
                                 `treffen_art` enum('Online','in Präsenz','Egal') DEFAULT 'Egal',
                                 `treffen_frequenz` enum('Regelmäßig 1x pro Monat','Regelmäßig alle 2 Wochen','Egal') DEFAULT 'Egal'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

ALTER TABLE `tandempartner`
  ADD PRIMARY KEY (`id`);

ALTER TABLE `tandempartner`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

ALTER TABLE `tandempartner`
    ADD UNIQUE KEY `benutzer_rolle` (`benutzer_id`, rolle);