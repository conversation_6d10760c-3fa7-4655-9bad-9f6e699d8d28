/* Standardized form styles for the mentoring application */

/* Form container */
.form-container {
    margin-bottom: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    margin: 20px auto;
    max-width: 1200px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Form controls */
.form-container input[type="text"],
.form-container input[type="password"],
.form-container input[type="email"],
.form-container input[type="number"],
.form-container select,
.form-container textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 6px;
    box-sizing: border-box;
    margin-bottom: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background-color: #fff;
}

.form-container input:focus,
.form-container select:focus,
.form-container textarea:focus {
    outline: none;
    border-color: rgba(13, 23, 84, 1);
    box-shadow: 0 0 0 3px rgba(13, 23, 84, 0.1);
}

/* Dropdown specific styles */
.form-container select {
    min-height: 38px;
    height: auto;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

/* Select option styles to prevent cutting off */
.form-container select option {
    padding: 8px;
    white-space: normal;
    word-wrap: break-word;
}

/* Textarea specific styles */
.form-container textarea {
    min-height: 120px;
    resize: vertical;
    font-family: inherit;
}

/* Form control with specific width */
.mentoring-control {
    width: 400px;
    max-width: 100%;
}

/* Profile update control styles */
.profilupdate-control {
    width: 100px;
    max-width: 100%;
}

.profilupdate-control-control {
    width: 100%;
    max-width: 300px;
    min-width: 150px;
}

/* Table cell select fields need special handling */
td select {
    width: auto !important;
    min-width: 150px;
    max-width: 100%;
}

/* Checkbox container */
.form-container .form-check {
    padding-left: 25px;
    margin-bottom: 10px;
}

/* Form labels */
.form-container label {
    font-weight: bold;
    margin-bottom: 5px;
    display: block;
    color: #333;
    font-size: 0.95rem;
}

.form-container .control-label {
    font-weight: 600;
    color: rgba(13, 23, 84, 1);
}

/* Button styles */
.btn_nav_blue {
    background-color: rgba(13, 23, 84, 1) !important;
    border-color: rgba(13, 23, 84, 1) !important;
    color: white;
    min-width: 90px;
    width: 100px;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn_nav_blue:hover {
    background-color: rgba(13, 23, 84, 0.8) !important;
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 23, 84, 0.3);
}

.btn_nav_blue_save {
    background-color: rgba(13, 23, 84, 1) !important;
    border-color: rgba(13, 23, 84, 1) !important;
    color: white;
    min-width: 225px;
    width: 225px;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn_nav_blue_500px {
    background-color: rgba(13, 23, 84, 1) !important;
    border-color: rgba(13, 23, 84, 1) !important;
    color: white;
    min-width: 500px;
    width: 500px;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 600;
}

/* Form section headings */
.form-container h1,
.form-container h2,
.form-container h3 {
    color: rgba(13, 23, 84, 1);
    font-weight: bold;
    margin-bottom: 20px;
}

.form-container h1 {
    font-size: 1.8rem;
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(13, 23, 84, 0.1);
}

.form-container h2 {
    font-size: 1.5rem;
    margin-bottom: 25px;
}

.form-container h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: rgba(13, 23, 84, 0.8);
}

/* Form grid layout */
.form-container .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
    margin-bottom: 20px;
}

.form-container [class^="col-"] {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Form Field Groups */
.form-container .field-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.form-container .field-group h4 {
    color: rgba(13, 23, 84, 1);
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Checkbox and Radio Styles */
.form-container input[type="checkbox"],
.form-container input[type="radio"] {
    width: auto;
    margin-right: 8px;
    transform: scale(1.2);
}

.form-container .checkbox label,
.form-container .radio label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
    margin-bottom: 10px;
}

/* Checkbox List Styling */
.form-container .checkbox-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.form-container .checkbox-list .checkbox {
    flex: none;
    width: 100%;
    margin-right: 0;
    margin-bottom: 0;
}

/* Form Navigation */
.form-container .form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #eee;
}

.form-container .form-navigation .btn {
    min-width: 150px;
}

/* Error and Success States */
.form-container .has-error input,
.form-container .has-error select,
.form-container .has-error textarea {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-container .has-success input,
.form-container .has-success select,
.form-container .has-success textarea {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.form-container .help-block,
.form-container .invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-container .valid-feedback {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 5px;
}

/* Hint Text */
.form-container .hint,
.form-container .form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-container {
        margin: 10px;
        padding: 20px;
    }

    .mentoring-control {
        width: 100%;
    }

    .form-container .row {
        margin: 0;
    }

    .form-container .row > [class*="col-"] {
        padding: 0;
        margin-bottom: 15px;
    }

    .form-container .form-navigation {
        flex-direction: column;
        gap: 15px;
    }

    .form-container .form-navigation .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .form-container {
        padding: 15px;
    }

    .form-container h1 {
        font-size: 1.5rem;
    }

    .form-container h2 {
        font-size: 1.3rem;
    }

    .form-container h3 {
        font-size: 1.1rem;
    }

    .form-container input,
    .form-container select,
    .form-container textarea {
        padding: 10px 12px;
    }

    .btn_nav_blue,
    .btn_nav_blue_save,
    .btn_nav_blue_500px {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}