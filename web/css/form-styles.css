/* Standardized form styles for the mentoring application */

/* Form container */
.form-container {
    margin-bottom: 20px;
}

/* Form controls */
.form-container input[type="text"],
.form-container input[type="password"],
.form-container input[type="email"],
.form-container select,
.form-container textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-sizing: border-box;
    margin-bottom: 10px;
}

/* Dropdown specific styles */
.form-container select {
    min-height: 38px;
    height: auto;
    white-space: normal;
    overflow: visible;
    text-overflow: clip;
}

/* Select option styles to prevent cutting off */
.form-container select option {
    padding: 8px;
    white-space: normal;
    word-wrap: break-word;
}

/* Form control with specific width */
.mentoring-control {
    width: 400px;
    max-width: 100%;
}

/* Profile update control styles */
.profilupdate-control-control {
    width: 100%;
    max-width: 300px;
    min-width: 150px;
}

/* Table cell select fields need special handling */
td select {
    width: auto !important;
    min-width: 150px;
    max-width: 100%;
}

/* Checkbox container */
.form-container .form-check {
    padding-left: 25px;
    margin-bottom: 10px;
}

/* Form labels */
.form-container label {
    font-weight: bold;
    margin-bottom: 5px;
    display: block;
}

/* Form buttons */
.btn_nav_blue {
    background-color: rgba(13, 23, 84, 1) !important;
    border-color: rgba(13, 23, 84, 1) !important;
    color: white;
    min-width: 90px;
    width: 100px;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    display: inline-block;
}

.btn_nav_blue:hover {
    background-color: rgba(13, 23, 84, 0.8) !important;
    color: white;
    text-decoration: none;
}

/* Form section headings */
.form-container h3 {
    font-weight: bold;
    color: rgba(13, 23, 84, 1);
    margin-bottom: 15px;
}

/* Form grid layout */
.form-container .row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.form-container [class^="col-"] {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .mentoring-control {
        width: 100%;
    }
}