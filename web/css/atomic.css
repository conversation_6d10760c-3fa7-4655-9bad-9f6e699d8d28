* {
    box-sizing: border-box
}

.hidden {
    display: none
}

.clr {
    clear: both
}

.b_0 {
    bottom: 0
}

.b-0 {
    bottom: 0 !important
}

.bd_0 {
    border: 0
}

.bd-0 {
    border: 0 !important
}

.bd_dev {
    border: 1px dashed red
}

.bd-dev {
    border: 1px dashed red !important
}

.bdb_0 {
    border-bottom: 0
}

.bdb-0 {
    border-bottom: 0
}

.bdc_bright {
    border-color: #fff
}

.bdc-bright {
    border-color: #fff !important
}

.bdc_dark {
    border-color: #000
}

.bdc-dark {
    border-color: #000 !important
}

.bdl_0 {
    border-left: 0
}

.bdl-0 {
    border-left: 0
}

.bdr_0 {
    border-right: 0
}

.bdr-0 {
    border-right: 0
}

.bdrz_0 {
    border-radius: 0
}

.bdrz-0 {
    border-radius: 0 !important
}

.bds_s {
    border-style: solid
}

.bds-s {
    border-style: solid !important
}

.bdt_0 {
    border-top: 0
}

.bdt-0 {
    border-top: 0
}

.bgc_bright_10_oh:hover {
    background-color: rgba(255, 255, 255, .1)
}

.bgc-bright-10-oh:hover {
    background-color: rgba(255, 255, 255, .1) !important
}

.bgc_bright_10 {
    background-color: rgba(255, 255, 255, .1)
}

.bgc-bright-10 {
    background-color: rgba(255, 255, 255, .1) !important
}

.bgc_bright_20_oh:hover {
    background-color: rgba(255, 255, 255, .2)
}

.bgc-bright-20-oh:hover {
    background-color: rgba(255, 255, 255, .2) !important
}

.bgc_bright_20 {
    background-color: rgba(255, 255, 255, .2)
}

.bgc-bright-20 {
    background-color: rgba(255, 255, 255, .2) !important
}

.bgc_bright_30 {
    background-color: rgba(255, 255, 255, .3)
}

.bgc-bright-30 {
    background-color: rgba(255, 255, 255, .3) !important
}

.bgc_bright {
    background-color: #fff
}

.bgc-bright {
    background-color: #fff !important
}

.bgc_dark_5_oh:hover {
    background-color: rgba(0, 0, 0, .05)
}

.bgc-dark-5-oh:hover {
    background-color: rgba(0, 0, 0, .05) !important
}

.bgc_dark_5 {
    background-color: rgba(0, 0, 0, .05)
}

.bgc-dark-5 {
    background-color: rgba(0, 0, 0, .05) !important
}

.bgc_dark_10_oh:hover {
    background-color: rgba(0, 0, 0, .1)
}

.bgc-dark-10-oh:hover {
    background-color: rgba(0, 0, 0, .1) !important
}

.bgc_dark_10 {
    background-color: rgba(0, 0, 0, .1)
}

.bgc-dark-10 {
    background-color: rgba(0, 0, 0, .1) !important
}

.bgc_dark_20 {
    background-color: rgba(0, 0, 0, .2)
}

.bgc-dark-20 {
    background-color: rgba(0, 0, 0, .2) !important
}

.bgc_dark_30 {
    background-color: rgba(0, 0, 0, .3)
}

.bgc-dark-30 {
    background-color: rgba(0, 0, 0, .3) !important
}

.bgc_dark {
    background-color: #000
}

.bgc-dark {
    background-color: #000 !important
}

.bgc_dim {
    background-color: rgba(0, 0, 0, 0.05)
}

.bgc-dim {
    background-color: rgba(0, 0, 0, 0.05) !important
}

.bgc_t {
    background-color: transparent
}

.bgc-t {
    background-color: transparent !important
}

.bgg_light {
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #FFF), color-stop(1, #DDD));
    background-image: -o-linear-gradient(bottom, #FFF 0%, #DDD 100%);
    background-image: -moz-linear-gradient(bottom, #FFF 0%, #DDD 100%);
    background-image: -webkit-linear-gradient(bottom, #FFF 0%, #DDD 100%);
    background-image: -ms-linear-gradient(bottom, #FFF 0%, #DDD 100%);
    background-image: linear-gradient(to bottom, #FFF 0%, #DDD 100%)
}

.bgg-light {
    background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #FFF), color-stop(1, #DDD)) !important;
    background-image: -o-linear-gradient(bottom, #FFF 0%, #DDD 100%) !important;
    background-image: -moz-linear-gradient(bottom, #FFF 0%, #DDD 100%) !important;
    background-image: -webkit-linear-gradient(bottom, #FFF 0%, #DDD 100%) !important;
    background-image: -ms-linear-gradient(bottom, #FFF 0%, #DDD 100%) !important;
    background-image: linear-gradient(to bottom, #FFF 0%, #DDD 100%) !important
}

.bgi_0 {
    background-image: none
}

.bgi-0 {
    background-image: none !important
}

.bgp_c {
    background-position: center
}

.bgp-c {
    background-position: center !important
}

.bgz_ct {
    -webkit-background-size: contain;
    -moz-background-size: contain;
    -o-background-size: contain;
    background-size: contain
}

.bgz-ct {
    -webkit-background-size: contain !important;
    -moz-background-size: contain !important;
    -o-background-size: contain !important;
    background-size: contain !important
}

.bgz_cv {
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;
    background-size: cover
}

.bgz-cv {
    -webkit-background-size: cover !important;
    -moz-background-size: cover !important;
    -o-background-size: cover !important;
    background-size: cover !important
}

.bxsh_0 {
    box-shadow: 0
}

.bxsh-0 {
    box-shadow: 0 !important
}

.c_bright_a:after {
    color: #fff
}

.c-bright-a:after {
    color: #fff !important
}

.c_bright_b:before {
    color: #fff
}

.c-bright-b:before {
    color: #fff !important
}

.c_bright_oh:hover {
    color: rgba(255, 255, 255, 1)
}

.c-bright-oh:hover {
    color: rgba(255, 255, 255, 1) !important
}

.c_bright {
    color: #fff
}

.c-bright {
    color: #fff !important
}

.c_dark_10 {
    color: rgba(0, 0, 0, .1)
}

.c-dark-10 {
    color: rgba(0, 0, 0, .1) !important
}

.c_dark_20 {
    color: rgba(0, 0, 0, .2)
}

.c-dark-20 {
    color: rgba(0, 0, 0, .2) !important
}

.c_dark_30 {
    color: rgba(0, 0, 0, .3)
}

.c-dark-30 {
    color: rgba(0, 0, 0, .3) !important
}

.c_dark_40 {
    color: rgba(0, 0, 0, .4)
}

.c-dark-40 {
    color: rgba(0, 0, 0, .4) !important
}

.c_dark_50 {
    color: rgba(0, 0, 0, .5)
}

.c-dark-50 {
    color: rgba(0, 0, 0, .5) !important
}

.c_dark_60 {
    color: rgba(0, 0, 0, .6)
}

.c-dark-60 {
    color: rgba(0, 0, 0, .6) !important
}

.c_dark_70 {
    color: rgba(0, 0, 0, .7)
}

.c-dark-70 {
    color: rgba(0, 0, 0, .7) !important
}

.c_dark_80 {
    color: rgba(0, 0, 0, .8)
}

.c-dark-80 {
    color: rgba(0, 0, 0, .8) !important
}

.c_dark_90 {
    color: rgba(0, 0, 0, .9)
}

.c-dark-90 {
    color: rgba(0, 0, 0, .9) !important
}

.c_dark_a:after {
    color: #000
}

.c-dark-a:after {
    color: #000 !important
}

.c_dark_b:before {
    color: #000
}

.c-dark-b:before {
    color: #000 !important
}

.c_dark {
    color: #000
}

.c-dark {
    color: #000 !important
}

.c_gold_a:after {
    color: gold
}

.c-gold-a:after {
    color: gold !important
}

.c_gold_b:before {
    color: gold
}

.c-gold-b:before {
    color: gold !important
}

.c_gold {
    color: gold
}

.c-gold {
    color: gold !important
}

.c_inh {
    color: inherit
}

.c-inh {
    color: inherit !important
}

.c_muted_a:after {
    color: #bbb
}

.c-muted-a:after {
    color: #bbb !important
}

.c_muted_b:before {
    color: #bbb
}

.c-muted-b:before {
    color: #bbb !important
}

.c_muted {
    color: #bbb
}

.c-muted {
    color: #bbb !important
}

.cl_2 {
    -webkit-column-count: 2;
    -moz-column-count: 2;
    column-count: 2
}

.cl-2 {
    -webkit-column-count: 2 !important;
    -moz-column-count: 2 !important;
    column-count: 2 !important
}

.cl_3 {
    -webkit-column-count: 3;
    -moz-column-count: 3;
    column-count: 3
}

.cl-3 {
    -webkit-column-count: 3 !important;
    -moz-column-count: 3 !important;
    column-count: 3 !important
}

.cur_d {
    cursor: default
}

.cur-d {
    cursor: default !important
}

.cur_ha {
    cursor: pointer
}

.cur-ha {
    cursor: pointer !important
}

.cur_m {
    cursor: move
}

.cur-m {
    cursor: move !important;
    cursor: zoom-in !important
}

.cur_zoomin {
    cursor: zoom-in
}

.d_b {
    display: block
}

.d-b {
    display: block !important
}

.d_ib {
    display: inline-block
}

.d-ib {
    display: inline-block !important
}

.d_tb {
    display: table
}

.d-tb {
    display: table !important
}

.d_tbc {
    display: table-cell
}

.d-tbc {
    display: table-cell !important
}

.fef_eb {
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.4)
}

.fef-eb {
    text-shadow: 0 1px 0 rgba(255, 255, 255, 0.4) !important
}

.ff_ss {
    font-family: 'arial', serif
}

.ff-ss {
    font-family: 'arial', "serif!important"
}

.fl_l {
    float: left
}

.fl-l {
    float: left !important
}

.fl_n {
    float: none
}

.fl-n {
    float: none !important
}

.fl_r {
    float: right
}

.fl-r {
    float: right !important
}

.fs_l {
    font-size: 1.4rem
}

.fs-l {
    font-size: 1.4rem !important
}

.fs_n {
    font-size: 1rem
}

.fs-n {
    font-size: 1rem !important
}

.fs_s {
    font-size: .8rem
}

.fs-s {
    font-size: .8rem !important
}

.fs_xl {
    font-size: 2rem
}

.fs-xl {
    font-size: 2rem !important
}

.fs_xs {
    font-size: .7rem
}

.fs-xs {
    font-size: .7rem !important
}

.fs_xxl {
    font-size: 3rem
}

.fs-xxl {
    font-size: 3rem !important
}

.fs_xxxl {
    font-size: 4rem
}

.fs-xxxl {
    font-size: 4rem !important
}

.fst_i {
    font-style: italic
}

.fst-i {
    font-style: italic !important
}

.fw_b {
    font-weight: bold
}

.fw-b {
    font-weight: bold !important
}

.fw_n {
    font-weight: normal
}

.fw-n {
    font-weight: normal !important
}

.gray {
    filter: url("data:image/svg+xml;utf8,<svg xmlns=\'http://www.w3.org/2000/svg\'><filter id=\'grayscale\'><feColorMatrix type=\'matrix\' values=\'0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0\'/></filter></svg>#grayscale");
    filter: gray;
    -webkit-filter: grayscale(100%)
}

.h_80 {
    height: 80px
}

.h-80 {
    height: 80px !important
}

.h_eq {
    vertical-align: top;
    float: none;
    display: table-cell
}

.h-eq {
    vertical-align: top !important;
    float: none !important;
    display: table-cell !important
}

.hy_a {
    hyphens: auto;
    -moz-hyphens: auto;
    -webkit-hyphens: auto;
    -ms-hyphens: auto
}

.hy-a {
    hyphens: auto !important;
    -moz-hyphens: auto !important;
    -webkit-hyphens: auto !important;
    -ms-hyphens: auto !important
}

.hy_m {
    hyphens: manual;
    -moz-hyphens: manual;
    -webkit-hyphens: manual;
    -ms-hyphens: manual
}

.hy-m {
    hyphens: manual !important;
    -moz-hyphens: manual !important;
    -webkit-hyphens: manual !important;
    -ms-hyphens: manual !important
}

.hy_n {
    hyphens: none;
    -moz-hyphens: none;
    -webkit-hyphens: none;
    -ms-hyphens: none
}

.hy-n {
    hyphens: none !important;
    -moz-hyphens: none !important;
    -webkit-hyphens: none !important;
    -ms-hyphens: none !important
}

.l_0 {
    left: 0
}

.l-0 {
    left: 0 !important
}

.lh_l {
    line-height: 1.8rem
}

.lh-l {
    line-height: 1.8rem !important
}

.lh_n {
    line-height: 1.4rem
}

.lh-n {
    line-height: 1.4rem !important
}

.lh_nl {
    line-height: 1.5rem
}

.lh-nl {
    line-height: 1.5rem !important
}

.lh_xl {
    line-height: 2.0rem
}

.lh-xl {
    line-height: 2.0rem !important
}

.lh_xxl {
    line-height: 3.0rem
}

.lh-xxl {
    line-height: 3.0rem !important
}

.lh_xxxl {
    line-height: 4.0rem
}

.lh-xxxl {
    line-height: 4.0rem !important
}

.m_0 {
    margin: 0
}

.m-0 {
    margin: 0 0 !important
}

.m_0a {
    margin: 0 auto
}

.m-0a {
    margin: 0 auto auto !important
}

.m_a {
    margin: auto
}

.m-a {
    margin: auto auto !important
}

.m_l {
    margin: 2rem
}

.m-l {
    margin: 2rem 2rem !important
}

.m_n {
    margin: 1rem
}

.m-n {
    margin: 1rem 1rem !important
}

.m_s {
    margin: .5rem
}

.m-s {
    margin: .5rem .5rem !important
}

.m_xxl {
    margin: 8rem
}

.m-xxl {
    margin: 8rem 8rem !important
}

.mah_100 {
    max-height: 100%
}

.mah-100 {
    max-height: 100% !important
}

.maw_100 {
    max-width: 100%
}

.maw-100 {
    max-width: 100% !important
}

.maw_50 {
    max-width: 50%
}

.maw-50 {
    max-width: 50% !important
}

.mb_0 {
    margin-bottom: 0
}

.mb-0 {
    margin-bottom: 0 !important
}

.mb_5px {
    margin-bottom: 5px
}

.mb-5px {
    margin-bottom: 5px !important
}

.mb_l {
    margin-bottom: 2rem
}

.mb-l {
    margin-bottom: 2rem !important
}

.mb_n {
    margin-bottom: 1rem
}

.mb-n {
    margin-bottom: 1rem !important
}

.mb_s {
    margin-bottom: .5rem
}

.mb-s {
    margin-bottom: .5rem !important
}

.mb_xl {
    margin-bottom: 3rem
}

.mb-xl {
    margin-bottom: 3rem !important
}

.mb_xs {
    margin-bottom: .25rem
}

.mb-xs {
    margin-bottom: .25rem !important
}

.mb_xxl {
    margin-bottom: 4rem
}

.mb-xxl {
    margin-bottom: 4rem !important
}

.mb_xxs {
    margin-bottom: .125rem
}

.mb-xxs {
    margin-bottom: .125rem !important
}

.mih_100 {
    min-height: 100%
}

.mih-100 {
    min-height: 100% !important
}

.miw_100 {
    min-width: 100%
}

.miw-100 {
    min-width: 100% !important
}

.miw_50 {
    min-width: 50%
}

.miw-50 {
    min-width: 50% !important
}

.ml_l {
    margin-left: 2rem
}

.ml-l {
    margin-left: 2rem !important
}

.ml_n {
    margin-left: 1rem
}

.ml-n {
    margin-left: 1rem !important
}

.ml_-n {
    margin-left: -1rem
}

.ml--n {
    margin-left: -1rem !important
}

.ml_s {
    margin-left: .5rem
}

.ml-s {
    margin-left: .5rem !important
}

.ml_xs {
    margin-left: .25rem
}

.ml-xs {
    margin-left: .25rem !important
}

.mr_0 {
    margin-right: 0
}

.mr-0 {
    margin-right: 0 !important
}

.mr_l {
    margin-right: 2rem
}

.mr-l {
    margin-right: 2rem !important
}

.mr_n {
    margin-right: 1rem
}

.mr-n {
    margin-right: 1rem !important
}

.mr_-n {
    margin-right: -1rem
}

.mr--n {
    margin-right: -1rem !important
}

.mr_s {
    margin-right: .5rem
}

.mr-s {
    margin-right: .5rem !important
}

.mr_xs {
    margin-right: .25rem
}

.mr-xs {
    margin-right: .25rem !important
}

.mt_0 {
    margin-top: 0
}

.mt-0 {
    margin-top: 0 !important
}

.mt_l {
    margin-top: 2rem
}

.mt-l {
    margin-top: 2rem !important
}

.mt_n {
    margin-top: 1rem
}

.mt-n {
    margin-top: 1rem !important
}

.mt_s {
    margin-top: .5rem
}

.mt-s {
    margin-top: .5rem !important
}

.mt_xl {
    margin-top: 3rem
}

.mt-xl {
    margin-top: 3rem !important
}

.mt_xs {
    margin-top: .25rem
}

.mt-xs {
    margin-top: .25rem !important
}

.mt_xxl {
    margin-top: 8rem
}

.mt-xxl {
    margin-top: 8rem !important
}

.op_0 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    filter: alpha(opacity=0);
    -moz-opacity: .0;
    -khtml-opacity: .0;
    opacity: .0
}

.op_10 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=10)";
    filter: alpha(opacity=10);
    -moz-opacity: .10;
    -khtml-opacity: .10;
    opacity: .10
}

.op-10 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=10)" !important;
    filter: alpha(opacity=10) !important;
    -moz-opacity: .10 !important;
    -khtml-opacity: .10 !important;
    opacity: .10 !important
}

.op_100_oh:hover {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1
}

.op-100-oh:hover {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)" !important;
    filter: alpha(opacity=100) !important;
    -moz-opacity: 1 !important;
    -khtml-opacity: 1 !important;
    opacity: 1 !important
}

.op_100 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1
}

.op-100 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)" !important;
    filter: alpha(opacity=100) !important;
    -moz-opacity: 1 !important;
    -khtml-opacity: 1 !important;
    opacity: 1 !important
}

.op_30 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
    filter: alpha(opacity=30);
    -moz-opacity: .30;
    -khtml-opacity: .30;
    opacity: .30
}

.op_50 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: .50;
    -khtml-opacity: .50;
    opacity: .50
}

.op_75 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=75)";
    filter: alpha(opacity=75);
    -moz-opacity: .75;
    -khtml-opacity: .75;
    opacity: .75
}

.op-75 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=75)" !important;
    filter: alpha(opacity=75) !important;
    -moz-opacity: .75 !important;
    -khtml-opacity: .75 !important;
    opacity: .75 !important
}

.op_80 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    filter: alpha(opacity=80);
    -moz-opacity: .80;
    -khtml-opacity: .80;
    opacity: .80
}

.op-80 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)" !important;
    filter: alpha(opacity=80) !important;
    -moz-opacity: .80 !important;
    -khtml-opacity: .80 !important;
    opacity: .80 !important
}

.op_90 {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=90)";
    filter: alpha(opacity=90);
    -moz-opacity: .90;
    -khtml-opacity: .90;
    opacity: .90
}

.ov_a {
    overflow: auto
}

.ov-a {
    overflow: auto !important
}

.ov_h {
    overflow: hidden
}

.ov-h {
    overflow: hidden !important
}

.ovy_a {
    overflow-y: auto
}

.ovy-a {
    overflow-y: auto !important
}

.p_0 {
    padding: 0
}

.p-0 {
    padding: 0 0 !important
}

.p_l {
    padding: 2rem
}

.p-l {
    padding: 2rem 2rem !important
}

.p_n {
    padding: 1rem
}

.p-n {
    padding: 1rem 1rem !important
}

.p_s {
    padding: .5rem
}

.p-s {
    padding: .5rem .5rem !important
}

.p_xs {
    padding: .3rem
}

.p-xs {
    padding: .3rem .3rem !important
}

.pb_0 {
    padding-bottom: 0
}

.pb-0 {
    padding-bottom: 0 !important
}

.pb_l {
    padding-bottom: 2rem
}

.pb-l {
    padding-bottom: 2rem !important
}

.pb_n {
    padding-bottom: 1rem
}

.pb-n {
    padding-bottom: 1rem !important
}

.pb_s {
    padding-bottom: .5rem
}

.pb-s {
    padding-bottom: .5rem !important
}

.pb_xl {
    padding-bottom: 3rem
}

.pb-xl {
    padding-bottom: 3rem !important
}

.pb_xs {
    padding-bottom: .25rem
}

.pb-xs {
    padding-bottom: .25rem !important
}

.pb_xxl {
    padding-bottom: 8rem
}

.pb-xxl {
    padding-bottom: 8rem !important
}

.pl_0 {
    padding-left: 0
}

.pl-0 {
    padding-left: 0 !important
}

.pl_l {
    padding-left: 2rem
}

.pl-l {
    padding-left: 2rem !important
}

.pl_n {
    padding-left: 1rem
}

.pl-n {
    padding-left: 1rem !important
}

.pl_xl {
    padding-left: 3rem
}

.pl-xl {
    padding-left: 3rem !important
}

.pl_s {
    padding-left: .5rem
}

.pl-s {
    padding-left: .5rem !important
}

.pl_xs {
    padding-left: .3rem
}

.pl-xs {
    padding-left: .3rem !important
}

.pos_a {
    position: absolute
}

.pos-a {
    position: absolute !important
}

.pos_f {
    position: fixed
}

.pos-f {
    position: fixed !important
}

.pos_r {
    position: relative
}

.pos-r {
    position: relative !important
}

.pos_s {
    position: static
}

.pos-s {
    position: static !important
}

.pr_0 {
    padding-right: 0
}

.pr-0 {
    padding-right: 0 !important
}

.pr_l {
    padding-right: 2rem
}

.pr-l {
    padding-right: 2rem !important
}

.pr_n {
    padding-right: 1rem
}

.pr-n {
    padding-right: 1rem !important
}

.pr_xl {
    padding-right: 3rem
}

.pr-xl {
    padding-right: 3rem !important
}

.pr_s {
    padding-right: .5rem
}

.pr-s {
    padding-right: .5rem !important
}

.pr_xs {
    padding-right: .3rem
}

.pr-xs {
    padding-right: .3rem !important
}

.pt_0 {
    padding-top: 0
}

.pt-0 {
    padding-top: 0 !important
}

.pt_100 {
    padding-top: 100%
}

.pt-100 {
    padding-top: 100% !important
}

.pt_l {
    padding-top: 2rem
}

.pt-l {
    padding-top: 2rem !important
}

.pt_n {
    padding-top: 1rem
}

.pt-n {
    padding-top: 1rem !important
}

.pt_s {
    padding-top: .5rem
}

.pt-s {
    padding-top: .5rem !important
}

.pt_xl {
    padding-top: 3rem
}

.pt-xl {
    padding-top: 3rem !important
}

.pt_xs {
    padding-top: .25rem
}

.pt-xs {
    padding-top: .25rem !important
}

.pt_xxl {
    padding-top: 8rem
}

.pt-xxl {
    padding-top: 8rem !important
}

.r_0 {
    right: 0
}

.r-0 {
    right: 0 !important
}

.ratio_sq > * {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0
}

.ratio-sq > * {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0 !important
}

.ratio_sq:before {
    content: "";
    display: block;
    padding-top: 100%
}

.ratio-sq:before {
    content: "";
    display: block;
    padding-top: 100% !important
}

.ratio_sq {
    position: relative;
    width: inherit;
    overflow: hidden
}

.ratio-sq {
    position: relative;
    width: inherit;
    overflow: hidden !important
}

.t_0 {
    top: 0
}

.t-0 {
    top: 0 !important
}

.ta_c {
    text-align: center
}

.ta-c {
    text-align: center !important
}

.ta_j {
    text-align: justify
}

.ta-j {
    text-align: justify !important
}

.ta_l {
    text-align: left
}

.ta-l {
    text-align: left !important
}

.ta_r {
    text-align: right
}

.ta-r {
    text-align: right !important
}

.td_0 {
    text-decoration: none
}

.td-0 {
    text-decoration: none !important
}

.td_l {
    text-decoration: line-through
}

.td-l {
    text-decoration: line-through !important
}

.td_ul_oh:hover {
    text-decoration: underline
}

.td-ul-oh:hover {
    text-decoration: underline !important
}

.td_ul {
    text-decoration: underline
}

.td-ul {
    text-decoration: underline !important
}

.ti_0 {
    text-indent: 0
}

.ti-0 {
    text-indent: 0 !important
}

.ti_-1000 {
    text-indent: -1000rem
}

.ti--1000 {
    text-indent: -1000rem !important
}

.tov_el {
    text-overflow: ellipsis
}

.tov-el {
    text-overflow: ellipsis !important
}

.tov_h {
    text-overflow: hidden
}

.tov-h {
    text-overflow: hidden !important
}

.ts_0 {
    text-shadow: none
}

.ts-0 {
    text-shadow: none !important
}

.ts_l {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5)
}

.ts-l {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5) !important
}

.ts_s {
    text-shadow: 0 1px 1px rgba(0, 0, 0, .5)
}

.ts-s {
    text-shadow: 0 1px 1px rgba(0, 0, 0, .5) !important
}

.tt_0 {
    text-transform: none
}

.tt-0 {
    text-transform: none !important
}

.tt_uc {
    text-transform: uppercase
}

.tt-uc {
    text-transform: uppercase !important
}

.v_h {
    visibility: hidden
}

.v-h {
    visibility: hidden !important
}

.va_c {
    position: relative;
    -webkit-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%)
}

.va-c {
    position: relative !important;
    -webkit-transform: translateY(-50%) !important;
    -ms-transform: translateY(-50%) !important;
    transform: translateY(-50%) !important
}

.va_t {
    vertical-align: top
}

.va-t {
    vertical-align: top !important
}

.w_100 {
    width: 100%
}

.w-100 {
    width: 100% !important
}

.w_a {
    width: auto
}

.w-a {
    width: auto !important
}

.whs_nw {
    white-space: nowrap
}

.whs-nw {
    white-space: nowrap !important
}

.z_1000 {
    z-index: 1000
}

.z-1000 {
    z-index: 1000 !important
}

.zoo_oh {
    position: absolute;
    height: 108%;
    left: -4%;
    top: -4%;
    width: 108%
}

.zoo-oh {
    position: absolute;
    height: 108% !important;
    left: -4% !important;
    top: -4% !important;
    width: 108% !important
}

@media (min-width: 480px) {
    .bd_dev {
        border-color: green
    }

    .bd-dev {
        border-color: green !important
    }
}

@media (min-width: 768px) {
    .bd_dev {
        border-color: #add8e6
    }

    .bd-dev {
        border-color: #add8e6 !important
    }

    .bd_dev:before {
        content: "sm";
        position: absolute
    }

    .bd-dev:before {
        content: "sm" !important;
        position: absolute
    }

    .bgc_t_sm {
        background-color: transparent
    }

    .bgc-t-sm {
        background-color: transparent !important
    }

    .fl_r_sm {
        float: right
    }

    .fl-r-sm {
        float: right !important
    }

    .ta_r_sm {
        text-align: right
    }

    .ta-r-sm {
        text-align: right !important
    }

    .ta_l_sm {
        text-align: left
    }

    .ta-l-sm {
        text-align: left !important
    }
}

@media (min-width: 992px) {
    .bd_dev {
        border-color: pink
    }

    .bd-dev {
        border-color: pink !important
    }

    .bd_dev:before {
        content: "md";
        position: absolute
    }

    .bd-dev:before {
        content: "md" !important;
        position: absolute
    }

    .bgc_t_md {
        background-color: transparent
    }

    .bgc-t-md {
        background-color: transparent !important
    }

    .fl_r_md {
        float: right
    }

    .fl-r-md {
        float: right !important
    }

    .ta_r_md {
        text-align: right
    }

    .ta-r-md {
        text-align: right !important
    }

    .ta_l_md {
        text-align: left
    }

    .ta-l-md {
        text-align: left !important
    }
}

@media (min-width: 1200px) {
    .bd_dev {
        border-color: #00f
    }

    .bd-dev {
        border-color: #00f !important
    }

    .bd_dev:before {
        content: "lg";
        position: absolute
    }

    .bd-dev:before {
        content: "lg" !important;
        position: absolute
    }

    .bgc_t_lg {
        background-color: transparent
    }

    .bgc-t-lg {
        background-color: transparent !important
    }

    .fl_r_lg {
        float: right
    }

    .fl-r-lg {
        float: right !important
    }

    .ta_r_lg {
        text-align: right
    }

    .ta-r-lg {
        text-align: right !important
    }

    .ta_l_lg {
        text-align: left
    }

    .ta-l-lg {
        text-align: left !important
    }
}

.bd_dark_10 {
    border: 1px solid rgba(0, 0, 0, 0.1)
}

.bd-dark-10 {
    border: 1px solid rgba(0, 0, 0, 0.1) !important
}

.bdb_dark_10 {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.bdb-dark-10 {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1)
}

.bd_brand {
    border: #1d5596
}

.bd-brand {
    border: #1d5596
}

.bdb_brand {
    border-bottom: #1d5596
}

.bdb-brand {
    border-bottom: #1d5596
}

.bdt_brand {
    border-top: #1d5596
}

.bdt-brand {
    border-top: #1d5596
}

.bdc_blue {
    border-color: rgba(98, 130, 186, 1)
}

.bdc-blue {
    border-color: rgba(98, 130, 186, 1) !important
}

.bdc_darkblue {
    border-color: rgba(13, 23, 84, 1)
}

.bdc-darkblue {
    border-color: rgba(13, 23, 84, 1) !important
}

.bdc_lightblue {
    border-color: rgba(29, 85, 150, 1)
}

.bdc-lightblue {
    border-color: rgba(29, 85, 150, 1) !important
}

.bd_warmgray {
    border: 1px solid rgba(0, 0, 0, 0.075)
}

.bd-warmgray {
    border: 1px solid rgba(0, 0, 0, 0.075) !important
}

.bdc_warmgray {
    border-color: rgba(0, 0, 0, 0.075)
}

.bdc-warmgray {
    border-color: rgba(0, 0, 0, 0.075) !important
}

.bgc_blue_10_oh:hover {
    background-color: rgba(98, 130, 186, .1)
}

.bgc-blue-10-oh:hover {
    background-color: rgba(98, 130, 186, .1) !important
}

.bgc_blue_10 {
    background-color: rgba(98, 130, 186, .1)
}

.bgc-blue-10 {
    background-color: rgba(98, 130, 186, .1) !important
}

.bgc_blue_20 {
    background-color: rgba(98, 130, 186, .2)
}

.bgc-blue-20 {
    background-color: rgba(98, 130, 186, .2) !important
}

.bgc_blue_30 {
    background-color: rgba(98, 130, 186, .3)
}

.bgc-blue-30 {
    background-color: rgba(98, 130, 186, .3) !important
}

.bgc_blue_a:after {
    background-color: rgba(98, 130, 186, 1)
}

.bgc-blue-a:after {
    background-color: rgba(98, 130, 186, 1) !important
}

.bgc_blue_b:before {
    background-color: rgba(98, 130, 186, 1)
}

.bgc-blue-b:before {
    background-color: rgba(98, 130, 186, 1) !important
}

.bgc_blue {
    background-color: rgba(98, 130, 186, 1)
}

.bgc-blue {
    background-color: rgba(98, 130, 186, 1) !important
}

.bgc_darkblue_oh:hover {
    background-color: rgba(13, 23, 84, 1)
}

.bgc-darkblue-oh:hover {
    background-color: rgba(13, 23, 84, 1) !important
}

.bgc_darkblue_10_oh:hover {
    background-color: rgba(13, 23, 84, .1)
}

.bgc-darkblue-10-oh:hover {
    background-color: rgba(13, 23, 84, .1) !important
}

.bgc_darkblue_10 {
    background-color: rgba(13, 23, 84, .1)
}

.bgc-darkblue-10 {
    background-color: rgba(13, 23, 84, .1) !important
}

.bgc_darkblue_20 {
    background-color: rgba(13, 23, 84, .2)
}

.bgc-darkblue-20 {
    background-color: rgba(13, 23, 84, .2) !important
}

.bgc_darkblue_30 {
    background-color: rgba(13, 23, 84, .3)
}

.bgc-darkblue-30 {
    background-color: rgba(13, 23, 84, .3) !important
}

.bgc_darkblue_a:after {
    background-color: rgba(13, 23, 84, 1)
}

.bgc-darkblue-a:after {
    background-color: rgba(13, 23, 84, 1) !important
}

.bgc_darkblue_b:before {
    background-color: rgba(13, 23, 84, 1)
}

.bgc-darkblue-b:before {
    background-color: rgba(13, 23, 84, 1) !important
}

.bgc_darkblue {
    background-color: rgba(13, 23, 84, 1)
}

.bgc-darkblue {
    background-color: rgba(13, 23, 84, 1) !important
}

.bgc_lightblue_oh:hover {
    background-color: rgba(29, 85, 150, 1)
}

.bgc-lightblue-oh:hover {
    background-color: rgba(29, 85, 150, 1) !important
}

.bgc_lightblue_10_oh:hover {
    background-color: rgba(29, 85, 150, .1)
}

.bgc-lightblue-10-oh:hover {
    background-color: rgba(29, 85, 150, .1) !important
}

.bgc_lightblue_10 {
    background-color: rgba(29, 85, 150, .1)
}

.bgc-lightblue-10 {
    background-color: rgba(29, 85, 150, .1) !important
}

.bgc_lightblue_20_oh:hover {
    background-color: rgba(29, 85, 150, .2)
}

.bgc-lightblue-20-oh:hover {
    background-color: rgba(29, 85, 150, .2) !important
}

.bgc_lightblue_20 {
    background-color: rgba(29, 85, 150, .2)
}

.bgc-lightblue-20 {
    background-color: rgba(29, 85, 150, .2) !important
}

.bgc_lightblue_30 {
    background-color: rgba(29, 85, 150, .3)
}

.bgc-lightblue-30 {
    background-color: rgba(29, 85, 150, .3) !important
}

.bgc_lightblue {
    background-color: rgba(29, 85, 150, 1)
}

.bgc-lightblue {
    background-color: rgba(29, 85, 150, 1) !important
}

.bgc_warmgray_10_oh:hover {
    background-color: rgba(242, 244, 234, .1)
}

.bgc-warmgray-10-oh:hover {
    background-color: rgba(242, 244, 234, .1) !important
}

.bgc_warmgray_10 {
    background-color: rgba(242, 244, 234, .1)
}

.bgc-warmgray-10 {
    background-color: rgba(242, 244, 234, .1) !important
}

.bgc_warmgray_20 {
    background-color: rgba(242, 244, 234, .2)
}

.bgc-warmgray-20 {
    background-color: rgba(242, 244, 234, .2) !important
}

.bgc_warmgray_30 {
    background-color: rgba(242, 244, 234, .3)
}

.bgc-warmgray-30 {
    background-color: rgba(242, 244, 234, .3) !important
}

.bgc_warmgray_a:after {
    background-color: rgba(242, 244, 234, 1)
}

.bgc-warmgray-a:after {
    background-color: rgba(242, 244, 234, 1) !important
}

.bgc_warmgray_b:before {
    background-color: rgba(242, 244, 234, 1)
}

.bgc-warmgray-b:before {
    background-color: rgba(242, 244, 234, 1) !important
}

.bgc_warmgray {
    background-color: rgba(242, 244, 234, 1)
}

.bgc-warmgray {
    background-color: rgba(242, 244, 234, 1) !important
}

.c_blue_10 {
    color: rgba(98, 130, 186, .1)
}

.c-blue-10 {
    color: rgba(98, 130, 186, .1) !important
}

.c_blue_20 {
    color: rgba(98, 130, 186, .2)
}

.c-blue-20 {
    color: rgba(98, 130, 186, .2) !important
}

.c_blue_30 {
    color: rgba(98, 130, 186, .3)
}

.c-blue-30 {
    color: rgba(98, 130, 186, .3) !important
}

.c_blue_40 {
    color: rgba(98, 130, 186, .4)
}

.c-blue-40 {
    color: rgba(98, 130, 186, .4) !important
}

.c_blue_50 {
    color: rgba(98, 130, 186, .5)
}

.c-blue-50 {
    color: rgba(98, 130, 186, .5) !important
}

.c_blue_60 {
    color: rgba(98, 130, 186, .6)
}

.c-blue-60 {
    color: rgba(98, 130, 186, .6) !important
}

.c_blue_70 {
    color: rgba(98, 130, 186, .7)
}

.c-blue-70 {
    color: rgba(98, 130, 186, .7) !important
}

.c_blue_80 {
    color: rgba(98, 130, 186, .8)
}

.c-blue-80 {
    color: rgba(98, 130, 186, .8) !important
}

.c_blue_90 {
    color: rgba(98, 130, 186, .9)
}

.c-blue-90 {
    color: rgba(98, 130, 186, .9) !important
}

.c_blue_a:after {
    color: rgba(98, 130, 186, 1)
}

.c-blue-a:after {
    color: rgba(98, 130, 186, 1) !important
}

.c_blue_b:before {
    color: rgba(98, 130, 186, 1)
}

.c-blue-b:before {
    color: rgba(98, 130, 186, 1) !important
}

.c_blue_oh:hover {
    color: rgba(98, 130, 186, 1)
}

.c-blue-oh:hover {
    color: rgba(98, 130, 186, 1) !important
}

.c_blue {
    color: rgba(98, 130, 186, 1)
}

.c-blue {
    color: rgba(98, 130, 186, 1) !important
}

.c_darkblue_10 {
    color: rgba(13, 23, 84, .1)
}

.c-darkblue-10 {
    color: rgba(13, 23, 84, .1) !important
}

.c_darkblue_20 {
    color: rgba(13, 23, 84, .2)
}

.c-darkblue-20 {
    color: rgba(13, 23, 84, .2) !important
}

.c_darkblue_30 {
    color: rgba(13, 23, 84, .3)
}

.c-darkblue-30 {
    color: rgba(13, 23, 84, .3) !important
}

.c_darkblue_40 {
    color: rgba(13, 23, 84, .4)
}

.c-darkblue-40 {
    color: rgba(13, 23, 84, .4) !important
}

.c_darkblue_50 {
    color: rgba(13, 23, 84, .5)
}

.c-darkblue-50 {
    color: rgba(13, 23, 84, .5) !important
}

.c_darkblue_60 {
    color: rgba(13, 23, 84, .6)
}

.c-darkblue-60 {
    color: rgba(13, 23, 84, .6) !important
}

.c_darkblue_70 {
    color: rgba(13, 23, 84, .7)
}

.c-darkblue-70 {
    color: rgba(13, 23, 84, .7) !important
}

.c_darkblue_80 {
    color: rgba(13, 23, 84, .8)
}

.c-darkblue-80 {
    color: rgba(13, 23, 84, .8) !important
}

.c_darkblue_90 {
    color: rgba(13, 23, 84, .9)
}

.c-darkblue-90 {
    color: rgba(13, 23, 84, .9) !important
}

.c_darkblue_a:after {
    color: rgba(13, 23, 84, 1)
}

.c-darkblue-a:after {
    color: rgba(13, 23, 84, 1) !important
}

.c_darkblue_b:before {
    color: rgba(13, 23, 84, 1)
}

.c-darkblue-b:before {
    color: rgba(13, 23, 84, 1) !important
}

.c_darkblue_oh:hover {
    color: rgba(13, 23, 84, 1)
}

.c-darkblue-oh:hover {
    color: rgba(13, 23, 84, 1) !important
}

.c_darkblue {
    color: rgba(13, 23, 84, 1)
}

.c-darkblue {
    color: rgba(13, 23, 84, 1) !important
}

.c_lightblue_10 {
    color: rgba(29, 85, 150, .1)
}

.c-lightblue-10 {
    color: rgba(29, 85, 150, .1) !important
}

.c_lightblue_20 {
    color: rgba(29, 85, 150, .2)
}

.c-lightblue-20 {
    color: rgba(29, 85, 150, .2) !important
}

.c_lightblue_30 {
    color: rgba(29, 85, 150, .3)
}

.c-lightblue-30 {
    color: rgba(29, 85, 150, .3) !important
}

.c_lightblue_40 {
    color: rgba(29, 85, 150, .4)
}

.c-lightblue-40 {
    color: rgba(29, 85, 150, .4) !important
}

.c_lightblue_50 {
    color: rgba(29, 85, 150, .5)
}

.c-lightblue-50 {
    color: rgba(29, 85, 150, .5) !important
}

.c_lightblue_60 {
    color: rgba(29, 85, 150, .6)
}

.c-lightblue-60 {
    color: rgba(29, 85, 150, .6) !important
}

.c_lightblue_70 {
    color: rgba(29, 85, 150, .7)
}

.c-lightblue-70 {
    color: rgba(29, 85, 150, .7) !important
}

.c_lightblue_80 {
    color: rgba(29, 85, 150, .8)
}

.c-lightblue-80 {
    color: rgba(29, 85, 150, .8) !important
}

.c_lightblue_90 {
    color: rgba(29, 85, 150, .9)
}

.c-lightblue-90 {
    color: rgba(29, 85, 150, .9) !important
}

.c_lightblue_a:after {
    color: rgba(29, 85, 150, 1)
}

.c-lightblue-a:after {
    color: rgba(29, 85, 150, 1) !important
}

.c_lightblue_b:before {
    color: rgba(29, 85, 150, 1)
}

.c-lightblue-b:before {
    color: rgba(29, 85, 150, 1) !important
}

.c_lightblue_oh:hover {
    color: rgba(29, 85, 150, 1)
}

.c-lightblue-oh:hover {
    color: rgba(29, 85, 150, 1) !important
}

.c_lightblue {
    color: rgba(29, 85, 150, 1)
}

.c-lightblue {
    color: rgba(29, 85, 150, 1) !important
}

.c_warmgray_10 {
    color: rgba(242, 244, 234, .1)
}

.c-warmgray-10 {
    color: rgba(242, 244, 234, .1) !important
}

.c_warmgray_20 {
    color: rgba(242, 244, 234, .2)
}

.c-warmgray-20 {
    color: rgba(242, 244, 234, .2) !important
}

.c_warmgray_30 {
    color: rgba(242, 244, 234, .3)
}

.c-warmgray-30 {
    color: rgba(242, 244, 234, .3) !important
}

.c_warmgray_40 {
    color: rgba(242, 244, 234, .4)
}

.c-warmgray-40 {
    color: rgba(242, 244, 234, .4) !important
}

.c_warmgray_50 {
    color: rgba(242, 244, 234, .5)
}

.c-warmgray-50 {
    color: rgba(242, 244, 234, .5) !important
}

.c_warmgray_60 {
    color: rgba(242, 244, 234, .6)
}

.c-warmgray-60 {
    color: rgba(242, 244, 234, .6) !important
}

.c_warmgray_70 {
    color: rgba(242, 244, 234, .7)
}

.c-warmgray-70 {
    color: rgba(242, 244, 234, .7) !important
}

.c_warmgray_80 {
    color: rgba(242, 244, 234, .8)
}

.c-warmgray-80 {
    color: rgba(242, 244, 234, .8) !important
}

.c_warmgray_90 {
    color: rgba(242, 244, 234, .9)
}

.c-warmgray-90 {
    color: rgba(242, 244, 234, .9) !important
}

.c_warmgray_a:after {
    color: rgba(242, 244, 234, 1)
}

.c-warmgray-a:after {
    color: rgba(242, 244, 234, 1) !important
}

.c_warmgray_b:before {
    color: rgba(242, 244, 234, 1)
}

.c-warmgray-b:before {
    color: rgba(242, 244, 234, 1) !important
}

.c_warmgray {
    color: rgba(242, 244, 234, 1)
}

.c-warmgray {
    color: rgba(242, 244, 234, 1) !important
}

.bd_red {
    border: 1px solid rgba(200, 45, 38, 1)
}

.bd-red {
    border: 1px solid rgba(200, 45, 38, 1) !important
}

.bdc_red {
    border-color: rgba(200, 45, 38, 1)
}

.bdc-red {
    border-color: rgba(200, 45, 38, 1) !important
}

.bgc_red_10_oh:hover {
    background-color: rgba(200, 45, 38, .1)
}

.bgc-red-10-oh:hover {
    background-color: rgba(200, 45, 38, .1) !important
}

.bgc_red_10 {
    background-color: rgba(200, 45, 38, .1)
}

.bgc-red-10 {
    background-color: rgba(200, 45, 38, .1) !important
}

.bgc_red_20 {
    background-color: rgba(200, 45, 38, .2)
}

.bgc-red-20 {
    background-color: rgba(200, 45, 38, .2) !important
}

.bgc_red_30 {
    background-color: rgba(200, 45, 38, .3)
}

.bgc-red-30 {
    background-color: rgba(200, 45, 38, .3) !important
}

.bgc_red {
    background-color: rgba(200, 45, 38, 1)
}

.bgc-red {
    background-color: rgba(200, 45, 38, 1) !important
}

.c_red_10 {
    color: rgba(200, 45, 38, .1)
}

.c-red-10 {
    color: rgba(200, 45, 38, .1) !important
}

.c_red_20 {
    color: rgba(200, 45, 38, .2)
}

.c-red-20 {
    color: rgba(200, 45, 38, .2) !important
}

.c_red_30 {
    color: rgba(200, 45, 38, .3)
}

.c-red-30 {
    color: rgba(200, 45, 38, .3) !important
}

.c_red_40 {
    color: rgba(200, 45, 38, .4)
}

.c-red-40 {
    color: rgba(200, 45, 38, .4) !important
}

.c_red_50 {
    color: rgba(200, 45, 38, .5)
}

.c-red-50 {
    color: rgba(200, 45, 38, .5) !important
}

.c_red_60 {
    color: rgba(200, 45, 38, .6)
}

.c-red-60 {
    color: rgba(200, 45, 38, .6) !important
}

.c_red_70 {
    color: rgba(200, 45, 38, .7)
}

.c-red-70 {
    color: rgba(200, 45, 38, .7) !important
}

.c_red_80 {
    color: rgba(200, 45, 38, .8)
}

.c-red-80 {
    color: rgba(200, 45, 38, .8) !important
}

.c_red_90 {
    color: rgba(200, 45, 38, .9)
}

.c-red-90 {
    color: rgba(200, 45, 38, .9) !important
}

.c_red {
    color: rgba(200, 45, 38, 1)
}

.c-red {
    color: rgba(200, 45, 38, 1) !important
}

.bd_steelblue {
    border: 1px solid rgba(130, 144, 156, 1)
}

.bd-steelblue {
    border: 1px solid rgba(130, 144, 156, 1) !important
}

.bdc_steelblue {
    border-color: rgba(130, 144, 156, 1)
}

.bdc-steelblue {
    border-color: rgba(130, 144, 156, 1) !important
}

.bgc_steelblue_10_oh:hover {
    background-color: rgba(130, 144, 156, .1)
}

.bgc-steelblue-10-oh:hover {
    background-color: rgba(130, 144, 156, .1) !important
}

.bgc_steelblue_10 {
    background-color: rgba(130, 144, 156, .1)
}

.bgc-steelblue-10 {
    background-color: rgba(130, 144, 156, .1) !important
}

.bgc_steelblue_20 {
    background-color: rgba(130, 144, 156, .2)
}

.bgc-steelblue-20 {
    background-color: rgba(130, 144, 156, .2) !important
}

.bgc_steelblue_30 {
    background-color: rgba(130, 144, 156, .3)
}

.bgc-steelblue-30 {
    background-color: rgba(130, 144, 156, .3) !important
}

.bgc_steelblue {
    background-color: rgba(130, 144, 156, 1)
}

.bgc-steelblue {
    background-color: rgba(130, 144, 156, 1) !important
}

.c_steelblue_10 {
    color: rgba(130, 144, 156, .1)
}

.c-steelblue-10 {
    color: rgba(130, 144, 156, .1) !important
}

.c_steelblue_20 {
    color: rgba(130, 144, 156, .2)
}

.c-steelblue-20 {
    color: rgba(130, 144, 156, .2) !important
}

.c_steelblue_30 {
    color: rgba(130, 144, 156, .3)
}

.c-steelblue-30 {
    color: rgba(130, 144, 156, .3) !important
}

.c_steelblue_40 {
    color: rgba(130, 144, 156, .4)
}

.c-steelblue-40 {
    color: rgba(130, 144, 156, .4) !important
}

.c_steelblue_50 {
    color: rgba(130, 144, 156, .5)
}

.c-steelblue-50 {
    color: rgba(130, 144, 156, .5) !important
}

.c_steelblue_60 {
    color: rgba(130, 144, 156, .6)
}

.c-steelblue-60 {
    color: rgba(130, 144, 156, .6) !important
}

.c_steelblue_70 {
    color: rgba(130, 144, 156, .7)
}

.c-steelblue-70 {
    color: rgba(130, 144, 156, .7) !important
}

.c_steelblue_80 {
    color: rgba(130, 144, 156, .8)
}

.c-steelblue-80 {
    color: rgba(130, 144, 156, .8) !important
}

.c_steelblue_90 {
    color: rgba(130, 144, 156, .9)
}

.c-steelblue-90 {
    color: rgba(130, 144, 156, .9) !important
}

.c_steelblue {
    color: rgba(130, 144, 156, 1)
}

.c-steelblue {
    color: rgba(130, 144, 156, 1) !important
}

.bd_orange {
    border: 1px solid rgba(219, 104, 11, 1)
}

.bd-orange {
    border: 1px solid rgba(219, 104, 11, 1) !important
}

.bdc_orange {
    border-color: rgba(219, 104, 11, 1)
}

.bdc-orange {
    border-color: rgba(219, 104, 11, 1) !important
}

.bgc_orange_10_oh:hover {
    background-color: rgba(219, 104, 11, .1)
}

.bgc-orange-10-oh:hover {
    background-color: rgba(219, 104, 11, .1) !important
}

.bgc_orange_10 {
    background-color: rgba(219, 104, 11, .1)
}

.bgc-orange-10 {
    background-color: rgba(219, 104, 11, .1) !important
}

.bgc_orange_20 {
    background-color: rgba(219, 104, 11, .2)
}

.bgc-orange-20 {
    background-color: rgba(219, 104, 11, .2) !important
}

.bgc_orange_30 {
    background-color: rgba(219, 104, 11, .3)
}

.bgc-orange-30 {
    background-color: rgba(219, 104, 11, .3) !important
}

.bgc_orange {
    background-color: rgba(219, 104, 11, 1)
}

.bgc-orange {
    background-color: rgba(219, 104, 11, 1) !important
}

.c_orange_10 {
    color: rgba(219, 104, 11, .1)
}

.c-orange-10 {
    color: rgba(219, 104, 11, .1) !important
}

.c_orange_20 {
    color: rgba(219, 104, 11, .2)
}

.c-orange-20 {
    color: rgba(219, 104, 11, .2) !important
}

.c_orange_30 {
    color: rgba(219, 104, 11, .3)
}

.c-orange-30 {
    color: rgba(219, 104, 11, .3) !important
}

.c_orange_40 {
    color: rgba(219, 104, 11, .4)
}

.c-orange-40 {
    color: rgba(219, 104, 11, .4) !important
}

.c_orange_50 {
    color: rgba(219, 104, 11, .5)
}

.c-orange-50 {
    color: rgba(219, 104, 11, .5) !important
}

.c_orange_60 {
    color: rgba(219, 104, 11, .6)
}

.c-orange-60 {
    color: rgba(219, 104, 11, .6) !important
}

.c_orange_70 {
    color: rgba(219, 104, 11, .7)
}

.c-orange-70 {
    color: rgba(219, 104, 11, .7) !important
}

.c_orange_80 {
    color: rgba(219, 104, 11, .8)
}

.c-orange-80 {
    color: rgba(219, 104, 11, .8) !important
}

.c_orange_90 {
    color: rgba(219, 104, 11, .9)
}

.c-orange-90 {
    color: rgba(219, 104, 11, .9) !important
}

.c_orange {
    color: rgba(219, 104, 11, 1)
}

.c-orange {
    color: rgba(219, 104, 11, 1) !important
}

.ff_brand{ font-weight:normal; font-family: 'NDRSansRegular'}.ff-brand{ font-weight:normal; font-family: 'NDRSansRegular' !important}
.ff_brand_c{ font-weight:normal; font-family: 'NDRSansCondRegular'}.ff-brand-c{ font-weight:normal; font-family: 'NDRSansCondRegular' !important}
