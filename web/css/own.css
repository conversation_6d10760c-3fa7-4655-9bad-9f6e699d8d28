.fillparent {
    height: 100%;
    width: 100%;
}

.fixedheight {
    border: 1px solid #ddd;
    border-top: none;
    padding: 10px;
    height: 600px;
    overflow: scroll;
}

.tox-tinymce {
    border: 1px solid #ced4da !important;
    border-radius: 0 !important;
}

.mentoring-control {
    width: 400px;
}

.profilupdate-control {
    width: 100px;
}

.mentoring-checkbox {

}

.btn_nav_blue {
    background-color: rgba(13, 23, 84, 1) !important;
    border-color: rgba(13, 23, 84, 1) !important;
    color: white;
    min-width: 90px;
    width: 100px;
}

.btn_nav_blue_save {
    background-color: rgba(13, 23, 84, 1) !important;
    border-color: rgba(13, 23, 84, 1) !important;
    color: white;
    min-width: 225px;
    width: 225px;
}
.btn_nav_blue_500px
{
    background-color: rgba(13, 23, 84, 1) !important;
    border-color: rgba(13, 23, 84, 1) !important;
    color: white;
    min-width: 500px;
    width: 500px;
}
.matching_td {
    background-color: #d2ddea;
}

.nonmatching_td {
    background-color: #ffffff;
}

.profil-tr {
    display: flex;
    height: 50px;
    width: 660px;
}
/* Matching bar */
#outer, .progress-bar-outer {
    width: 30px;
    height: 100px;
    border: 2px solid #ccc;
    overflow: hidden;

    position: relative;

    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

#inner, #inner div, .progress-bar-inner, .progress-bar-inner div {
    width: 100%;
    overflow: hidden;
    left: -2px;
    position: absolute;
}

#inner, .progress-bar-inner {
    border: 2px solid #999;
    border-top-width: 0;
    background-color: #999;

    bottom: 0;
    height: 0%;
    
    /* Add color transitions based on percentage */
    transition: background-color 0.5s ease;
}

#inner div, .progress-bar-inner div {
    border: 2px solid orange;
    border-bottom-width: 0;
    background-color: orange;

    top: 0;
    width: 100%;
    height: 5px;
}

/* Profile Details Layout Styles */
.profile-header {
    margin-bottom: 30px;
}

.header-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.home-link {
    font-size: 2rem;
    color: rgba(13, 23, 84, 1);
    text-decoration: none;
}

.navigation-dots {
    display: flex;
    gap: 10px;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ccc;
    border: 2px solid #999;
}

.dot.active {
    background-color: rgba(13, 23, 84, 1);
    border-color: rgba(13, 23, 84, 1);
}

.profile-title {
    font-size: 1.8rem;
    font-weight: bold;
    color: rgba(13, 23, 84, 1);
    margin: 0;
    text-align: center;
}

.profile-content {
    max-width: 1200px;
    margin: 0 auto;
}

.profile-card-container {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.profile-left-section {
    flex: 0 0 200px;
    text-align: center;
}

.profile-image-container {
    position: relative;
    display: inline-block;
}

.profile-image {
    border-radius: 8px;
    border: 2px solid #ddd;
}

.match-percentage-vertical {
    position: absolute;
    right: -40px;
    top: 50%;
    transform: translateY(-50%);
    writing-mode: vertical-rl;
    text-orientation: mixed;
    font-size: 12px;
    color: #666;
}

.match-text {
    margin-bottom: 10px;
    display: block;
}

.profile-name {
    margin-top: 15px;
    font-weight: bold;
    font-size: 1.1rem;
    color: rgba(13, 23, 84, 1);
}

.profile-right-section {
    flex: 1;
}

.profile-info-card {
    background: rgba(29, 85, 150, 0.1);
    border-radius: 8px;
    padding: 20px;
}

.profile-role-header h2 {
    margin: 0 0 20px 0;
    color: rgba(13, 23, 84, 1);
    font-size: 1.5rem;
    font-weight: bold;
}

.profile-details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.profile-detail-item {
    display: flex;
    justify-content: space-between;
}

.detail-label {
    font-weight: bold;
    color: #333;
}

.detail-value {
    color: #666;
}

.profile-interests {
    border-top: 1px solid #ddd;
    padding-top: 15px;
    font-size: 0.95rem;
}

.competencies-values-container {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
}

.competencies-card, .values-card {
    flex: 1;
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.card-title {
    margin: 0 0 20px 0;
    font-size: 1.3rem;
    font-weight: bold;
    color: rgba(13, 23, 84, 1);
}

.competencies-list, .values-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.competency-item, .value-item {
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 0.9rem;
    border: 1px solid #ddd;
}

.competency-item.matching, .value-item.matching {
    background-color: #d2ddea;
    border-color: #b8c9e0;
}

.competency-item.non-matching, .value-item.non-matching {
    background-color: #ffffff;
    border-color: #ddd;
}

.action-buttons-container {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

.btn-back, .btn-contact {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-back {
    background-color: rgba(13, 23, 84, 1);
    color: white;
}

.btn-back:hover {
    background-color: rgba(13, 23, 84, 0.8);
}

.btn-contact {
    background-color: rgba(13, 23, 84, 1);
    color: white;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-contact:hover {
    background-color: rgba(13, 23, 84, 0.8);
    color: white;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-card-container {
        flex-direction: column;
        gap: 20px;
    }

    .profile-left-section {
        flex: none;
    }

    .match-percentage-vertical {
        position: static;
        writing-mode: horizontal-tb;
        text-orientation: initial;
        margin-top: 10px;
        transform: none;
    }

    .profile-details-grid {
        grid-template-columns: 1fr;
    }

    .competencies-values-container {
        flex-direction: column;
        gap: 20px;
    }

    .action-buttons-container {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-back, .btn-contact {
        text-align: center;
    }
}

/* ========================================
   STANDARDIZED FORM STYLES
   ======================================== */

/* Form Container Base Styles */
.form-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 30px;
    margin: 20px auto;
    max-width: 1200px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Form Headers */
.form-container h1,
.form-container h2,
.form-container h3 {
    color: rgba(13, 23, 84, 1);
    font-weight: bold;
    margin-bottom: 20px;
}

.form-container h1 {
    font-size: 1.8rem;
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(13, 23, 84, 0.1);
}

.form-container h2 {
    font-size: 1.5rem;
    margin-bottom: 25px;
}

.form-container h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: rgba(13, 23, 84, 0.8);
}

/* Form Field Styling */
.form-container .form-group {
    margin-bottom: 20px;
}

.form-container label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
    font-size: 0.95rem;
}

.form-container .control-label {
    font-weight: 600;
    color: rgba(13, 23, 84, 1);
}

/* Input Field Styles */
.form-container input[type="text"],
.form-container input[type="password"],
.form-container input[type="email"],
.form-container input[type="number"],
.form-container select,
.form-container textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background-color: #fff;
}

.form-container input:focus,
.form-container select:focus,
.form-container textarea:focus {
    outline: none;
    border-color: rgba(13, 23, 84, 1);
    box-shadow: 0 0 0 3px rgba(13, 23, 84, 0.1);
}

/* Select Dropdown Specific Styles */
.form-container select {
    min-height: 48px;
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
}

/* Textarea Specific Styles */
.form-container textarea {
    min-height: 120px;
    resize: vertical;
    font-family: inherit;
}

/* Checkbox and Radio Styles */
.form-container input[type="checkbox"],
.form-container input[type="radio"] {
    width: auto;
    margin-right: 8px;
    transform: scale(1.2);
}

.form-container .checkbox label,
.form-container .radio label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
    margin-bottom: 10px;
}

/* Form Row Layout */
.form-container .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
    margin-bottom: 20px;
}

.form-container .row > [class*="col-"] {
    padding: 0 15px;
}

/* D-flex Layout Support */
.form-container .d-flex {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.form-container .d-flex:last-child {
    border-bottom: none;
}

.form-container .d-flex h3 {
    margin: 0;
    font-size: 1.1rem;
    color: rgba(13, 23, 84, 1);
    font-weight: 600;
}

/* Button Styles */
.form-container .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    text-align: center;
    min-width: 120px;
}

.form-container .btn_nav_blue,
.form-container .btn-primary {
    background-color: rgba(13, 23, 84, 1);
    color: white;
    border: 2px solid rgba(13, 23, 84, 1);
}

.form-container .btn_nav_blue:hover,
.form-container .btn-primary:hover {
    background-color: rgba(13, 23, 84, 0.9);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 23, 84, 0.3);
    color: white;
    text-decoration: none;
}

.form-container .btn-success {
    background-color: #28a745;
    color: white;
    border: 2px solid #28a745;
}

.form-container .btn-success:hover {
    background-color: #218838;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.form-container .btn-default,
.form-container .btn-secondary {
    background-color: #6c757d;
    color: white;
    border: 2px solid #6c757d;
}

.form-container .btn-default:hover,
.form-container .btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
    color: white;
    text-decoration: none;
}

/* Button Groups */
.form-container .form-group:last-child {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
    text-align: right;
}

.form-container .pull-right {
    float: right;
}

.form-container .pull-left {
    float: left;
}

/* Error and Success States */
.form-container .has-error input,
.form-container .has-error select,
.form-container .has-error textarea {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-container .has-success input,
.form-container .has-success select,
.form-container .has-success textarea {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.form-container .help-block,
.form-container .invalid-feedback {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
}

.form-container .valid-feedback {
    color: #28a745;
    font-size: 0.875rem;
    margin-top: 5px;
}

/* Hint Text */
.form-container .hint,
.form-container .form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 5px;
    font-style: italic;
}

/* Special Form Elements */
.form-container .mentoring-control {
    max-width: 400px;
}

.form-container .profilupdate-control {
    max-width: 200px;
}

/* Card-based Form Sections */
.form-container .card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 20px;
}

.form-container .card-header {
    background: rgba(13, 23, 84, 0.1);
    border-bottom: 1px solid #dee2e6;
    padding: 15px 20px;
    font-weight: 600;
    color: rgba(13, 23, 84, 1);
    border-radius: 8px 8px 0 0;
}

.form-container .card-body {
    padding: 20px;
}

/* Login Form Specific Styles */
.site-login .form-container {
    max-width: 500px;
    margin: 50px auto;
}

.site-login h1 {
    text-align: center;
    color: rgba(13, 23, 84, 1);
    margin-bottom: 30px;
}

/* Checkbox List Styling - One checkbox per row */
.form-container .checkbox-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.form-container .checkbox-list .checkbox {
    flex: none;
    width: 100%;
    margin-right: 0;
    margin-bottom: 0;
}

/* Force all checkboxes to display one per row */
.form-container .checkbox,
.form-container .form-group.field-checkbox,
.form-container .field-checkbox {
    display: block;
    width: 100%;
    margin-bottom: 15px;
    clear: both;
}

/* Ensure checkbox containers don't float */
.form-container .checkbox {
    float: none !important;
    display: block !important;
    width: 100% !important;
    margin-bottom: 15px !important;
}

/* Style checkbox labels to be full width */
.form-container .checkbox label {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 12px 15px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 0;
    cursor: pointer;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.form-container .checkbox label:hover {
    background: #e9ecef;
    border-color: rgba(13, 23, 84, 0.3);
}

.form-container .checkbox input[type="checkbox"] {
    margin-right: 12px;
    margin-left: 0;
    transform: scale(1.2);
}

/* Specific styling for different form sections */
.kompetenzen-form .checkbox,
.profil-form .checkbox,
.tandempartner-form .checkbox {
    display: block;
    width: 100%;
    margin-bottom: 12px;
}

/* Override any Bootstrap or YII2 default checkbox styling */
.form-container .form-group .checkbox,
.form-container .form-group.field-checkbox {
    display: block !important;
    float: none !important;
    width: 100% !important;
    margin-bottom: 15px !important;
}

/* Ensure proper spacing in checkbox groups */
.form-container .checkbox + .checkbox {
    margin-top: 0;
}

/* Style for checked checkboxes */
.form-container .checkbox input[type="checkbox"]:checked + label,
.form-container .checkbox label:has(input[type="checkbox"]:checked) {
    background: rgba(13, 23, 84, 0.1);
    border-color: rgba(13, 23, 84, 0.5);
    color: rgba(13, 23, 84, 1);
    font-weight: 600;
}

/* Additional YII2 ActiveForm checkbox styling */
.form-container .form-group.field-checkbox {
    margin-bottom: 15px !important;
}

.form-container .form-group.field-checkbox .checkbox {
    margin-bottom: 0;
}

/* Ensure proper spacing between checkbox groups */
.form-container .checkbox-list .form-group {
    margin-bottom: 10px;
}

.form-container .checkbox-list .form-group:last-child {
    margin-bottom: 0;
}

/* Override any inline styles that might interfere */
.form-container .checkbox[style] {
    display: block !important;
    width: 100% !important;
    float: none !important;
}

/* Specific overrides for different form types */
.profil-form .checkbox,
.kompetenzen-form .checkbox,
.tandempartner-form .checkbox,
.benutzer-form .checkbox {
    display: block !important;
    width: 100% !important;
    margin-bottom: 15px !important;
    float: none !important;
    clear: both !important;
}

/* Ensure text inputs in checkbox lists are also full width */
.form-container .checkbox-list .form-group input[type="text"] {
    width: 100%;
    margin-top: 10px;
}

/* ========================================
   VERTICAL SELECT LAYOUT STYLES
   ======================================== */

/* Container for vertical select layout */
.form-container .d-flex[style*="flex-direction: column"] {
    display: flex !important;
    flex-direction: column !important;
    gap: 20px;
    margin-bottom: 30px;
}

/* Individual select item styling */
.form-container .select-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.3s ease;
}

.form-container .select-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Select item headers */
.form-container .select-item h3 {
    margin: 0 0 15px 0;
    color: rgba(13, 23, 84, 1);
    font-size: 1.1rem;
    font-weight: 600;
}

/* Select dropdown styling within select items */
.form-container .select-item .form-group {
    margin-bottom: 0;
}

.form-container .select-item select {
    width: 100%;
    max-width: 400px;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    background-color: #fff;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-container .select-item select:focus {
    outline: none;
    border-color: rgba(13, 23, 84, 1);
    box-shadow: 0 0 0 3px rgba(13, 23, 84, 0.1);
}

/* Responsive design for select items */
@media (max-width: 768px) {
    .form-container .select-item {
        padding: 15px;
    }

    .form-container .select-item h3 {
        font-size: 1rem;
        margin-bottom: 12px;
    }

    .form-container .select-item select {
        max-width: 100%;
        padding: 10px 12px;
    }
}

/* Override any existing d-flex styles that might interfere */
.profil-form .d-flex[style*="flex-direction: column"] {
    align-items: stretch !important;
    justify-content: flex-start !important;
}

/* Ensure proper spacing between form sections */
.form-container .d-flex[style*="flex-direction: column"] + .field-group {
    margin-top: 30px;
}

/* Form Field Groups */
.form-container .field-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.form-container .field-group h4 {
    color: rgba(13, 23, 84, 1);
    margin-bottom: 15px;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Required Field Indicator */
.form-container .required label:after {
    content: " *";
    color: #dc3545;
    font-weight: bold;
}

/* Form Navigation */
.form-container .form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #eee;
}

.form-container .form-navigation .btn {
    min-width: 150px;
}

/* Responsive Form Design */
@media (max-width: 768px) {
    .form-container {
        margin: 10px;
        padding: 20px;
    }

    .form-container .row {
        margin: 0;
    }

    .form-container .row > [class*="col-"] {
        padding: 0;
        margin-bottom: 15px;
    }

    .form-container .d-flex {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .form-container .d-flex .col-sm-4,
    .form-container .d-flex .col-sm-6 {
        width: 100%;
        margin-bottom: 10px;
    }

    .form-container .form-navigation {
        flex-direction: column;
        gap: 15px;
    }

    .form-container .form-navigation .btn {
        width: 100%;
    }

    .form-container .pull-left,
    .form-container .pull-right {
        float: none;
        width: 100%;
        margin-bottom: 10px;
    }
}

@media (max-width: 480px) {
    .form-container {
        padding: 15px;
    }

    .form-container h1 {
        font-size: 1.5rem;
    }

    .form-container h2 {
        font-size: 1.3rem;
    }

    .form-container h3 {
        font-size: 1.1rem;
    }

    .form-container input,
    .form-container select,
    .form-container textarea {
        padding: 10px 12px;
    }

    .form-container .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
}

/* ========================================
   SPECIFIC FORM OVERRIDES
   ======================================== */

/* Benutzer Form */
.benutzer-form .checkbox-list {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

/* Profil Form */
.profil-form .d-flex {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

/* Kompetenzen Form */
.kompetenzen-form .checkbox {
    background: #f8f9fa;
    padding: 12px 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin-bottom: 10px;
    transition: background-color 0.3s ease;
}

.kompetenzen-form .checkbox:hover {
    background: #e9ecef;
}

.kompetenzen-form .checkbox input[type="checkbox"]:checked + label {
    color: rgba(13, 23, 84, 1);
    font-weight: 600;
}

/* Tandempartner Form */
.tandempartner-form .d-flex {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid rgba(13, 23, 84, 1);
}

/* Label Manager Form */
.label-manager-form .card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Role Form */
.rolle-form {
    max-width: 600px;
}

/* Configuration Form */
.konfiguration-form {
    max-width: 800px;
}

/* Site Forms */
.site-login,
.site-register {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.site-login .form-container,
.site-register .form-container {
    background: white;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}