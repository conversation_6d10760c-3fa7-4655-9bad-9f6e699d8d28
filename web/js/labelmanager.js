$(document).ready(function () {

    $("#labelmanager-liste").change(function () {
        show_liste();
    });
    show_liste();
});

function show_liste() {
    let liste = $("#labelmanager-liste").val();
    $.post("/label-manager/liste", {liste: liste}, function (data) {
        let response = JSON.parse(data);
        $("#listview").html(response.html);
        $("#listhead").html(response.title);
    });
}

function save_labelmanager_value() {
    $('#lmform').submit();
}

function new_labelmanager_value() {
    location='/label-manager/update?id=0';
}