{"name": "mentoring", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.114", "bootstrap": "^5.3.3", "react": "^18.3.1", "react-bootstrap": "^2.10.5", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^6.27.0", "react-scripts": "5.0.1", "recoil": "^0.7.7", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"prestart": "echo 'Checking Node.js version...' && node -v | grep -q 'v14' || (echo 'Error: Node.js version 14 is required!' && exit 1) && echo 'Checking npm version...' && npm -v || (echo 'Error: npm is not installed or accessible!' && exit 1) && echo 'Checking TypeScript installation...' && tsc -v || (echo 'Error: TypeScript is not installed!' && exit 1) && echo 'All checks passed!'", "dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/css-modules": "^1.0.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1"}}