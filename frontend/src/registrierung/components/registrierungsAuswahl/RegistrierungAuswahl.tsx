import React from "react";
import styles from "./Registrieren.module.css";
import { NavLink } from "react-router-dom";
import  { ReactComponent as MentorIcon } from "./MentorIcon.svg";
import { ReactComponent as MenteeIcon }  from "./MenteeIcon.svg";
import { ReactComponent as MenteeAndMentorIcon }  from "./MenteeAndMentorIcon.svg";
import { ReactComponent as AchtungIcon } from "./AchtungIcon.svg";
import { useNavigate } from 'react-router-dom';
import Header from "../../../layout/header/Header";
import Card from "../../../layout/card/Card";
import Kompetenzen from "../kompetenzen/Kompetenzen";


const RegistrierungAuswahl = () => {
  const navigate = useNavigate();

  const registerMentorHandler = () => {
    navigate('/registrierung', {
      state: {registerArt: 'mentor'}
    });
  }

  const registerMenteeHandler = () => {
    navigate('/registrierung', {
      state: {registerArt: 'mentee'}
    })
  }

  const registerMenteeAndMentorHandler = () => {
    navigate('/registrierung', {
      state: {registerArt: 'menteeAndMentor'}
    })
  }

  return (
    <Header>
      <div className={styles.containerSiteHeader}>
        <NavLink to="/">
          <div className={styles.back}>
            <div>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                height="40px"
                viewBox="0 -960 960 960"
                width="40px"
                fill="#545454"
              >
                <path d="M372.15-268.51 160-480.15l212.15-212.16 24.26 24.26-170.97 170.97H800v33.85H225.44l170.97 170.46-24.26 24.26Z" />
              </svg>
            </div>
            <button className={styles.btnBack}>Zurück</button>
          </div>
        </NavLink>

        <div className={styles.containerSiteTitle}>
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="40px"
              viewBox="0 -960 960 960"
              width="40px"
              fill="#545454"
            >
              <path d="M574.54-120q-19.26 0-36.76-7.31-17.5-7.31-31.52-21.33L334.36-320l20.9-21.9q8.71-8.72 20.22-10.87 11.5-2.15 23.78 1.31l85.35 19.43v-290.28q0-7.23 4.84-12.07 4.83-4.85 12.16-4.85 7.34 0 12.09 4.85 4.76 4.84 4.76 12.07v335.11l-136.97-34.72L529.82-172.2q9.23 9.38 20.44 13.87 11.2 4.48 24.28 4.48H720q42.74 0 72.91-30.17 30.17-30.17 30.17-72.9v-130q0-7.23 4.83-12.08t12.17-4.85q7.33 0 12.09 4.85 4.75 4.85 4.75 12.08v130q0 57.33-39.79 97.13Q777.33-120 720-120H574.54Zm22.95-260v-160.77q0-7.41 4.83-12.17 4.83-4.75 12.17-4.75 7.33 0 12.09 4.84 4.75 4.85 4.75 12.08V-380h-33.84Zm112.71 0v-102.31q0-7.23 4.84-12.07 4.83-4.85 12.16-4.85 7.34 0 12.09 4.85 4.76 4.84 4.76 12.07V-380H710.2Zm9.8 226.15H529.82 720ZM178.46-240q-24.58 0-41.52-16.94Q120-273.88 120-298.46v-443.08q0-24.58 16.94-41.52Q153.88-800 178.46-800h563.08q24.58 0 41.52 16.94Q800-766.12 800-741.54v143.85h-33.85v-143.85q0-9.23-7.69-16.92-7.69-7.69-16.92-7.69H178.46q-9.23 0-16.92 7.69-7.69 7.69-7.69 16.92v443.08q0 9.23 7.69 16.92 7.69 7.69 16.92 7.69h81.39L293.46-240h-115Z" />
            </svg>
          </div>
          <div className={styles.siteTitle}>
            <span>Registrierung auswählen</span>
          </div>
        </div>
      </div>
      <Card>
        <div className={styles.title}>
          <h1>Ich möchte mich einbringen als:</h1>
        </div>
        <div className={styles.container}>
          <div onClick={registerMenteeHandler}>
            <div className={styles.mentee}>
              <div className={styles.menteeIcon}>
                <MenteeIcon  width="150" height="150"/>
              </div>
              <button className={styles.btnMentee}>Mentee</button>
            </div>
          </div>

          <div onClick={registerMentorHandler}>
            <div className={styles.mentor}>
              <div className={styles.mentorIcon}>
                <MentorIcon  width="150" height="150"/>
              </div>
              <button className={styles.btnMentor}>Mentor*in</button>
            </div>
          </div>

          <div onClick={registerMenteeAndMentorHandler}>
            <div className={styles.mentorAndMentee}>
              <div className={styles.mentorAndMenteeIcon}>
                <MenteeAndMentorIcon  width="150" height="150"/>
              </div>
              <button className={styles.btnMentorAndMentee}>
                Mentee & Mentor*in
              </button>
            </div>
          </div>
        </div>
        <div className={styles.info}>
          <div className={styles.achtungIcon}>
            <AchtungIcon  width="100" height="100"/>
          </div>
          <div className={styles.para}>
            <p>
              Wenn du dich registrierst, erklärst du dich dazu bereit, an einem
              6 Monate Generationen- Mentoring-Programm und an einem
              Präsenz-Auftakttreffen teilzunehmen.
            </p>
          </div>
        </div>
      </Card>
      </Header>
  );
};

export default RegistrierungAuswahl;
