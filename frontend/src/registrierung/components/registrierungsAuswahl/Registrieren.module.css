.registerAuswahl{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  height: 80vh;
}

.registerContainer {
  display: flex;
  justify-content: center;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  /* margin-top: 50px; */
}

/* .register {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 1550px;
  height: 730px;
  border: none;
  border-radius: 10px;
  margin-top: 0px;
  background-color: #ffffff;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
} */

.navBar {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgb(15 30 106);
  width: 1550px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  /* padding: 15px; */
  border: none;
}

.logo {
  padding-left: 20px;
}

.logo h1 {
  color: #ffffff;
}

.logout {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  /* padding: 10px; */
  /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */
  border-radius: 10px;
  margin-right: 20px;
  /* border: 1px solid #959595; */
  /* width: 500px; */
}

.btnLogout {
  cursor: pointer;
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  background: none;
  border: none;
  padding-left: 10px;
}

.logoutIcon {
  height: 35px;
  width: 35px;
  color: #ffffff;
  padding-right: 10px;
}

.containerSiteHeader {
  width: 1550px;
  display: flex;
  /* flex-direction: column; */
  justify-content: space-between;
  align-items: center;
  /* border: 1px solid seagreen; */
  margin-top: 10px;
}

.back {
  display: flex;
  justify-content: center;
  align-items: center;
  /* margin-top: 50px; */
}

.btnBack {
  cursor: pointer;
  font-size: 24px;
  color: #545454;
  background: none;
  border: none;
  /* border-radius: 5px; */
  padding: 10px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.containerSiteTitle {
  margin-top: 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 340px;
  padding: 15px;
  padding-right: 600px;
  /* border: 2px solid red; */
}

.siteTitle span {
  /* padding-left:15px; */
  font-size: 25px;
  color: #545454;
  /* box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.title {
  width: 1200px;
}

.title h1 {
  font-size: 35px;
  color: #00206a;
  text-align: left;
}

.container {
  display: flex;
  justify-content: space-between;
  width: 1200px;
  margin-top: 50px;
}

.mentor {
  cursor: pointer;
  background-color: rgb(245 249 255);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  height: 200px;
}

.mentor:hover {
  text-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.mentorIcon {
  height: 180px;
  width: 310px;
}

.btnMentor {
  cursor: pointer;
  font-size: 30px;
  color: #314a85;
  background: none;
  border: none;
}

.mentee {
  cursor: pointer;
  background-color: rgb(245 249 255);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  height: 200px;
}

.mentee:hover {
  text-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.menteeIcon {
  height: 180px;
  width: 300px;
}

.btnMentee {
  cursor: pointer;
  font-size: 25px;
  color: #314a85;
  background: none;
  border: none;
}

.mentorAndMentee {
  cursor: pointer;
  background-color: rgb(245 249 255);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  height: 200px;
}

.mentorAndMentee:hover {
  text-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.mentorAndMenteeIcon {
  height: 180px;
  width: 300px;
}

.btnMentorAndMentee {
  cursor: pointer;
  font-size: 25px;
  color: #314a85;
  background: none;
  border: none;
}

.info {
  display: flex;
  justify-content: space-between;
  width: 1190px;
  margin-top: 80px;
}

.para {
  border: 2px solid red;
  border-radius: 15px;
  width: 950px;
  text-align: justify;
  padding: 10px;
  line-height: 1.5;
  height: auto;
  color: #314a85;
}
