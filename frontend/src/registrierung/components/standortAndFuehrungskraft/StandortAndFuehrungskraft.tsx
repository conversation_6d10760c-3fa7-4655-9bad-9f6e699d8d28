import React, { useEffect, useState } from "react";
import { useRecoilState } from 'recoil';
import styles from "../../../styles/ShareRegistrierung.module.css";
import Form from 'react-bootstrap/Form';
import Header from "../../../layout/header/Header";
import CardHeader from "../../../layout/cardHeader/CardHeader";
import Card from "../../../layout/card/Card";
import { fetchGeschlecht, fetchStandort } from "../../../Utilities/apiUtils";
import { useHandleUnauthorized } from "../../../Utilities/customHooks";
import { selectedFuehrungsKraftState, selectedStandortState } from "../../../recoil/recoilState";

interface Standort {
    id: number,
    standort: string,
}

interface StandortAndFuehrungskraftProps {
    btnBackHandler: () => void,
    btnToEigenschaften: () => void,
}

const StandortAndFuehrungskraft: React.FC<StandortAndFuehrungskraftProps> =  ({btnBack<PERSON>and<PERSON>, btnToEigenschaften}) => {
    const unauthorized = useHandleUnauthorized();
    // useRecoilState
    const[selectedGlobalFuehrungsKraft,setSelectedGlobalFuehrungsKraft] = useRecoilState(selectedFuehrungsKraftState);
    const[selectedGlobalStandort,setSelectedGlobalStandort] = useRecoilState(selectedStandortState);

    // useState
    const [standortList, setStandortList] = useState<Standort[]>([]);

    useEffect(() => {
        const fetch = async () => {
            const standortData = await fetchStandort();

            if(standortData === "UNAUTHORIZED") {
                return unauthorized
            } else {
                setStandortList(standortData);
            }
        }

        fetch();

    })

    const changeRadioStandortHandler = (id: number) => {
        setSelectedGlobalStandort([id]);
    } 

    return(
        <Header>
            <CardHeader title="Führungskraft und Standort" 
            svg={
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="40px"
                    viewBox="0 -960 960 960"
                    width="40px"
                    fill="#545454"
                >
                    <path d="m421.69-401.9-98.87-98.87q-7.23-7.23-17.7-7.09-10.48.14-18.09 7.76-7.62 7.61-7.62 17.89 0 10.29 7.62 17.9l112.74 112.08q9.34 9.61 21.78 9.61 12.45 0 21.81-9.61l232.95-232.69q7.23-7.23 7.42-17.71.19-10.47-7.42-18.09-7.62-7.61-18.03-7.61t-17.77 7.61L421.69-401.9ZM202.57-140q-25.79 0-44.18-18.39T140-202.57v-554.86q0-25.79 18.39-44.18T202.57-820h554.86q25.79 0 44.18 18.39T820-757.43v554.86q0 25.79-18.39 44.18T757.43-140H202.57Zm0-50.26h554.86q4.62 0 8.47-3.84 3.84-3.85 3.84-8.47v-554.86q0-4.62-3.84-8.47-3.85-3.84-8.47-3.84H202.57q-4.62 0-8.47 3.84-3.84 3.85-3.84 8.47v554.86q0 4.62 3.84 8.47 3.85 3.84 8.47 3.84Zm-12.31-579.48v579.48-579.48Z" />
                </svg>
            }
            />
            <Card>
            <Form className={styles.form}>
                <div className={styles.title}>
                    <h1>Ich bin Führungskraft:</h1>
                </div>
                <div className={styles.container}>
                <div className={styles.checkboxContainer}>
                    <Form.Check
                        type="radio"
                        value="1"
                        label="Ja"
                        id="fuehrungskraft-ja"
                        checked={selectedGlobalFuehrungsKraft === true}
                        onChange={() => setSelectedGlobalFuehrungsKraft(true)}
                        name="fuehrungskraft"
                        className={styles.checkbox}
                    />
                </div>
                <div className={styles.checkboxContainer}>
                    <Form.Check
                        type="radio"
                        value="0"
                        label="Nein"
                        id="fuehrungskraft-nein"
                        checked={selectedGlobalFuehrungsKraft === false}
                        onChange={() => setSelectedGlobalFuehrungsKraft(false)}
                        name="fuehrungskraft"
                        className={styles.checkbox}
                    />
                </div>
                </div>




            <div className={styles.secondTitle}>
                <h1>Mein Standort ist:</h1>
            </div>
            <div className={styles.container}>
                {standortList.map((standort) => {
                const isChecked: boolean = selectedGlobalStandort.includes(standort.id);
                // console.log(isChecked);
                return(
                    <div key={standort.id} className={styles.checkboxContainer}>
                        <Form.Check
                            type="radio"
                            value={standort.id}
                            label={standort.standort}
                            id={`standort-${standort.id}`}
                            checked={isChecked}
                            onChange={() => changeRadioStandortHandler(standort.id)}
                            name="standort"
                            className={styles.checkbox}
                        />
                    </div>);
                })}
            </div>
        </Form>
        <div className={styles.btnsContainer}>
                <div className={styles.back} onClick={btnBackHandler}>
                    <button type="button" className={styles.btnBack}>Zurück</button>
                </div>
                <div className={styles.next} onClick={btnToEigenschaften}>
                    <button type="submit" className={styles.btnNext}>Weiter</button>
                </div>
            </div>
            </Card>
        </Header>
    )
}

export default StandortAndFuehrungskraft;