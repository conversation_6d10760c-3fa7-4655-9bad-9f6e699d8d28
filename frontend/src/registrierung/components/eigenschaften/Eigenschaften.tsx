import React, { useEffect, useState } from "react";
import { useRecoilState } from 'recoil';
import { selectedEigenschaftState } from "../../../recoil/recoilState"
import styles from "../../../styles/ShareRegistrierung.module.css";
import Form from "react-bootstrap/Form";
import { fetchEigenschaften } from "../../../Utilities/apiUtils";
import { useLocation } from "react-router-dom";
import Header from "../../../layout/header/Header";
import Card from "../../../layout/card/Card";
import CardHeader from "../../../layout/cardHeader/CardHeader";
import { useHandleUnauthorized } from "../../../Utilities/customHooks";

interface EigenschaftenProps {
    btnBackHandler: () => void,
    btnToHobbies: () => void,
}


interface Eigenschaften {
    id: number,
    eigenschaft: string,
}

const Eigenschaften: React.FC<EigenschaftenProps> = ({btn<PERSON>ack<PERSON><PERSON><PERSON>, btnToHobbies}) => {
    const unauthorized = useHandleUnauthorized();
// useState
  const [eigenschaftenList, setEigenschaftenList] = useState<Eigenschaften[]>([]);
//   useRecoilState
  const [selectedGlobalEigenschaft, setSelectedGlobalEigenschaft] = useRecoilState(selectedEigenschaftState);

  const handleCheckboxChange = (id: number) => {
    setSelectedGlobalEigenschaft((prev) =>
      prev.includes(id) ? prev.filter((eigenschaftId) => eigenschaftId !== id) : [...prev, id]
    );
  };  

  useEffect(() => {
   
    const fetch = async () => {
        const eigenschaftData = await fetchEigenschaften();

        if(eigenschaftData === "UNAUTHORIZED") {
            return unauthorized
        } else {
            setEigenschaftenList(eigenschaftData);
        }
    };
  
    fetch();
  
  }, []);
  
 

    return (
      <Header>
        <CardHeader 
        // btnBackHandler={btnBackHandler}
        title="Eigenschaften"
        svg={
          <svg
              xmlns="http://www.w3.org/2000/svg"
              height="40px"
              viewBox="0 -960 960 960"
              width="40px"
              fill="#545454"
          >
              <path d="m421.69-401.9-98.87-98.87q-7.23-7.23-17.7-7.09-10.48.14-18.09 7.76-7.62 7.61-7.62 17.89 0 10.29 7.62 17.9l112.74 112.08q9.34 9.61 21.78 9.61 12.45 0 21.81-9.61l232.95-232.69q7.23-7.23 7.42-17.71.19-10.47-7.42-18.09-7.62-7.61-18.03-7.61t-17.77 7.61L421.69-401.9ZM202.57-140q-25.79 0-44.18-18.39T140-202.57v-554.86q0-25.79 18.39-44.18T202.57-820h554.86q25.79 0 44.18 18.39T820-757.43v554.86q0 25.79-18.39 44.18T757.43-140H202.57Zm0-50.26h554.86q4.62 0 8.47-3.84 3.84-3.85 3.84-8.47v-554.86q0-4.62-3.84-8.47-3.85-3.84-8.47-3.84H202.57q-4.62 0-8.47 3.84-3.84 3.85-3.84 8.47v554.86q0 4.62 3.84 8.47 3.85 3.84 8.47 3.84Zm-12.31-579.48v579.48-579.48Z" />
          </svg>
      }
        />
          
       
      <Card>
      {/* <div className={styles.formContainer}> */}
      <div className={styles.formContainer}>
        <Form className={styles.form}>
          <div className={styles.title}>
          <h1>Das ist mir wichtig:</h1>
          </div>
          <div className={styles.container}>
          {eigenschaftenList.map((eigenschaft) => {
            const isChecked = selectedGlobalEigenschaft.includes(eigenschaft.id);
            
            return (
              <div key={eigenschaft.id} className={styles.checkboxContainer}>
                <Form.Check
                  type="checkbox"
                  value={eigenschaft.id}
                  label={eigenschaft.eigenschaft}
                  id={`default-${eigenschaft.id}`}
                  name="eigenschaft"
                  checked={isChecked}
                  onChange={() => handleCheckboxChange(eigenschaft.id)}
                  className={styles.checkbox}
                />
              </div>
            );
          })}
          </div>
        </Form>
      </div>
      <div className={styles.btnsContainer}>
            <div className={styles.back} onClick={btnBackHandler}>
              <div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="40px"
                  viewBox="0 -960 960 960"
                  width="40px"
                  fill="#545454"
                >
                  <path d="M372.15-268.51 160-480.15l212.15-212.16 24.26 24.26-170.97 170.97H800v33.85H225.44l170.97 170.46-24.26 24.26Z" />
                </svg>
              </div>
              <button className={styles.btnBack}>Zurück</button>
            </div>


            <div className={styles.next} onClick={btnToHobbies}>
              <button type="submit" className={styles.btnNext}>Weiter</button>
              <div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="40px"
                  viewBox="0 -960 960 960"
                  width="40px"
                  fill="#ffffff"
                >
                  <path d="M684.16-454.87H205.13q-10.87 0-18-7.14Q180-469.14 180-480q0-10.87 7.13-18 7.13-7.13 18-7.13h479.03L462.1-726.92q-7.18-7.49-7.39-17.53-.22-10.04 7.6-17.75 7.82-7.72 17.69-7.82 9.87-.11 17.69 7.71l260.44 260.44q4.89 4.9 7.13 10.21 2.23 5.32 2.23 11.69 0 6.38-2.23 11.66-2.24 5.28-7.13 10.18L497.69-197.69q-7.23 7.23-17.34 7.42-10.12.19-18.04-7.42-7.82-7.93-7.82-17.85 0-9.92 7.82-17.49l221.85-221.84Z" />
                </svg>
              </div>
            </div>
          </div>
      </Card>
</Header>
  );
};

export default Eigenschaften;

