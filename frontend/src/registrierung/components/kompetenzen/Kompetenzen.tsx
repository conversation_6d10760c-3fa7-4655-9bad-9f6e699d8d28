import React, { useEffect, useState } from "react";
import { useRecoilState, useRecoilValue } from 'recoil';
import { selectedMentorState, selectedMenteeState } from "../../../recoil/recoilState"
import styles from "../../../styles/ShareRegistrierung.module.css";
import Form from "react-bootstrap/Form";
import { fetchKompetenzenList } from "../../../Utilities/apiUtils";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import Header from "../../../layout/header/Header";
import Card from "../../../layout/card/Card";
import CardHeader from "../../../layout/cardHeader/CardHeader";

interface KompetenzenProps {
  btnNextToGeneration: () => void;
  showMenteeKompetenzen: boolean;
  currentRegisterArt: string;
}


interface kompetenzen {
  id: number, 
  kompetenz: string
}

const Kompetenzen: React.FC<KompetenzenProps> = ({btnNextToGeneration, showMenteeKompetenzen, currentRegisterArt}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [kompetenzenList, setKompetenzenList] = useState<kompetenzen[]>([]);
  const [registerKompetenzenMenteeAndMentor, setRegisterKompetenzenMenteeAndMentor] = useState<boolean>(false);
  const [art, setArt] = useState<string>("")
  const [selectedKompetenzenMentee, setSelectedKompetenzenMentee] = useState<number[]>([]);
  const [selectedGlobalMentor, setSelectedGlobalMentor] = useRecoilState(selectedMentorState);
  const [selectedGlobalMentee, setSelectedGlobalMentee] = useRecoilState(selectedMenteeState);

console.log(selectedGlobalMentor);
  const handleCheckboxChange = (id: number) => {
    setSelectedGlobalMentor((prev) =>
      prev.includes(id) ? prev.filter((kompetenzId) => kompetenzId !== id) : [...prev, id]
    );
  };  

  const handleCheckboxMenteeChange = (id: number) => {
    setSelectedGlobalMentee((prev) =>
      prev.includes(id) ? prev.filter((kompetenzId) => kompetenzId !== id) : [...prev, id]
    );
  };  
  
  const csrfToken = localStorage.getItem('csrfToken');

  console.log(csrfToken);


  useEffect(() => {
   
  
    const fetchBenutzerKompetenzen = async () => {
      try {
        const res = await fetch('http://localhost:8050/benutzer-kompetenzen/create-mentor', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken ?? '',
          },
          credentials: 'include',
        });
  
        const result = await res.json();
        console.log("Benutzerkompetenzen:", result);

        // if(result.benutzerKompetenzen != null) {
        //   navigate("/")
        // }
  
      } catch (error) {
        console.error(error);
      }
    };
  
    const fetchKompetenzen = async () => {
      try {
        const data = await fetchKompetenzenList();
        setKompetenzenList(data);
      } catch (error) {
        console.error(error);
      }
    };
  
    fetchBenutzerKompetenzen();
    fetchKompetenzen();
  
    const registerArt = location.state?.registerArt || "";
    // setCurrentRegisterArt(registerArt);
    console.log("inside use Effect", currentRegisterArt);
    if (showMenteeKompetenzen) {
      setRegisterKompetenzenMenteeAndMentor(true);
    } else {
      setRegisterKompetenzenMenteeAndMentor(false);
    }
  
  }, [showMenteeKompetenzen, currentRegisterArt]);
  
  console.log("out of the useEffect", currentRegisterArt);


  const submitHandler = async (e: React.FormEvent<HTMLFormElement>) => {
    // const csrfToken = getCsrfTokenFromCookie();
    e.preventDefault();
  
    // const form = e.target as HTMLFormElement;
    // const formElements = Array.from(form.elements) as HTMLInputElement[];
    // const selectedKompetenzen: number[] = [];
  
    // formElements.forEach((element) => {
    //   if (element.name === "kompetenz" && element.checked) {
    //     selectedKompetenzen.push(Number(element.value));
    //   }
    // });

    // let newArt:string = "";
    

    // if(currentRegisterArt === "menteeAndMentor") {
    //   if(!registerKompetenzenMenteeAndMentor) {
    //     newArt = "mentor";
    //     // setArtMentors(selectedKompetenzen)
    //     setSelectedGlobalMentor(selectedGlobalMentor)
    //     // selectedGlobalMentor
    //     setArt(newArt);
    //     setRegisterKompetenzenMenteeAndMentor(true)
    //   } else {
    //     newArt = "mentee"
    //     setSelectedGlobalMentee(selectedGlobalMentee);
        
    //     setArt(newArt);
    //   }
    // } else if (currentRegisterArt === "mentor") {
    //   newArt = "mentor";
    //   // setSelectedGlobalMentor(selectedKompetenzen)
    //   setSelectedGlobalMentor(selectedGlobalMentor)

    //   setArt(newArt);

    //   // const btnNext = () => {
    //   //   setBtnNextToGeneration(true)
    //   // }
    // } else if (currentRegisterArt === "mentee") {
    //   newArt = "mentee"
    //   setSelectedGlobalMentee(selectedGlobalMentee);
      
    //   setArt(newArt);
    // }
  
    // console.log("Ausgewählte Mentors:", selectedGlobalMentor);
    // console.log("Ausgewählte Mentees:", selectedGlobalMentee);
    // console.log("Art:", newArt);
  
    // try {
    //   const response = await fetch('http://localhost:8050/benutzer-kompetenzen/create-mentor', {
    //     method: 'POST',
    //     headers: {
    //       'Content-Type': 'application/json',
    //       'X-CSRF-Token': csrfToken ?? '',
    //     },
    //     credentials: 'include',
    //     body: JSON.stringify({
    //       kompetenzen: selectedKompetenzen,
    //       arts: newArt
    //     }),
    //   });

    //   console.log(response);


    //   if (response.status === 401) {
    //     localStorage.removeItem('csrfToken');
    //     navigate("/login")
    //   }
  
    //   const result = await response.json();
  
    //   if (!response.ok) {
       
    //     throw new Error(`Fehler: ${result.message || response.statusText}`);
    //   }

    //   console.log(result);
    // } catch (error) {
    //   console.error("Fehler beim Erstellen neuer Daten:", error);
    // }
  };
  
  
    const btnBackHandler = () => {
      if(!registerKompetenzenMenteeAndMentor) {
          navigate("/register-auswahl")
        } else if (registerKompetenzenMenteeAndMentor){
          setRegisterKompetenzenMenteeAndMentor(false)
        }
      }

      console.log("Mentor", selectedGlobalMentor)
      console.log("Mentee", selectedGlobalMentee)

    return (
      <Header>
        <CardHeader 
        // btnBackHandler={btnBackHandler}
        title={currentRegisterArt === "mentor" && <span>Registrierung Mentor*in</span> || currentRegisterArt === "mentee" && <span>Registrierung Mentee</span>}
        svg={
          <svg
              xmlns="http://www.w3.org/2000/svg"
              height="40px"
              viewBox="0 -960 960 960"
              width="40px"
              fill="#545454"
          >
              <path d="m421.69-401.9-98.87-98.87q-7.23-7.23-17.7-7.09-10.48.14-18.09 7.76-7.62 7.61-7.62 17.89 0 10.29 7.62 17.9l112.74 112.08q9.34 9.61 21.78 9.61 12.45 0 21.81-9.61l232.95-232.69q7.23-7.23 7.42-17.71.19-10.47-7.42-18.09-7.62-7.61-18.03-7.61t-17.77 7.61L421.69-401.9ZM202.57-140q-25.79 0-44.18-18.39T140-202.57v-554.86q0-25.79 18.39-44.18T202.57-820h554.86q25.79 0 44.18 18.39T820-757.43v554.86q0 25.79-18.39 44.18T757.43-140H202.57Zm0-50.26h554.86q4.62 0 8.47-3.84 3.84-3.85 3.84-8.47v-554.86q0-4.62-3.84-8.47-3.85-3.84-8.47-3.84H202.57q-4.62 0-8.47 3.84-3.84 3.85-3.84 8.47v554.86q0 4.62 3.84 8.47 3.85 3.84 8.47 3.84Zm-12.31-579.48v579.48-579.48Z" />
          </svg>
      }
        />
          
       
        {!registerKompetenzenMenteeAndMentor && (currentRegisterArt === "menteeAndMentor" || currentRegisterArt === "mentor" ) && (
      <Card>
      {/* <div className={styles.formContainer}> */}
      <div className={styles.formContainer}>
        <Form className={styles.form} onSubmit={submitHandler}>
        <div className={styles.title}>
          <h1>Meine Erfahrungskompetenzen sind:</h1>
        </div>
          <div className={styles.container}>
          {kompetenzenList.map((kompetenz) => {
            const isChecked = (
              (selectedGlobalMentor && 
                (currentRegisterArt === "mentor" || currentRegisterArt === "menteeAndMentor") && 
                selectedGlobalMentor.includes(kompetenz.id)
              )
            );
            
            return (
              <div key={kompetenz.id} className={styles.checkboxContainer}>
                <Form.Check
                  type="checkbox"
                  value={kompetenz.id}
                  label={kompetenz.kompetenz}
                  id={`default-${kompetenz.id}`}
                  name="kompetenz"
                  checked={isChecked}
                  onChange={() => handleCheckboxChange(kompetenz.id)}
                  className={styles.checkbox}
                />
              </div>
            );
          })}
          </div>
        </Form>
      </div>
      <div className={styles.btnsContainer}>
            <div className={styles.back} onClick={btnBackHandler}>
              <div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="40px"
                  viewBox="0 -960 960 960"
                  width="40px"
                  fill="#545454"
                >
                  <path d="M372.15-268.51 160-480.15l212.15-212.16 24.26 24.26-170.97 170.97H800v33.85H225.44l170.97 170.46-24.26 24.26Z" />
                </svg>
              </div>
              <button className={styles.btnBack}>Zurück</button>
            </div>


            <div className={styles.next} onClick={currentRegisterArt === "mentor" ? btnNextToGeneration : () => setRegisterKompetenzenMenteeAndMentor(true)}>
              <button className={styles.btnNext}>Weiter</button>
              <div>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="40px"
                  viewBox="0 -960 960 960"
                  width="40px"
                  fill="#ffffff"
                >
                  <path d="M684.16-454.87H205.13q-10.87 0-18-7.14Q180-469.14 180-480q0-10.87 7.13-18 7.13-7.13 18-7.13h479.03L462.1-726.92q-7.18-7.49-7.39-17.53-.22-10.04 7.6-17.75 7.82-7.72 17.69-7.82 9.87-.11 17.69 7.71l260.44 260.44q4.89 4.9 7.13 10.21 2.23 5.32 2.23 11.69 0 6.38-2.23 11.66-2.24 5.28-7.13 10.18L497.69-197.69q-7.23 7.23-17.34 7.42-10.12.19-18.04-7.42-7.82-7.93-7.82-17.85 0-9.92 7.82-17.49l221.85-221.84Z" />
                </svg>
              </div>
            </div>
          </div>
      </Card>
  )}

        {((registerKompetenzenMenteeAndMentor && currentRegisterArt === "menteeAndMentor") || 
          (!registerKompetenzenMenteeAndMentor && currentRegisterArt === "mentee")) &&  (
            <Card>
            <div className={styles.formContainer}>
              <Form className={styles.form} onSubmit={submitHandler}>
              <div className={styles.title}>
                {<h1>Meine Wunschkompetenzen sind:</h1>}
              </div>
                <div className={styles.container}>
                {kompetenzenList.map((kompetenz) => {
                  const isChecked = 
                  (selectedGlobalMentee && 
                    selectedGlobalMentee.includes(kompetenz.id)
                  );
                  
                  
                  return(
                    <div key={kompetenz.id} className={styles.checkboxContainer}>
                      <Form.Check
                        type="checkbox"
                        value={kompetenz.id}
                        label={kompetenz.kompetenz}
                        id={`default-${kompetenz.id}`}
                        name="kompetenz"
                        checked={isChecked}
                        onChange={() => handleCheckboxMenteeChange(kompetenz.id)}
                        className={styles.checkbox}
                      />
                    </div>)}
                  )}
                  </div>
              </Form>
          </div>
          <div className={styles.btnsContainer}>
                  <div className={styles.back} onClick={btnBackHandler}>
                    <div>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        height="40px"
                        viewBox="0 -960 960 960"
                        width="40px"
                        fill="#545454"
                      >
                        <path d="M372.15-268.51 160-480.15l212.15-212.16 24.26 24.26-170.97 170.97H800v33.85H225.44l170.97 170.46-24.26 24.26Z" />
                      </svg>
                    </div>
                    <button className={styles.btnBack}>Zurück</button>
                  </div>
          
          
                  <div className={styles.next} onClick={btnNextToGeneration}>
                    <button className={styles.btnNext}>Weiter</button>
                    <div>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        height="40px"
                        viewBox="0 -960 960 960"
                        width="40px"
                        fill="#ffffff"
                      >
                        <path d="M684.16-454.87H205.13q-10.87 0-18-7.14Q180-469.14 180-480q0-10.87 7.13-18 7.13-7.13 18-7.13h479.03L462.1-726.92q-7.18-7.49-7.39-17.53-.22-10.04 7.6-17.75 7.82-7.72 17.69-7.82 9.87-.11 17.69 7.71l260.44 260.44q4.89 4.9 7.13 10.21 2.23 5.32 2.23 11.69 0 6.38-2.23 11.66-2.24 5.28-7.13 10.18L497.69-197.69q-7.23 7.23-17.34 7.42-10.12.19-18.04-7.42-7.82-7.93-7.82-17.85 0-9.92 7.82-17.49l221.85-221.84Z" />
                      </svg>
                    </div>
                  </div>
                </div>
            </Card>
    )}
</Header>
  );
};

export default Kompetenzen;

