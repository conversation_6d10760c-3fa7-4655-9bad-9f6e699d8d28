import React, { useState, useEffect } from "react";
import Kompetenzen from "../components/kompetenzen/Kompetenzen";
import GenerationenAndTaetigkeit from "../components/generationenAndTaetigkeit/GenerationenAndTaetigkeit";
import { useLocation, useNavigate } from "react-router-dom";
import StandortAndFuehrungskraft from "../components/standortAndFuehrungskraft/StandortAndFuehrungskraft";
import Geschlecht from "../components/geschlecht/Geschlecht";
import Eigenschaften from "../components/eigenschaften/Eigenschaften";
import Hobbies from "../components/hobbies/Hobbies";
import PartnerPrefernces from "../../partnerPreferences/page/PartnerPrefernces";
import { useRecoilState, useRecoilValue } from "recoil";
import { globalGeschlechtState, registrierungState, kompetenzenState } from "../../recoil/recoilState";

const Registrieren: React.FC = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const [kompetenzen, setKompetenzen] = useRecoilState(kompetenzenState);
    // const [kompetenzen, setKompetenzen] = useState<boolean>(false);
    const [generation, setGeneration] = useState<boolean>(false);
    const [showMenteeKompetenzen, setShowMenteeKompetenzen] = useState<boolean>(false);
    const [currentRegisterArt, setCurrentRegisterArt] = useState<string>(
        location.state?.registerArt || ""
      );
    const [standortAndFuehrungskraft, setStandortAndFuehrungskraft] = useState<boolean>(false);
    const [geschlecht, setGeschlecht] = useState<boolean>(false);
    const [eigenschaft, setEigenschaft] = useState<boolean>(false);
    const [hobbies, setHobbies] = useState<boolean>(false);
    const [partnerPrefernces, setPartnerPrefernces] = useState<boolean>(false); 

    const [globalGeschlecht, setGlobalGeschlecht] = useRecoilState(globalGeschlechtState);
    const [registrierungGlobal, setRegistrierungGlobal] = useRecoilState(registrierungState)
    // const registrierungGlobal = useRecoilValue(registrierungState);


    useEffect(() => {
      const isReloaded = sessionStorage.getItem('isReloaded');
  
      if (isReloaded === 'true') {
        navigate('/register-auswahl');
      } else {
        sessionStorage.setItem('isReloaded', 'true');
      }
    }, [navigate]);
  
    useEffect(() => {
      const handleBeforeUnload = (event: BeforeUnloadEvent) => {
        const message = 'Ihre Eingaben gehen verloren, wenn Sie die Seite verlassen.';
        event.preventDefault();
        event.returnValue = message;
        return message;
      };
  
      window.addEventListener('beforeunload', handleBeforeUnload);
  
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }, []);
  
    useEffect(() => {
      return () => {
        sessionStorage.removeItem('isReloaded');
      };
    }, []);

    // Btns Back

    const btnBackToKompetenzenHandler = (showMenteeFirst: boolean = false) => {
      setKompetenzen(true);
      setGeneration(false);
      setHobbies(false)
      setStandortAndFuehrungskraft(false);
      if(currentRegisterArt === "menteeAndMentor") {
        setShowMenteeKompetenzen(showMenteeFirst);
    }
    };

    const btnBackToGenAndTaetigkeitHandler = () => {
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setHobbies(false)
      setEigenschaft(false);
      setStandortAndFuehrungskraft(false);
      setGeneration(true);
    }

    const btnBackToStandortAndFuehrungskraftHandler = () => {
      setGeschlecht(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setGeneration(false);
      setHobbies(false)
      setEigenschaft(false);
      setStandortAndFuehrungskraft(true);
    } 

    const btnBackToHobbies = () => {
      setGeneration(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setStandortAndFuehrungskraft(false);
      setEigenschaft(false);
      setGeschlecht(false);
      setRegistrierungGlobal(false);
      setHobbies(true)
    }

    const btnBackToEigenschaftenHandler = () => {
      setGeneration(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setStandortAndFuehrungskraft(false);
      setGeschlecht(false);
      setHobbies(false)
      setEigenschaft(true);
    }

    const btnBacktoRegisters = () => {
      setGeneration(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setStandortAndFuehrungskraft(false);
      setHobbies(false)
      setEigenschaft(false);
      setPartnerPrefernces(false);
      setGeschlecht(true);
      // setGlobalGeschlecht(true)
    }
    // Btns TO

    const btnGenerationHandler = () => {
        setKompetenzen(false);
        setShowMenteeKompetenzen(false);
        setStandortAndFuehrungskraft(false);
        setEigenschaft(false);
        setHobbies(false)
        setGeneration(true);
    }

    const btnToStandortAndFuehrungskraft = () => {
      setGeneration(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setEigenschaft(false);
      setHobbies(false)
      setStandortAndFuehrungskraft(true);
    }

    const btnToGeschlecht = () => {
      setGeneration(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setStandortAndFuehrungskraft(false);
      setHobbies(false)
      setEigenschaft(false);
      setGeschlecht(true);
    } 

    const btnToEigenschaften = () => {
      setGeneration(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setStandortAndFuehrungskraft(false);
      setGeschlecht(false);
      setHobbies(false)
      setEigenschaft(true);
    }

    const btnToHobbies = () => {
      setGeneration(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setStandortAndFuehrungskraft(false);
      setGeschlecht(false);
      setEigenschaft(false);
      setHobbies(true)
    }

    const nextToPartnerPrefernces = () => {
      setGeneration(false);
      setKompetenzen(false);
      setShowMenteeKompetenzen(false);
      setStandortAndFuehrungskraft(false);
      setGeschlecht(false);
      setEigenschaft(false);
      setHobbies(false);
      setPartnerPrefernces(true)
      setGlobalGeschlecht(true)
    }

    
 
    return(
        <div>
        {kompetenzen && <Kompetenzen btnNextToGeneration={btnGenerationHandler}  showMenteeKompetenzen={showMenteeKompetenzen} currentRegisterArt={currentRegisterArt}
        />}
        {generation && <GenerationenAndTaetigkeit btnBackHandler={btnBackToKompetenzenHandler} btnToStandortAndFuehrungskraft={btnToStandortAndFuehrungskraft}/>}

        {standortAndFuehrungskraft && <StandortAndFuehrungskraft btnBackHandler={btnBackToGenAndTaetigkeitHandler} btnToEigenschaften={btnToEigenschaften}/>}
        {(geschlecht || (registrierungGlobal && !globalGeschlecht)) && <Geschlecht btnBackHandler={btnBackToHobbies} btnNextTo={nextToPartnerPrefernces}/>}
        {eigenschaft && <Eigenschaften   btnBackHandler={btnBackToStandortAndFuehrungskraftHandler} btnToHobbies={btnToHobbies}/>}
        {hobbies && <Hobbies btnBackHandler={btnBackToEigenschaftenHandler} btnToGeschlecht={btnToGeschlecht}/>}
        {partnerPrefernces && <PartnerPrefernces />} 
        </div>
    )
}

export default Registrieren;