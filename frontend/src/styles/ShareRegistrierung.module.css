.btnsContainer{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 500px;
    margin-top: 15px;
  }
  
  .back{
    cursor: pointer;
    display: flex;
    /* flex-direction: column; */
    justify-content: center;
    align-items: center;
    /* margin-top: 15px; */
    background-color: #00206a;
    /* border: none; */
    border-radius: 5px;
    height: 20px;
    width: 150px;
    padding: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
    /* margin-top: 220px; */
  }
  
  .back:hover {
    text-shadow: 0 8px 12px rgba(0, 0, 0, 0.2); 
    transform: translateX(-5px);
  }
  
  .btnBack{
    cursor: pointer;
    font-size: 24px;
    color: #ffffff; 
    background: none;
    border: none;
    /* border-radius: 5px; */
    padding: 10px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .next{
    cursor: pointer;
    display: flex;
    /* flex-direction: column; */
    justify-content: center;
    align-items: center;
    /* margin-top: 15px; */
    background-color: #00206a;
    /* border: none; */
    border-radius: 5px;
    height: 20px;
    width: 150px;
    padding: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
    /* margin-top: 220px; */
  }
  
  .next:hover {
    text-shadow: 0 8px 12px rgba(0, 0, 0, 0.2); 
    transform: translateX(5px);
  }
  
  .btnNext{
    cursor: pointer;
    font-size: 24px;
    color: #ffffff; 
    background: none;
    border: none;
    /* border-radius: 5px; */
    padding: 10px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  
  .containerSiteTitle{
    margin-top: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 340px;
    padding: 15px;
    padding-right: 600px;
    /* border: 2px solid red; */
  }
  
  .siteTitle{
    /* padding-left:15px; */
  }
  
  .siteTitle span{
    /* padding-left:15px; */
    font-size: 25px;
    color: #545454;
    /* box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .title{
    width: 1220px;
    background-color: #f9f9f9;
    padding: 8px;
    margin-top: 0px;
    margin-bottom: 10px;
    border-radius: 10px;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    border: 1px solid #00206a;
  }
  
  
  .title h1{
    font-size: 24px;
    font-weight:bold; 
    color: #00206a;
    text-align: left;
  }
  
  
  .secondTitle{
    width: 1220px;
    background-color: #f9f9f9;
    padding: 8px;
    margin-top: 50px;
    margin-bottom: 10px;
    border-radius: 10px;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    border: 1px solid #00206a;
  }
  
  
  .secondTitle h1{
    font-size: 24px;
    font-weight:bold; 
    color: #00206a;
    text-align: left;
  }
  
  /* .container{
    display: flex;
    justify-content: space-between;
    width: 1200px;
    margin-top: 50px;
  } */
  
  
  .container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    background-color: #f9f9f9;
    width: 1217px;
    padding: 10px;
    border-radius: 15px;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    border: 1px solid #00206a;
  }
  
  /* .formContainer{
    height: 1000px;
    display: flex;
    flex-direction: column;
    background-color: #00206a;
  } */

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  .form {
    width: 100%;
    height: 628px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    animation: fadeIn 0.6s ease-in-out forwards;
    will-change: opacity;
  }
  
  
  .checkboxContainer {
    width: 500px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 20px;
    /* border: 2px solid red; */
  }
  
  .checkbox {
    cursor: pointer;
    display: flex;
    align-items: center;
    padding: 2.5px;
    /* width: 1000px; */
    /* height: 30px; */
    /* border: 1px solid blue; */
  }
  
  .checkbox label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    padding: 10px;
    border: 1px solid rgb(129, 129, 129);
    width: 500px;
    height: 10px;
    border-radius: 5px;
    color: #314a85;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
  
  }
  
  .checkbox label:hover {
    background-color: #e0e0e0;
    transform: translateX(5px);
  }
  
  .checkbox input{
    width: 20px;
    height: 20px;
    transform: scale(1.5);
    margin-right: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease-in-out;
  }
  
  .checkbox input:checked + label {
    font-weight: bold;
    color: #007bff; 
    /* margin-left: 100px; */
  }
  

.info {
  display: flex;
  justify-content: space-between;
  width: 1190px;
  margin-top: 80px;
}

.para {
  border: 2px solid red;
  border-radius: 15px;
  width: 950px;
  text-align: justify;
  padding: 10px;
  line-height: 1.5;
  height: auto;
  color: #314a85;
}