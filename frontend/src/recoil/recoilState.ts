import { atom } from 'recoil';

export const selectedMentorState = atom<number[]>({
  key: 'selectedMentorState',
  default: [],
});

export const selectedMenteeState = atom<number[]>({
  key: 'selectedMenteeState',
  default: [],
})

export const selectedGenerationState = atom<number[]>({
  key: 'selectedGenerationState',
  default: [],
})

export const selectedGesuchterGenerationState = atom<number[]>({
  key: 'selectedGesuchterGenerationState',
  default: [],
})

export const selectedTaetigkeitState = atom<number[]>({
  key: 'selectedTaetigkeitState',
  default: [],
})

export const selectedGesuchteTaetigkeitState = atom<number[]>({
  key: 'selectedGesuchteTaetigkeitState',
  default: [],
})

export const selectedStandortState = atom<number[]>({
  key: 'selectedStandortState',
  default: [],
})

export const selectedGesuchterStandortState = atom<number[]>({
  key: "selectedGesuchterStandortState",
  default: [],
})

export const selectedFuehrungsKraftState = atom<boolean | null>({
  key: 'selectedFuehrungsKraftState',
  default: null,
})

export const selectedGesuchteFuehrungsKraftState = atom<boolean | number | null> ({
  key: "selectedGesuchteFuehrungsKraftState",
  default: null, 
})

export const selectedGeschlechtState = atom<number | null>({
  key: "selectedGeschlechtState",
  default: null,
})

export const selectedGesuchtesGeschlechtState = atom<number | null>({
  key: "selectedGesuchtesGeschlechtState",
  default: null,
})

export const selectedEigenschaftState = atom<number[]>({
  key: "selectedEigenschaftState",
  default: [],
})

export const selectedHobbiesState = atom<number[]>({
  key: "selectedHobbiesState",
  default: [],
})


export const globalGeschlechtState = atom<boolean> ({
  key: "globalGeschlechtState",
  default: false,
})


export const registrierungState = atom<boolean> ({
  key: "registrierungState",
  default: false,
})

export const kompetenzenState = atom<boolean> ({
  key: "kompetenzenState",
  default: true,
})

export const selectedWieOftState = atom<number[]> ({
  key: "selectedWieOftState",
  default: [],
})

export const selectedWieTreffenState = atom<number[]> ({
  key: "selectedWieTreffenState",
  default: [],
})

// export const selectedGesuchteGenerationState = atom<number[]> ({
//   key: "selectedGesuchteGenerationState",
//   default: [],
// })

// export const selectedGesuchteTaetigkeitState = atom<number[]> ({
//   key:"selectedGesuchteTaetigkeitState",
//   default: [],
// })