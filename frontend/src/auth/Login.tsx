import React, { useState } from 'react';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [passwort, setPasswort] = useState('');

  const login = async () => {
    try {
      const response = await fetch('http://localhost:8050/site/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ email, passwort }),
      });

      const data = await response.json();
      console.log(data);
      if (data.success) {
        localStorage.setItem('csrfToken', data.csrfToken);
        console.log('Login erfolgreich');
        console.log(data.csrfToken);
        // if (data.isAdmin) {
        //   window.location.href = 'http://localhost:8050/benutzer';
        // }
      } else {
        console.error('Login fehlgeschlagen:', data.message);
      }
    } catch (error) {
      console.error('<PERSON><PERSON> beim Login:', error);
    }
  };

  return (
    <div>
      <h2>Login</h2>
      <input type="email" placeholder="E-Mail" value={email} onChange={e => setEmail(e.target.value)} />
      <input type="password" placeholder="Passwort" value={passwort} onChange={e => setPasswort(e.target.value)} />
      <button onClick={login}>Einloggen</button>
    </div>
  );
};

export default Login;
