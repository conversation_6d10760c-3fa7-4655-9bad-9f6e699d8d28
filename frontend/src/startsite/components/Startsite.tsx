import React from "react";
import styles from "./Home.module.css";
import { IoHome } from 'react-icons/io5';
import { NavLink } from "react-router-dom";
import { ReactComponent as RegisterIcon } from './RegisterIcon.svg';
import Navigation from "../../layout/navigation/Navigation";
import Header from "../../layout/header/Header";

const Home = () => {
  return (
     <Header>
          <div className={styles.containerSiteHeader}>
            <div className={styles.containerSiteTitle}>
              <div>
                <IoHome style={{ height: "28px", width: "28px" }} />

                {/* <svg xmlns="http://www.w3.org/2000/svg" height="38px" viewBox="0 -960 960 960" width="38px" fill="#545454"><path d="M233.85-193.85h156.92v-238.46h178.46v238.46h156.92v-369.23L480-749.49 233.85-563.25v369.4ZM200-160v-420l280-211.54L760-580v420H535.38v-238.46H424.62V-160H200Zm280-311.74Z"/></svg>                     */}
              </div>
              <div className={styles.siteTitle}>
                <span>Startseite</span>
              </div>
            </div>
          </div>

          <div className={styles.home}>
            <div className={styles.header}>
              <div className={styles.title}>
                <h1>
                  Wilkommen,
                  <span className={styles.titleSpan}>Max Mustermann</span> zum
                  Mentoringprogramm!
                </h1>
              </div>
            </div>
            <div className={styles.container}>
                <NavLink to="/register-auswahl">
                    <div className={styles.register}>
                        <div className={styles.registerIcon}>
                        <RegisterIcon  width="150" height="150"/>
                        {/* <svg xmlns="http://www.w3.org/2000/svg" height="320px" viewBox="29 -780 960 960" width="320px" fill="#314a85"><path d="M480-512q-44.55 0-74.77-30.22Q375-572.45 375-617.5t30.23-74.78Q435.45-722 480-722t74.78 29.72Q585-662.55 585-617.5t-30.22 75.28Q524.55-512 480-512ZM212-232v-47q0-23 14-42t37-30q57-25 110.97-38t106-13Q532-402 586-389t110.48 38.4q23.69 10.71 37.6 29.65Q748-302 748-279v47H212Zm22-22h492v-25q0-15-10.5-29T686-332q-51-25-102.19-36.5Q532.63-380 480-380t-104.31 11.5Q324-357 274-332q-19 10-29.5 24T234-279v25Zm246-280q35 0 59-24t24-59q0-35-24-59t-59-24q-35 0-59 24t-24 59q0 35 24 59t59 24Zm0-83Zm0 363Z"/></svg>                         */}
                        </div>
                        <button className={styles.btnRegister}>Teilnehmen</button>
                    </div>
                </NavLink>
                </div>
          </div>
    </Header>
  );
};

export default Home;
