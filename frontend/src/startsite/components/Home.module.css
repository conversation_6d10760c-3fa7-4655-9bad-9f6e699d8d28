.home {
  display: flex;
  flex-direction: column; 
  justify-content: center;
  align-items: center;
  width: 1550px;
  height: 730px;
  border: none;
  border-radius: 10px;
  /* margin-bottom: 20vh; */
  background-color: #ffffff;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.setting {
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  /* padding: 10px; */
  /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */
  border-radius: 10px;
  margin-right: 20px;
  /* border: 1px solid #959595; */
  /* width: 500px; */
}

.btnSetting {
  cursor: pointer;
  font-size: 20px;
  font-weight: 600;
  color: #545454;
  background: none;
  border: none;
  padding-left: 10px;
}

.settingIcon {
  height: 35px;
  width: 35px;
  color: #545454;
  padding-right: 10px;
}

.containerSiteHeader {
  width: 1550px;
  display: flex;
  /* flex-direction: column; */
  justify-content: space-between;
  align-items: center;
  /* border: 1px solid seagreen; */
  margin-top: 10px;
}

.containerSiteTitle {
  margin-top: 0px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 160px;
  padding: 15px;
  /* padding-left: 700px; */
  /* border: 2px solid red; */
}

.siteTitle {
  /* padding-left:15px; */
}

.siteTitle span {
  /* padding-left:15px; */
  font-size: 25px;
  color: #545454;
  /* box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); */
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 1500px;
  margin-top: 0px;
}

.title {
  width: 1200px;
}

.title h1 {
  font-size: 35px;
  color: #00206a;
  text-align: center;
}

.titleSpan {
  font-weight: 900;
}

.container {
  display: flex;
  justify-content: center;
  width: 800px;
  margin-top: 100px;
}

.register {
  cursor: pointer;
  background-color: rgb(245 249 255);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  height: 200px;
}

.register:hover {
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.registerIcon {
  height: 180px;
  width: 310px;
}

.btnRegister {
  cursor: pointer;
  font-size: 30px;
  color: #314a85;
  background: none;
  border: none;
}

.suchen {
  cursor: pointer;
  background-color: rgb(245 249 255);
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
  height: 200px;
}

.suchen:hover {
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.suchenIcon {
  height: 180px;
  width: 280px;
}

.btnSuchen {
  cursor: pointer;
  font-size: 25px;
  color: #314a85;
  background: none;
  border: none;
}
