const csrfToken = localStorage.getItem('csrfToken');


export const fetchKompetenzenList = async(): Promise<{id: number, kompetenz: string}[]> => {
    try{
        const response = await fetch('http://localhost:8050/kompetenzen');

        if(!response.ok) {
            throw new Error(`<PERSON><PERSON> beim <PERSON>den: ${response.status}`)
        }
        const data = await response.json();
        return data.kompetenzenList;
    } catch(error){
        console.log(error)
        return[]
    }
}


export const fetchGenerationen = async(): Promise<{id: number, generation: string}[] | "UNAUTHORIZED"> => {
    try{
        const response = await fetch("http://localhost:8050/generationen", {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrfToken ?? '',
            },
            credentials: 'include',
          });

        if(!response.ok) {
            throw new Error(`<PERSON><PERSON> beim <PERSON>den: ${response.status}`);
        }

        if (response.status === 401) {
        return "UNAUTHORIZED";
        }

        const data = await response.json();
        return data.generationen;

  } catch (error) {
    console.log(error);
    return "UNAUTHORIZED";
  }
}


export const fetchTaetigkeitsfeld = async(): Promise<{id: number, feld: string}[] | "UNAUTHORIZED"> => {
    try{
        const response = await fetch("http://localhost:8050/taetigkeitsfeld", {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrfToken ?? '',
            },
            credentials: 'include',
          });

          
        if(!response.ok) {
            throw new Error(`Fehler beim Laden: ${response.status}`);
        }

        if (response.status === 401) {
            return "UNAUTHORIZED";
            }

        const data = await response.json();

        return data.taetigkeitsfeld;

    } catch(error) {
        console.log(error)
        return []
    }
}

export const fetchStandort = async(): Promise<{id: number, standort: string}[] | "UNAUTHORIZED"> => {
    try{
        const response = await fetch("http://localhost:8050/standort", {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrfToken ?? '',
            },
            credentials: 'include',
          });

        if(!response.ok) {
            throw new Error(`Fehler beim Laden: ${response.status}`);
        }

        if (response.status === 401) {
            return "UNAUTHORIZED";
            }

        const data = await response.json();

        return data.standort;

    } catch(error) {
        console.log(error)
        return []
    }
}

export const fetchGeschlecht = async(): Promise<{id: number, geschlecht: string}[] | "UNAUTHORIZED"> => {
    try{
        const response = await fetch("http://localhost:8050/geschlecht", {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrfToken ?? '',
            },
            credentials: 'include',
          });

        if(!response.ok) {
            throw new Error(`Fehler beim Laden: ${response.status}`);
        }

        if (response.status === 401) {
            return "UNAUTHORIZED";
            }

        const data = await response.json();

        return data.geschlecht;

    } catch(error) {
        console.log(error)
        return []
    }
}



export const fetchEigenschaften = async(): Promise<{id: number, eigenschaft: string}[] | "UNAUTHORIZED"> => {
    try{
        const response = await fetch("http://localhost:8050/eigenschaften", {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrfToken ?? '',
            },
            credentials: 'include',
          });

        if(!response.ok) {
            throw new Error(`Fehler beim Laden: ${response.status}`);
        }

        if (response.status === 401) {
            return "UNAUTHORIZED";
            }

        const data = await response.json();

        return data.eigenschaften;

    } catch(error) {
        console.log(error)
        return []
    }
}

export const fetchHobbies = async(): Promise<{id: number, hobby: string}[] | "UNAUTHORIZED"> => {
    try{
        const response = await fetch("http://localhost:8050/hobbies", {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrfToken ?? '',
            },
            credentials: 'include',
          });

        if(!response.ok) {
            throw new Error(`Fehler beim Laden: ${response.status}`);
        }

        if (response.status === 401) {
            return "UNAUTHORIZED";
            }

        const data = await response.json();

        return data.hobbies;

    } catch(error) {
        console.log(error)
        return []
    }
}

export const fetchWieOft = async(): Promise<{id: number, option: string}[] | "UNAUTHORIZED"> => {
  try{
      const response = await fetch("http://localhost:8050/wie-oft/index", {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken ?? '',
          },
          credentials: 'include',
        });
      if(!response.ok) {
          throw new Error(`Fehler beim Laden: ${response.status}`);
      }

      if (response.status === 401) {
          return "UNAUTHORIZED";
          }

      const data = await response.json();

      return data.wieOft;

  } catch(error) {
      console.log(error)
      return []
  }
}

export const fetchWieTreffen = async(): Promise<{id: number, option: string}[] | "UNAUTHORIZED"> => {
  try{
      const response = await fetch("http://localhost:8050/wie-treffen", {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': csrfToken ?? '',
          },
          credentials: 'include',
        });
      if(!response.ok) {
          throw new Error(`Fehler beim Laden: ${response.status}`);
      }

      if (response.status === 401) {
          return "UNAUTHORIZED";
          }

      const data = await response.json();

      return data.wieTreffen;

  } catch(error) {
      console.log(error)
      return []
  }
}



// API für Create


export const createBenutzerKompetenzen = async (kompetenzen: number[], arts: string): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-kompetenzen/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          kompetenzen,
          arts,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();
  
      if (!response.ok) {
        throw new Error(`Fehler: ${result.message || response.statusText}`);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };
  


  export const createBenutzerEigenschaften = async (eigenschaften: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-eigenschaften/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          eigenschaften,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();
  
      if (!response.ok) {
        throw new Error(`Fehler: ${result.message || response.statusText}`);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerGeneration = async (generation: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-generation/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          generation,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();
  
      if (!response.ok) {
        throw new Error(`Fehler: ${result.message || response.statusText}`);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerStandort = async (standort: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-standort/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          standort,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();
  
      if (!response.ok) {
        throw new Error(`Fehler: ${result.message || response.statusText}`);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerProfile = async (fuehrungskraft: boolean, geschlecht: number): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer/create-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          fuehrungskraft,
          geschlecht,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();
  
      if (!response.ok) {
        throw new Error(`Fehler: ${result.message || response.statusText}`);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerTaetigkeit = async (taetigkeit: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-taetigkeit/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          taetigkeit,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();
  
      if (!response.ok) {
        throw new Error(`Fehler: ${result.message || response.statusText}`);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerHobbies = async (hobbies: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-hobbies/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          hobbies,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();
  
      if (!response.ok) {
        throw new Error(`Fehler: ${result.message || response.statusText}`);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };


  export const createBeutzerGesuchteFuehrungskraft = async (fuehrungskraft: boolean | number): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer/benutzer-gesuchte-fuehrungskraft', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          fuehrungskraft,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();

      console.log(result);
  
      if (!response.ok) {
        throw new Error(result);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };


  export const createBenutzerGesuchterStandort = async (standort: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-standort/benutzer-gesuchter-standort', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          standort,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();

      console.log(result);
  
      if (!response.ok) {
        throw new Error(result);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerGesuchteGeneration = async (generation: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-generation/benutzer-gesuchte-generation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          generation,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();

      console.log(result);
  
      if (!response.ok) {
        throw new Error(result);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerGesuchteTaetigkeit = async (taetigkeit: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer-taetigkeit/benutzer-gesuchte-taetigkeit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          taetigkeit,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();

      console.log(result);
  
      if (!response.ok) {
        throw new Error(result);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };
  

  export const createBenutzerWieOft = async (wieOft: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/wie-oft/benutzer-gesuchter-wie-oft', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          wieOft,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();

      console.log(result);
  
      if (!response.ok) {
        throw new Error(result);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerWieTreffen = async (wieTreffen: number[]): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/wie-treffen/benutzer-gesuchter-wie-treffen', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          wieTreffen,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();

      console.log(result);
  
      if (!response.ok) {
        throw new Error(result);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };

  export const createBenutzerGesuchtesGeschlecht = async (geschlecht: number): Promise<any> => {
    try {
      const response = await fetch('http://localhost:8050/benutzer/benutzer-gesuchtes-geschlecht', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken ?? '',
        },
        credentials: 'include',
        body: JSON.stringify({
          geschlecht,
        }),
      });
  
      if (response.status === 401) {
        localStorage.removeItem('csrfToken');
        throw new Error('Unauthorized');
      }
  
      const result = await response.json();

      console.log(result);
  
      if (!response.ok) {
        throw new Error(result);
      }
  
      return result;
    } catch (error) {
      console.error('Fehler beim Erstellen neuer Daten:', error);
      throw error;
    }
  };