import { createBenutzerKompetenzen, 
  createBenutzerGeneration, 
  createBenutzerStandort,
  createBenutzerTaetigkeit, 
  createBenutzerProfile, 
  createBenutzerEigenschaften, 
  createBenutzerHobbies,
  createBeutzerGesuchteFuehrungskraft,
  createBenutzerGesuchterStandort,
  createBenutzerGesuchteGeneration,
  createBenutzerGesuchteTaetigkeit,
  createBenutzerWieOft,
  createBenutzerWieTreffen,
  createBenutzerGesuchtesGeschlecht} 
from "./apiUtils";

import {useRecoilState } from 'recoil';

import { selectedMentorState, 
        selectedMenteeState, 
        selectedEigenschaftState, 
        selectedGenerationState, 
        selectedStandortState,
        selectedTaetigkeitState, 
        selectedGeschlechtState,
        selectedFuehrungsKraftState,
        selectedHobbiesState,
        selectedGesuchteFuehrungsKraftState,
        selectedGesuchterStandortState,
        selectedGesuchterGenerationState,
        selectedGesuchteTaetigkeitState,
        selected<PERSON>ieOftState,
        selectedWieTreffenState,
        selectedGesuchtesGeschlechtState} 
from ".././recoil/recoilState"

export const UsePostRegistierungAnServer = () => {
    // const mentor = useRecoilValue(selectedMentorState);
    // const mentee = useRecoilValue(selectedMenteeState);
    const [mentor, setMentor] = useRecoilState(selectedMentorState);
    const [mentee, setMentee] = useRecoilState(selectedMenteeState);
    const [eigenschaften, setEigenschaften] = useRecoilState(selectedEigenschaftState);
    const [generation, setGeneration] = useRecoilState(selectedGenerationState);
    const [standort, setStandort] = useRecoilState(selectedStandortState);
    const [taetigkeit, setTaetigkeit] = useRecoilState(selectedTaetigkeitState);
    const [geschlecht, setGesclecht] = useRecoilState(selectedGeschlechtState);
    const [fuehrungskraft, setFuehrungskraft] = useRecoilState(selectedFuehrungsKraftState);
    const [hobbies, setHobbies] = useRecoilState(selectedHobbiesState);
    const [gesuchteFuehrungskraft, setGesuchteFuehrungskraft] = useRecoilState(selectedGesuchteFuehrungsKraftState);
    const [gesuchterStandort, setGesuchterStandort] = useRecoilState(selectedGesuchterStandortState);
    const [gesuchteGeneration, setGesuchteGeneration] = useRecoilState(selectedGesuchterGenerationState);
    const [gesuchteTaetigkeit, setGesuchteTaetigkeit] = useRecoilState(selectedGesuchteTaetigkeitState);
    const [wieOft, setWieOft] = useRecoilState(selectedWieOftState);
    const [wieTreffen, setWieTreffen] = useRecoilState(selectedWieTreffenState);
    const [gesuchtesGeschlecht, setGesuchtesGeschlecht] = useRecoilState(selectedGesuchtesGeschlechtState);


    const registrieren = async(registerArt: string) => {
        try {
            if (registerArt === "menteeAndMentor") {
              if (mentor && mentor.length > 0) {
                const result = await createBenutzerKompetenzen(mentor, "mentor");
                console.log(result);
              }
              if (mentee && mentee.length > 0) {
               const result = await createBenutzerKompetenzen(mentee, "mentee");
               console.log(result);
              }
            } else if (registerArt === "mentor") {
              if (mentor && mentor.length > 0) {
               const result = await createBenutzerKompetenzen(mentor, "mentor");
               console.log(result);
              }
            } else if (registerArt === "mentee") {
              if (mentee && mentee.length > 0) {
               const result = await createBenutzerKompetenzen(mentee, "mentee");
               console.log(result);
              }
            } else {
              throw new Error("Unbekannter Registrierungstyp");
            }

            setMentor([]);
            setMentee([]);
        
          } catch (error) {
            console.error(error);
          }


          // Eigenschaften
        if (eigenschaften && eigenschaften.length > 0) {
            await createBenutzerEigenschaften(eigenschaften);
            setEigenschaften([])
        }

        // Generation
        if(generation && generation.length > 0) {
          await createBenutzerGeneration(generation);
          setGeneration([]);
        }

        // Standort
        if(standort && standort.length > 0) {
          await createBenutzerStandort(standort);
          setStandort([]);
        }

        // Taetigkeit
        if(taetigkeit && taetigkeit.length > 0) {
          await createBenutzerTaetigkeit(taetigkeit)
          setTaetigkeit([]);
        }

        // geschlecht & fühungskraft
        if (geschlecht !== undefined && fuehrungskraft !== null && fuehrungskraft !== undefined) {
          const geschlechtValue = geschlecht ?? 0;
          await createBenutzerProfile(fuehrungskraft, geschlechtValue);
          setGesclecht(null)
          setFuehrungskraft(null)
        }

        // Hobbies
        if(hobbies && hobbies.length > 0) {
          await createBenutzerHobbies(hobbies);
          setHobbies([]); 
        }

        // gesuchteFuehrungskraft
        if(gesuchteFuehrungskraft && gesuchteFuehrungskraft !== undefined) {
          await createBeutzerGesuchteFuehrungskraft(gesuchteFuehrungskraft);
          setGesuchteFuehrungskraft(null);
        }

        // gesuchterStandort
        if(gesuchterStandort && gesuchterStandort.length > 0) {
          await createBenutzerGesuchterStandort(gesuchterStandort);
          setGesuchterStandort([])
        }

        // gesuchteGeneration
        if(gesuchteGeneration && gesuchteGeneration.length > 0) {
          await createBenutzerGesuchteGeneration(gesuchteGeneration);
          setGesuchteGeneration([])
        }

        // gesuchteTaetigkeit
        if(gesuchteTaetigkeit && gesuchteTaetigkeit.length > 0) {
          await createBenutzerGesuchteTaetigkeit(gesuchteTaetigkeit);
          setGesuchteTaetigkeit([])
        }

        // wieOft
        if(wieOft && wieOft.length > 0) {
          await createBenutzerWieOft(wieOft);
          setWieOft([])
        }

        // wieTreffen
        if(wieTreffen && wieTreffen.length > 0) {
          await createBenutzerWieTreffen(wieTreffen);
          setWieTreffen([])
        }

        // gesuchtesGeschlecht
        if(gesuchtesGeschlecht && gesuchtesGeschlecht !== undefined) {
          await createBenutzerGesuchtesGeschlecht(gesuchtesGeschlecht);
          setGesuchtesGeschlecht(null);
        }
      }

    return registrieren;
    
}