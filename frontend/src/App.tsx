import './App.css';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Startsite from './startsite/components/Startsite';
import RegistrierungAuswahl from './registrierung/components/registrierungsAuswahl/RegistrierungAuswahl';
// import RegistrierungMentee from './registrierung/components/registrierungsMentee/RegistrierungsMentee';
// import Kompetenzen from './registrierung/components/kompetenzen/Kompetenzen';
// import RegisterMenteeAndMentor from './registrierung/components/registrierungsMenteeAndMentor/RegistrierungsMenteeAndMentor';
import Login from './auth/Login';
// import Generationen from './registrierung/components/generationenAndTaetigkeit/GenerationenAndTaetigkeit';
import Registrieren from './registrierung/page/Registrierung';
import PartnerPrefernces from './partnerPreferences/page/PartnerPrefernces';

function App() {
  return (
    <div className="App">
      
      <Router>
      <Routes>
      <Route
          path="/"
          element={<Startsite />}
        />


        <Route
          path="/register-auswahl"
          element={<RegistrierungAuswahl /> }
        />

        {/* <Route
          path="/register-mentee"
          element={<RegistrierungMentee /> }
        /> */}

        <Route
          path="/registrierung"
          element={<Registrieren />}
        />

        {/* <Route
        path='/Partner-prefernces'
        element={<PartnerPrefernces />}
        /> */}

        {/* <Route
          path="/register-generation"
          element={<Generationen/> }
        />   */}
        
        <Route
          path="/login"
          element={<Login />}
        /> 

        
        


     {/* <Home />  */}
     {/* <RegistrierungAuswahl /> */}
     {/* <RegistrierungMentee /> */}
     {/* <Kompetenzen /> */}
     </Routes>
     </Router>
    </div>
  );
}

export default App;
