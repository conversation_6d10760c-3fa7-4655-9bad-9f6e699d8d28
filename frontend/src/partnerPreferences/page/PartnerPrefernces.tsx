import React, { useState } from "react";
import GesuchteFurhungskraftUndStandort from "../components/gesuchteStandortUndFuehrungskraft/GesuchteStandortUndFuehrungskraft";
import GesuchteGenerationenAndTaetigkeit from "../components/gesuchteGenerationenAndTaetigkeit/GesuchteGenerationenAndTaetigkeit";
import { useRecoilState, useRecoilValue } from "recoil";
import { globalGeschlechtState, registrierungState, kompetenzenState } from "../../recoil/recoilState";
// import Registrieren from "../../registrierung/page/Registrierung";
import Registrierung from "../../registrierung/page/Registrierung";
import WieOftUndWieTreffen from "../components/wieOftUndWieTreffen/WieOftUndWieTreffen";
import GesuchtesGeschlecht from "../components/gesuchtesGeschlecht/GeshuchtesGeschlecht";



const PartnerPrefernces: React.FC = () => {
    const [gesuchteFuehrungskraftUndStandort, setGesuchteFuehrungskraftUndStandort] = useState<boolean>(false);
    const [registrierungGlobal, setRegistrierungGlobal] = useRecoilState(registrierungState)
    const [registrierung, setRegistrierung] = useState<boolean>(false);
    const [wieOftUndWieTreffen, setWieOftUndWieTreffen] = useState<boolean>(false);
    const [geschutesGeschlecht, setGeschutesGeschlecht] = useState<boolean>(false);
    const [globalGeschlecht, setGlobalGeschlecht] = useRecoilState(globalGeschlechtState)
    // const [btnBacktoRegister, setBtnBacktoRegister] = useState<boolean>(false);
    const [kompetenzen, setKompetenzen] = useRecoilState(kompetenzenState);


   
    const nextToWieOftWieTreffen = () => {
        setGlobalGeschlecht(false);
        setRegistrierungGlobal(false);
        setGesuchteFuehrungskraftUndStandort(false);
        setGeschutesGeschlecht(false);
        setWieOftUndWieTreffen(true);
    }

    const nextToFuehrungskraftUndStandort = () => {
        // setGesuchteGenerationenAndTaetigkeit(false);
        setGlobalGeschlecht(false);
        setRegistrierungGlobal(false);
        setGeschutesGeschlecht(false);
        setGesuchteFuehrungskraftUndStandort(true);
    }


    const nextToGesuchtesGeschlecht = () => {
        setGlobalGeschlecht(false);
        setRegistrierungGlobal(false);
        setGesuchteFuehrungskraftUndStandort(false);
        setWieOftUndWieTreffen(false);
        setGeschutesGeschlecht(true);
    }

    const backToRegister = () => {
        setGlobalGeschlecht(false);
        setGesuchteFuehrungskraftUndStandort(false);
        setRegistrierungGlobal(true);
        setKompetenzen(false);
        setGeschutesGeschlecht(false);
        // setRegistrierung(true);
    }

    const backtoGesuchteGenerationenAndTaetigkeit = () => {
        // setGesuchteGenerationenAndTaetigkeit(true);
        setGeschutesGeschlecht(false);
        setGlobalGeschlecht(true);
        setGesuchteFuehrungskraftUndStandort(false);
    }

    const backToFuehrungskraftUndSandort = () => {
        setGlobalGeschlecht(false);
        setWieOftUndWieTreffen(false)
        setGeschutesGeschlecht(false);
        setGesuchteFuehrungskraftUndStandort(true);
    }

    const backToWieOftWieTreffen = () => {
        setGlobalGeschlecht(false);
        setRegistrierungGlobal(false);
        setGesuchteFuehrungskraftUndStandort(false);
        setGeschutesGeschlecht(false);
        setWieOftUndWieTreffen(true);
        
    }


    return(
        <div>
        {globalGeschlecht && <GesuchteGenerationenAndTaetigkeit btnBackHandler={backToRegister} btnNextTo={nextToFuehrungskraftUndStandort} />} 
        {gesuchteFuehrungskraftUndStandort && <GesuchteFurhungskraftUndStandort btnBackHandler={backtoGesuchteGenerationenAndTaetigkeit} btnNextTo={nextToWieOftWieTreffen} />}
        {wieOftUndWieTreffen && <WieOftUndWieTreffen btnBackHandler={backToFuehrungskraftUndSandort} btnNextTo={nextToGesuchtesGeschlecht}/>}
        {geschutesGeschlecht && <GesuchtesGeschlecht btnBackHandler={backToWieOftWieTreffen}/>}
        {/* {registrierung && <Registrierung />} */}
        {/* {globalGeschlecht && <Registrieren />} */}
        </div>
    )
}

export default PartnerPrefernces;