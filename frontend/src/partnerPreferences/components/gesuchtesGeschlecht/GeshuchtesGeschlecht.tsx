import React, { useEffect, useState } from "react";
import { useRecoilState } from 'recoil';
// import styles from "./../../../styles/ShareRegistrierung.module.css";
import styles from "./Geschlecht.module.css"
import Form from 'react-bootstrap/Form';
import Header from "../../../layout/header/Header";
import CardHeader from "../../../layout/cardHeader/CardHeader";
import Card from "../../../layout/card/Card";
import { fetchGeschlecht} from "../../../Utilities/apiUtils";
import { useHandleUnauthorized } from "../../../Utilities/customHooks";
import { selectedGesuchtesGeschlechtState } from "../../../recoil/recoilState";
import { UsePostRegistierungAnServer } from "../../../Utilities/postRegistierungAnServer";
import { useLocation } from "react-router-dom";
import { ReactComponent as Info } from "./Info.svg";


interface Geschlecht {
    id: number,
    geschlecht: string,
}

interface GeschlechtProps {
    btnBackHandler: () => void,
}

const GesuchtesGeschlecht: React.FC<GeschlechtProps> =  ({btnBackHandler}) => {
    const location = useLocation();
    const registrieren = UsePostRegistierungAnServer();
    const unauthorized = useHandleUnauthorized();
    // useRecoilState
    const[selectedGlobalGesuchtesGeschlecht,setSelectedGlobalGesuchtesGeschlecht] = useRecoilState(selectedGesuchtesGeschlechtState);

    // useState
    const [geschlechtList, setGeschlechtList] = useState<Geschlecht[]>([]);


    useEffect(() => {
        const fetch = async () => {
            const geschlechtData = await fetchGeschlecht();

            if(geschlechtData === "UNAUTHORIZED") {
                return unauthorized
            } else {
                setGeschlechtList(geschlechtData);
            }
        }

        fetch();

    })

    const submitHandler = async () => {
        const registerArt = location.state?.registerArt || "";
        await registrieren(registerArt); 
    };

    const changeRadioGeschlechtHandler = (id: number) => {
        setSelectedGlobalGesuchtesGeschlecht(id);
    } 

    return(
        <Header>
            <CardHeader title="Geschlecht" 
            svg={
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="40px"
                    viewBox="0 -960 960 960"
                    width="40px"
                    fill="#545454"
                >
                    <path d="m421.69-401.9-98.87-98.87q-7.23-7.23-17.7-7.09-10.48.14-18.09 7.76-7.62 7.61-7.62 17.89 0 10.29 7.62 17.9l112.74 112.08q9.34 9.61 21.78 9.61 12.45 0 21.81-9.61l232.95-232.69q7.23-7.23 7.42-17.71.19-10.47-7.42-18.09-7.62-7.61-18.03-7.61t-17.77 7.61L421.69-401.9ZM202.57-140q-25.79 0-44.18-18.39T140-202.57v-554.86q0-25.79 18.39-44.18T202.57-820h554.86q25.79 0 44.18 18.39T820-757.43v554.86q0 25.79-18.39 44.18T757.43-140H202.57Zm0-50.26h554.86q4.62 0 8.47-3.84 3.84-3.85 3.84-8.47v-554.86q0-4.62-3.84-8.47-3.85-3.84-8.47-3.84H202.57q-4.62 0-8.47 3.84-3.84 3.85-3.84 8.47v554.86q0 4.62 3.84 8.47 3.85 3.84 8.47 3.84Zm-12.31-579.48v579.48-579.48Z" />
                </svg>
            }
            />
            <Card>
                <Form className={styles.form}>
                    <div className={styles.title}>
                        <h1>Mein*e Tandempartner*in sollte, folgendes Geschlecht haben:</h1>
                    </div>
                    <div className={styles.container}>
                        {geschlechtList.map((geschlecht) => {
                        const isChecked: boolean = selectedGlobalGesuchtesGeschlecht === geschlecht.id;
                        // console.log(isChecked);
                        return(
                            <div key={geschlecht.id} className={styles.checkboxContainer}>
                                <Form.Check
                                    type="radio"
                                    value={geschlecht.id}
                                    label={geschlecht.geschlecht.toLowerCase() === "keine angabe" ? "Egal" : geschlecht.geschlecht}
                                    id={`geschlecht-${geschlecht.id}`}
                                    checked={isChecked}
                                    onChange={() => changeRadioGeschlechtHandler(geschlecht.id)}
                                    name="geschlecht"
                                    className={styles.checkbox}
                                />
                            </div>);
                        })}
                    </div>
                </Form>

                <div className={styles.btnsContainer}>
                        <div className={styles.back} onClick={btnBackHandler}>
                            <button type="button" className={styles.btnBack}>Zurück</button>
                        </div>
                        <div className={styles.next}>
                            <button type="submit" className={styles.btnNext} onClick={submitHandler}>Submit</button>
                        </div>
                    </div>
            </Card>
        </Header>
    )
}

export default GesuchtesGeschlecht;