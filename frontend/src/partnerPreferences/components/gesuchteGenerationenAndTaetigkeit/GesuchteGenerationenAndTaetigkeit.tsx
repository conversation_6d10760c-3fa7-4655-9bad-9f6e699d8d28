import React, { useEffect, useState } from "react";
// import styles from "./Generationen.module.css";
import styles from "../../../styles/ShareRegistrierung.module.css";
import { useRecoilState, useRecoilValue } from 'recoil';
import { selectedGesuchterGenerationState, selectedGesuchteTaetigkeitState } from "../../../recoil/recoilState"
import { NavLink } from "react-router-dom";
import Form from 'react-bootstrap/Form';
import { fetchGenerationen, fetchTaetigkeitsfeld } from "../../../Utilities/apiUtils";
import { useHandleUnauthorized } from "../../../Utilities/customHooks";
import Header from "../../../layout/header/Header";
import CardHeader from "../../../layout/cardHeader/CardHeader";
import { useNavigate } from "react-router-dom";
import Card from "../../../layout/card/Card";

interface Generation {
    id: number;
    generation: string;
}

interface Taetigkeitsfeld {
    id: number;
    feld: string;
}

interface GenerationenProps {
    btnBackHandler: () => void;
    btnNextTo: () => void,
}

const GesuchteGenerationenAndTaetigkeit: React.FC<GenerationenProps> = ({ btnBackHandler, btnNextTo }) => {
    const navigate = useNavigate();
    // useRecoilState
    const [selectedGlobalGesuchterGeneration, setSelectedGlobalGesuchterGeneration] = useRecoilState(selectedGesuchterGenerationState);
    const [selectedGlobalGesuchteTaetigkeit, setSelectedGlobalGesuchteTaetigkeit] = useRecoilState(selectedGesuchteTaetigkeitState); 

    // useState
    const [generationenList, setGenerationList] = useState<Generation[]>([]);
    const [taetigkeitsfeldList, setTaetigkeitsfeldList] = useState<Taetigkeitsfeld[]>([]);
    const handleUnauthorized = useHandleUnauthorized();

    useEffect(() => {
        const fetchData = async () => {
            const generationData = await fetchGenerationen();
            const taetigkeitsfeldData = await fetchTaetigkeitsfeld();

            generationData === "UNAUTHORIZED" ?  handleUnauthorized() : setGenerationList(generationData);
            taetigkeitsfeldData === "UNAUTHORIZED" ?  handleUnauthorized() :  setTaetigkeitsfeldList(taetigkeitsfeldData);

        };

        fetchData();
    }, []);


    const changeGenerationHandler  = (id: number) => {
        setSelectedGlobalGesuchterGeneration((prv) => 
            prv.includes(id) ? prv.filter((generation) =>generation !== id) : [...prv, id]
        ) 
    }


    const changeTaetigkeitHandler = (id: number) => {
        setSelectedGlobalGesuchteTaetigkeit((prv) => 
            prv.includes(id) ? prv.filter((taetigkeitsId) =>taetigkeitsId !== id) : [...prv, id]
        ) 
    }

    return (
        <Header>
            <CardHeader title="Generation" 
            svg={
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    height="40px"
                    viewBox="0 -960 960 960"
                    width="40px"
                    fill="#545454"
                >
                    <path d="m421.69-401.9-98.87-98.87q-7.23-7.23-17.7-7.09-10.48.14-18.09 7.76-7.62 7.61-7.62 17.89 0 10.29 7.62 17.9l112.74 112.08q9.34 9.61 21.78 9.61 12.45 0 21.81-9.61l232.95-232.69q7.23-7.23 7.42-17.71.19-10.47-7.42-18.09-7.62-7.61-18.03-7.61t-17.77 7.61L421.69-401.9ZM202.57-140q-25.79 0-44.18-18.39T140-202.57v-554.86q0-25.79 18.39-44.18T202.57-820h554.86q25.79 0 44.18 18.39T820-757.43v554.86q0 25.79-18.39 44.18T757.43-140H202.57Zm0-50.26h554.86q4.62 0 8.47-3.84 3.84-3.85 3.84-8.47v-554.86q0-4.62-3.84-8.47-3.85-3.84-8.47-3.84H202.57q-4.62 0-8.47 3.84-3.84 3.85-3.84 8.47v554.86q0 4.62 3.84 8.47 3.85 3.84 8.47 3.84Zm-12.31-579.48v579.48-579.48Z" />
                </svg>
            }
            />
            <Card>
                <Form className={styles.form}>
                    <div className={styles.title}>
                        <h1>Mein*e Tandempartner*in sollte, folgender Generation angehören:</h1>
                    </div>
                    <div className={styles.container}>
                        {generationenList.map((generation) => {
                        const isChecked: boolean = selectedGlobalGesuchterGeneration.includes(generation.id);
                        // console.log(isChecked);
                        return(
                            <div key={generation.id} className={styles.checkboxContainer}>
                                <Form.Check
                                    type="checkbox"
                                    value={generation.id}
                                    label={generation.generation}
                                    id={`generation-${generation.id}`}
                                    checked={isChecked}
                                    onChange={() => changeGenerationHandler(generation.id)}
                                    name="generation"
                                    className={styles.checkbox}
                                />
                            </div>);
                        })}
                    </div>

                    <div className={styles.secondTitle}>
                        <h1>Mein*e Tandempartner*in sollte, folgendes Tätigkeitsfeld haben:</h1>
                    </div>
                    <div className={styles.container}>
                        {taetigkeitsfeldList.map((taetigkeitsfeld) => {
                            const isChecked: boolean =  selectedGlobalGesuchteTaetigkeit.includes(taetigkeitsfeld.id)
                            return(
                            <div key={taetigkeitsfeld.id} className={styles.checkboxContainer}>
                                <Form.Check
                                    type="checkbox"
                                    value={taetigkeitsfeld.id}
                                    label={taetigkeitsfeld.feld}
                                    id={`taetigkeitsfeld-${taetigkeitsfeld.id}`}
                                    checked={isChecked}
                                    onChange={() => changeTaetigkeitHandler(taetigkeitsfeld.id)}
                                    name="taetigkeitsfeld"
                                    className={styles.checkbox}
                                />
                            </div>)
                        })}
                    </div>
                </Form>
                <div className={styles.btnsContainer}>
                        <div className={styles.back} onClick={btnBackHandler}>
                            <button type="button" className={styles.btnBack}>Zurück</button>
                        </div>
                        <div className={styles.next} onClick={btnNextTo}>
                            <button type="submit" className={styles.btnNext}>Weiter</button>
                        </div>
                    </div>
            </Card>
        </Header>
    );
};

export default GesuchteGenerationenAndTaetigkeit;
