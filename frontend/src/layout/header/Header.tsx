import React, { ReactNode } from "react";
import Navigation from "../navigation/Navigation";
import styles from "./Header.module.css"

interface HeaderProps {
    children: ReactNode; 
}

const Header: React.FC<HeaderProps> = ({children}) => {
    return(
        <div className={styles.headerContainer}>
        <Navigation />
        <div className={styles.header}>
        {children}
        </div>
        </div>
    )
}

export default Header;