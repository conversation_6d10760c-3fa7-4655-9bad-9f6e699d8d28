import React from "react";
import styles from "./Navigation.module.css";
// import { NavLink } from "react-router-dom";
import { ReactComponent as NdrLogo } from "./Logo.svg";
import { useNavigate } from "react-router-dom";

const Navigation = () =>{
const navigate = useNavigate()

  const logoutHandler = async () => {
    const csrfToken = localStorage.getItem('csrfToken');
      await fetch('http://localhost:8050/site/logout-react', {
          method: 'POST',
          headers: {
              'Content-Type': 'application/json',
              'X-CSRF-Token': csrfToken ?? '',
          },
          credentials: 'include',
      });

      localStorage.removeItem('csrfToken');
      navigate("/login")
  }

    return(
        <div className={styles.navBar}>
        <div className={styles.logo}>
          <h1>Generationen Mentoring</h1>
        </div>
        <div>
          {/* <NdrLogo width="70" height="70" /> */}
          <NdrLogo width="70" height="70"/>

        </div>

        <div className={styles.logout}>
          <button className={styles.btnLogout} onClick={logoutHandler}>Logout</button>
          <div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="40px"
              viewBox="0 -960 960 960"
              width="40px"
              fill="#ffffff"
            >
              <path d="M202.57-140q-25.79 0-44.18-18.39T140-202.57v-554.86q0-25.79 18.39-44.18T202.57-820h277.4v50.26h-277.4q-4.62 0-8.47 3.84-3.84 3.85-3.84 8.47v554.86q0 4.62 3.84 8.47 3.85 3.84 8.47 3.84h277.4V-140h-277.4Zm459.38-181.54-35.85-35.95 97.39-97.38H363.85v-50.26h358.82l-97.39-97.38 35.59-36.21L820-479.46 661.95-321.54Z" />
            </svg>
          </div>
        </div>
    </div>
    )
}

export default Navigation;