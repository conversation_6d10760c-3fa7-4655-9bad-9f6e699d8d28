import React, { ReactNode } from "react";
import styles from "./CardHeader.module.css"

interface CardHeaderProps {
    // btnBackHandler: () => void,
    svg: ReactNode,
    title:ReactNode,
}


const CardHeader: React.FC<CardHeaderProps> = ({svg, title}) => {
    return(
        <div className={styles.containerSiteHeader}>
        <div>
          
        </div>
        <div className={styles.containerSiteTitle}>
          <div>
            {svg}
          </div>
          <div className={styles.siteTitle}>
          {title}
          </div>
        </div>
      </div>
    )
}

export default CardHeader; 