.containerSiteHeader{
    width: 1550px;
    display: flex;
    /* flex-direction: column; */
    justify-content: space-between;
    align-items: center;
    /* border: 1px solid seagreen; */
    margin-top: 10px;
}

.back{
    display: flex;
    justify-content: center;
    align-items: center;
    /* margin-top: 50px; */
}

.btnBack{
    cursor: pointer;
    font-size: 24px;
    color: #545454; 
    background: none;
    border: none;
    /* border-radius: 5px; */
    padding: 10px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.containerSiteTitle {
    margin-top: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 300px;
    padding: 15px;
    padding-right: 600px;
    /* border: 2px solid red; */
  }
  
  .siteTitle {
    /* padding-left:15px; */
  }
  
  .siteTitle span {
    /* padding-left:15px; */
    font-size: 25px;
    color: #545454;
    /* box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .title {
    width: 1220px;
    background-color: #f9f9f9;
    padding: 10px;
    margin-bottom: 10px;
    border-radius: 10px;
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
    border: 1px solid #00206a;
  }
  
  .title h1 {
    font-size: 35px;
    color: #00206a;
    text-align: left;
  }