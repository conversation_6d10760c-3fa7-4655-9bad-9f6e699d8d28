variables:
  GIT_CLONE_PATH: $CI_BUILDS_DIR/mentoring
  GIT_CLEAN_FLAGS: none

before_script:
  - git checkout "$CI_COMMIT_REF_NAME"
  - git pull
  - git status

deploy-test:
  stage: deploy
  variables:
    CI_DEBUG_TRACE: "true"  
  tags:
    - testrunner
  script:
    - set -e
    - ./githook.sh || (echo "An error occurred during githook.sh" && exit 1)
  environment: test
  only:
    - test
    - master

deploy-prod:
  stage: deploy
  tags:
    - prodrunner
  script:
    - set -e
    - ./githook.sh || (echo "An error occurred during githook.sh" && exit 1)
  environment: production
  only:
    - prod
