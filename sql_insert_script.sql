
-- Insert statements for tandempartner table (1-10)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätig<PERSON>itsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(1, 'mentor', 'Generation Y bis Jahrgang 1995', '<PERSON><PERSON><PERSON><PERSON>', 'HH', 'Programmspezifisch', 'Egal', 'Online', 'Regelmäßig 1* pro Monat'),
(2, 'mentee', 'Babyboomer bis Jahrgang 1964', '<PERSON>gal', 'HH', '<PERSON>gal', 'Ja', 'in Präsenz', 'Regelmäß<PERSON> alle 2 Wochen'),
(3, 'mentor', '<PERSON>gal', '<PERSON><PERSON><PERSON>', 'MV', 'Verwaltungsspezifisch', 'Nein', 'Egal', 'Egal'),
(4, 'mentee', 'Generation X bis Jahrgang 1980', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON>gal', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro <PERSON>t'),
(5, 'mentor', 'Generation Z bis Jahrgang 2010', '<PERSON>gal', 'HH', '<PERSON><PERSON>', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(6, 'mentee', 'Egal', '<PERSON>blich', 'SH', 'Programmspezifisch', 'Ja', 'Egal', 'Egal'),
(7, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(8, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'NDS', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(9, 'mentor', 'Egal', 'Weiblich', 'Auslandsstudio', 'Produktionsspezifisch', 'Nein', 'Egal', 'Egal'),
(10, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Programmspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat');

-- Insert statements for tandempartner table (11-20)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(11, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'HH', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(12, 'mentee', 'Egal', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Egal'),
(13, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(14, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'SH', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(15, 'mentor', 'Egal', 'Weiblich', 'NDS', 'Programmspezifisch', 'Nein', 'Egal', 'Egal'),
(16, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(17, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'Auslandsstudio', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(18, 'mentee', 'Egal', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Ja', 'Egal', 'Egal'),
(19, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Programmspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(20, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'MV', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen');

-- Insert statements for tandempartner table (21-30)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(21, 'mentor', 'Egal', 'Weiblich', 'SH', 'Verwaltungsspezifisch', 'Nein', 'Egal', 'Egal'),
(22, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(23, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'NDS', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(24, 'mentee', 'Egal', 'Weiblich', 'Auslandsstudio', 'Programmspezifisch', 'Ja', 'Egal', 'Egal'),
(25, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(26, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'HH', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(27, 'mentor', 'Egal', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Nein', 'Egal', 'Egal'),
(28, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Programmspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(29, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'SH', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(30, 'mentee', 'Egal', 'Weiblich', 'NDS', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Egal');

-- Insert statements for tandempartner table (31-40)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(31, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(32, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'Auslandsstudio', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(33, 'mentor', 'Egal', 'Weiblich', 'HH', 'Programmspezifisch', 'Nein', 'Egal', 'Egal'),
(34, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(35, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'MV', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(36, 'mentee', 'Egal', 'Weiblich', 'SH', 'Produktionsspezifisch', 'Ja', 'Egal', 'Egal'),
(37, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Programmspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(38, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'NDS', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(39, 'mentor', 'Egal', 'Weiblich', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Nein', 'Egal', 'Egal'),
(40, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat');

-- Insert statements for tandempartner table (41-50)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(41, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'HH', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(42, 'mentee', 'Egal', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Egal'),
(43, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(44, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'SH', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(45, 'mentor', 'Egal', 'Weiblich', 'NDS', 'Programmspezifisch', 'Nein', 'Egal', 'Egal'),
(46, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(47, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'Auslandsstudio', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(48, 'mentee', 'Egal', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Ja', 'Egal', 'Egal'),
(49, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Programmspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(50, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'MV', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen');

-- Insert statements for kompetenzen table (1-10)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(1, 'mentor', 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0),
(2, 'mentee', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(3, 'mentor', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(4, 'mentee', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(5, 'mentor', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(6, 'mentee', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(7, 'mentor', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(8, 'mentee', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(9, 'mentor', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(10, 'mentee', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1);

-- Insert statements for kompetenzen table (11-20)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(11, 'mentor', 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0),
(12, 'mentee', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(13, 'mentor', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(14, 'mentee', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(15, 'mentor', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(16, 'mentee', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(17, 'mentor', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(18, 'mentee', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(19, 'mentor', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(20, 'mentee', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1);

-- Insert statements for kompetenzen table (21-30)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(21, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(22, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(23, 'mentor', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(24, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(25, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(26, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(27, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(28, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(29, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(30, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0);

-- Insert statements for kompetenzen table (31-40)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(31, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(32, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(33, 'mentor', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(34, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(35, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(36, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(37, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(38, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(39, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(40, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0);

-- Insert statements for kompetenzen table (41-50)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(41, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(42, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(43, 'mentor', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(44, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(45, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(46, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(47, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(48, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(49, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(50, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0);

-- End of SQL insert script
-- This script contains 50 entries for each of the following tables:
-- - benutzer: User accounts with basic information
-- - profil: User profiles with personal and professional details
-- - tandempartner: Mentoring preferences for matching
-- - kompetenzen: Skills and competencies for mentoring relationships