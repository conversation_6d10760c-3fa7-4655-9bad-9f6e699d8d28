-- SQL Insert Script for 50 entries in benutzer, profil, tandempartner, and kompetenzen tables
-- Generated on 2025-08-04

-- Insert statements for benutzer table (1-10)
INSERT INTO benutzer (benutzername, domaene_id, nachname, vorname, email, passwort, employeeid) VALUES
('user1', 1, '<PERSON>', '<PERSON>', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP001'),
('user2', 1, '<PERSON>', '<PERSON>', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP002'),
('user3', 2, '<PERSON>', '<PERSON>', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP003'),
('user4', 2, '<PERSON>', '<PERSON>', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP004'),
('user5', 1, '<PERSON>', 'Michael', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP005'),
('user6', 1, 'Schneider', 'Julia', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP006'),
('user7', 2, 'Wagner', 'Stefan', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP007'),
('user8', 1, 'Becker', 'Laura', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP008'),
('user9', 2, 'Hoffmann', 'Markus', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP009'),
('user10', 1, 'Schulz', 'Sabine', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP010');

-- Insert statements for benutzer table (11-20)
INSERT INTO benutzer (benutzername, domaene_id, nachname, vorname, email, passwort, employeeid) VALUES
('user11', 2, 'Koch', 'Andreas', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP011'),
('user12', 1, 'Richter', 'Nicole', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP012'),
('user13', 2, 'Wolf', 'Christian', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP013'),
('user14', 1, 'Schröder', 'Katharina', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP014'),
('user15', 2, 'Neumann', 'Daniel', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP015'),
('user16', 1, 'Schwarz', 'Melanie', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP016'),
('user17', 2, 'Zimmermann', 'Peter', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP017'),
('user18', 1, 'Braun', 'Sandra', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP018'),
('user19', 2, 'Krüger', 'Martin', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP019'),
('user20', 1, 'Hofmann', 'Claudia', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP020');

-- Insert statements for benutzer table (21-30)
INSERT INTO benutzer (benutzername, domaene_id, nachname, vorname, email, passwort, employeeid) VALUES
('user21', 2, 'Schmitt', 'Robert', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP021'),
('user22', 1, 'Werner', 'Susanne', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP022'),
('user23', 2, 'Meier', 'Frank', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP023'),
('user24', 1, 'Lehmann', 'Birgit', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP024'),
('user25', 2, 'Schmid', 'Jürgen', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP025'),
('user26', 1, 'Maier', 'Petra', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP026'),
('user27', 2, 'Köhler', 'Wolfgang', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP027'),
('user28', 1, 'Walter', 'Monika', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP028'),
('user29', 2, 'Huber', 'Klaus', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP029'),
('user30', 1, 'Kaiser', 'Angelika', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP030');

-- Insert statements for benutzer table (31-40)
INSERT INTO benutzer (benutzername, domaene_id, nachname, vorname, email, passwort, employeeid) VALUES
('user31', 2, 'Lang', 'Dieter', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP031'),
('user32', 1, 'Winkler', 'Renate', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP032'),
('user33', 2, 'Bauer', 'Bernd', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP033'),
('user34', 1, 'Keller', 'Gabriele', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP034'),
('user35', 2, 'Berger', 'Uwe', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP035'),
('user36', 1, 'Baumann', 'Silvia', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP036'),
('user37', 2, 'Schuster', 'Rainer', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP037'),
('user38', 1, 'Lorenz', 'Heike', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP038'),
('user39', 2, 'Fuchs', 'Manfred', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP039'),
('user40', 1, 'Vogt', 'Ursula', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP040');

-- Insert statements for benutzer table (41-50)
INSERT INTO benutzer (benutzername, domaene_id, nachname, vorname, email, passwort, employeeid) VALUES
('user41', 2, 'Simon', 'Herbert', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP041'),
('user42', 1, 'Sauer', 'Ingrid', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP042'),
('user43', 2, 'Haas', 'Gerhard', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP043'),
('user44', 1, 'Krause', 'Karin', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP044'),
('user45', 2, 'Jäger', 'Horst', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP045'),
('user46', 1, 'Schubert', 'Brigitte', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP046'),
('user47', 2, 'Stein', 'Helmut', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP047'),
('user48', 1, 'Bergmann', 'Christa', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP048'),
('user49', 2, 'Friedrich', 'Günter', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP049'),
('user50', 1, 'Ziegler', 'Hannelore', '<EMAIL>', '$2y$13$AbCdEfGhIjKlMnOpQrStUvWxYz1234567890AbCdEfGhIj', 'EMP050');

-- Insert statements for profil table (1-10)
INSERT INTO profil (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, geduld, fairness, empathie, loyalität, selbstdisziplin, humor, verlässlichkeit, kritikfähigkeit, hilfsbereitschaft, hands_on_mentalität, neugierde, sport, filme, musik, reisen, outdoor, kunst_und_kultur, yoga, ehrenamt, weitere_hobbys, anzeigbar) VALUES
(1, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'HH', 'Programmspezifisch', 'Ja', 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 'Lesen, Kochen', 1),
(2, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'HH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Fotografie, Gärtnern', 1),
(3, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'Produktionsspezifisch', 'Ja', 1, 0, 1, 1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 'Angeln, Schach', 1),
(4, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'MV', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Gaming, Bloggen', 1),
(5, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 1, 0, 0, 'Wandern, Geschichte', 1),
(6, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 0, 0, 1, 1, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Tanzen, Malen', 1),
(7, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'SH', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 'Segeln, Astronomie', 1),
(8, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'SH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Social Media, Podcasts', 1),
(9, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'NDS', 'Produktionsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 'Radfahren, Kochen', 1),
(10, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'NDS', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Yoga, Meditation', 1);

-- Insert statements for profil table (11-20)
INSERT INTO profil (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, geduld, fairness, empathie, loyalität, selbstdisziplin, humor, verlässlichkeit, kritikfähigkeit, hilfsbereitschaft, hands_on_mentalität, neugierde, sport, filme, musik, reisen, outdoor, kunst_und_kultur, yoga, ehrenamt, weitere_hobbys, anzeigbar) VALUES
(11, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'Auslandsstudio', 'Programmspezifisch', 'Ja', 1, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 1, 1, 0, 0, 0, 'Reisen, Sprachen', 1),
(12, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 1, 0, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 1, 0, 0, 'Sprachen, Reisen', 1),
(13, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'HH', 'Produktionsspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 'Musik, Fotografie', 1),
(14, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'HH', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Design, Technologie', 1),
(15, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 'Politik, Geschichte', 1),
(16, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Film, Theater', 1),
(17, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'SH', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 'Garten, Handwerk', 1),
(18, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'SH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Mode, Kunst', 1),
(19, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'NDS', 'Produktionsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 'Sport, Kochen', 1),
(20, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'NDS', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Literatur, Musik', 1);

-- Insert statements for profil table (21-30)
INSERT INTO profil (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, geduld, fairness, empathie, loyalität, selbstdisziplin, humor, verlässlichkeit, kritikfähigkeit, hilfsbereitschaft, hands_on_mentalität, neugierde, sport, filme, musik, reisen, outdoor, kunst_und_kultur, yoga, ehrenamt, weitere_hobbys, anzeigbar) VALUES
(21, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Ja', 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 0, 'Wirtschaft, Politik', 1),
(22, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'Auslandsstudio', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 1, 0, 0, 1, 1, 1, 0, 0, 1, 0, 'Digitale Medien, Reisen', 1),
(23, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'HH', 'Produktionsspezifisch', 'Ja', 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 'Architektur, Design', 1),
(24, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'HH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Nachhaltigkeit, Umwelt', 1),
(25, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 'Technologie, Innovation', 1),
(26, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Medienproduktion, Audio', 1),
(27, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'SH', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 'Finanzen, Wirtschaft', 1),
(28, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'SH', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Journalismus, Recherche', 1),
(29, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'NDS', 'Produktionsspezifisch', 'Ja', 1, 0, 1, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 'Technik, Handwerk', 1),
(30, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'NDS', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Organisation, Management', 1);

-- Insert statements for profil table (31-40)
INSERT INTO profil (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, geduld, fairness, empathie, loyalität, selbstdisziplin, humor, verlässlichkeit, kritikfähigkeit, hilfsbereitschaft, hands_on_mentalität, neugierde, sport, filme, musik, reisen, outdoor, kunst_und_kultur, yoga, ehrenamt, weitere_hobbys, anzeigbar) VALUES
(31, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Auslandsstudio', 'Produktionsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 'Internationale Beziehungen, Sprachen', 1),
(32, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Kulturelle Vielfalt, Kommunikation', 1),
(33, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'HH', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 'Medienrecht, Journalismus', 1),
(34, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Videobearbeitung, Social Media', 1),
(35, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 'Personalmanagement, Coaching', 1),
(36, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Redaktion, Storytelling', 1),
(37, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'SH', 'Produktionsspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 'Kameraführung, Beleuchtung', 1),
(38, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'SH', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Projektmanagement, Teamführung', 1),
(39, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'NDS', 'Programmspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 'Moderation, Interviewtechnik', 1),
(40, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'NDS', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Tonproduktion, Sounddesign', 1);

-- Insert statements for profil table (41-50)
INSERT INTO profil (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, geduld, fairness, empathie, loyalität, selbstdisziplin, humor, verlässlichkeit, kritikfähigkeit, hilfsbereitschaft, hands_on_mentalität, neugierde, sport, filme, musik, reisen, outdoor, kunst_und_kultur, yoga, ehrenamt, weitere_hobbys, anzeigbar) VALUES
(41, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Ja', 1, 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 0, 1, 0, 0, 'Internationale Politik, Diplomatie', 1),
(42, 'mentee', 'Generation X bis Jahrgang 1980', 'Weiblich', 'Auslandsstudio', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Auslandskorrespondenz, Reportage', 1),
(43, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'HH', 'Verwaltungsspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 'Controlling, Finanzen', 1),
(44, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'HH', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Digitale Formate, Multimedia', 1),
(45, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'MV', 'Produktionsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 1, 0, 0, 1, 1, 0, 0, 1, 1, 0, 0, 1, 'Regie, Produktion', 1),
(46, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'HR, Personalentwicklung', 1),
(47, 'mentor', 'Babyboomer bis Jahrgang 1964', 'Männlich', 'SH', 'Programmspezifisch', 'Ja', 1, 0, 1, 1, 1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1, 0, 0, 1, 'Nachrichten, Politik', 1),
(48, 'mentee', 'Generation Z bis Jahrgang 2010', 'Weiblich', 'SH', 'Produktionsspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 0, 0, 1, 1, 0, 'Dokumentarfilm, Recherche', 1),
(49, 'mentor', 'Generation X bis Jahrgang 1980', 'Männlich', 'NDS', 'Verwaltungsspezifisch', 'Ja', 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 1, 0, 0, 1, 0, 0, 1, 'Strategie, Organisationsentwicklung', 1),
(50, 'mentee', 'Generation Y bis Jahrgang 1995', 'Weiblich', 'NDS', 'Programmspezifisch', 'Nein', 0, 1, 1, 0, 1, 1, 0, 0, 1, 1, 1, 0, 1, 1, 1, 0, 0, 1, 0, 'Kultur, Unterhaltung', 1);

-- Insert statements for tandempartner table (1-10)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(1, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'HH', 'Programmspezifisch', 'Egal', 'Online', 'Regelmäßig 1* pro Monat'),
(2, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'HH', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(3, 'mentor', 'Egal', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Nein', 'Egal', 'Egal'),
(4, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(5, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'HH', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(6, 'mentee', 'Egal', 'Weiblich', 'SH', 'Programmspezifisch', 'Ja', 'Egal', 'Egal'),
(7, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(8, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'NDS', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(9, 'mentor', 'Egal', 'Weiblich', 'Auslandsstudio', 'Produktionsspezifisch', 'Nein', 'Egal', 'Egal'),
(10, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Programmspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat');

-- Insert statements for tandempartner table (11-20)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(11, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'HH', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(12, 'mentee', 'Egal', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Egal'),
(13, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(14, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'SH', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(15, 'mentor', 'Egal', 'Weiblich', 'NDS', 'Programmspezifisch', 'Nein', 'Egal', 'Egal'),
(16, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(17, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'Auslandsstudio', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(18, 'mentee', 'Egal', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Ja', 'Egal', 'Egal'),
(19, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Programmspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(20, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'MV', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen');

-- Insert statements for tandempartner table (21-30)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(21, 'mentor', 'Egal', 'Weiblich', 'SH', 'Verwaltungsspezifisch', 'Nein', 'Egal', 'Egal'),
(22, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(23, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'NDS', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(24, 'mentee', 'Egal', 'Weiblich', 'Auslandsstudio', 'Programmspezifisch', 'Ja', 'Egal', 'Egal'),
(25, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(26, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'HH', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(27, 'mentor', 'Egal', 'Weiblich', 'MV', 'Produktionsspezifisch', 'Nein', 'Egal', 'Egal'),
(28, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Programmspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(29, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'SH', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(30, 'mentee', 'Egal', 'Weiblich', 'NDS', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Egal');

-- Insert statements for tandempartner table (31-40)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(31, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(32, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'Auslandsstudio', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(33, 'mentor', 'Egal', 'Weiblich', 'HH', 'Programmspezifisch', 'Nein', 'Egal', 'Egal'),
(34, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(35, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'MV', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(36, 'mentee', 'Egal', 'Weiblich', 'SH', 'Produktionsspezifisch', 'Ja', 'Egal', 'Egal'),
(37, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Programmspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(38, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'NDS', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(39, 'mentor', 'Egal', 'Weiblich', 'Auslandsstudio', 'Verwaltungsspezifisch', 'Nein', 'Egal', 'Egal'),
(40, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat');

-- Insert statements for tandempartner table (41-50)
INSERT INTO tandempartner (benutzer_id, rolle, generation, geschlecht, standort, tätigkeitsfeld, führungskraft, treffen_art, treffen_frequenz) VALUES
(41, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'HH', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(42, 'mentee', 'Egal', 'Weiblich', 'MV', 'Verwaltungsspezifisch', 'Ja', 'Egal', 'Egal'),
(43, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Produktionsspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(44, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'SH', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(45, 'mentor', 'Egal', 'Weiblich', 'NDS', 'Programmspezifisch', 'Nein', 'Egal', 'Egal'),
(46, 'mentee', 'Generation X bis Jahrgang 1980', 'Männlich', 'Egal', 'Verwaltungsspezifisch', 'Ja', 'Online', 'Regelmäßig 1* pro Monat'),
(47, 'mentor', 'Generation Z bis Jahrgang 2010', 'Egal', 'Auslandsstudio', 'Egal', 'Nein', 'in Präsenz', 'Regelmäßig alle 2 Wochen'),
(48, 'mentee', 'Egal', 'Weiblich', 'HH', 'Produktionsspezifisch', 'Ja', 'Egal', 'Egal'),
(49, 'mentor', 'Generation Y bis Jahrgang 1995', 'Männlich', 'Egal', 'Programmspezifisch', 'Nein', 'Online', 'Regelmäßig 1* pro Monat'),
(50, 'mentee', 'Babyboomer bis Jahrgang 1964', 'Egal', 'MV', 'Egal', 'Ja', 'in Präsenz', 'Regelmäßig alle 2 Wochen');

-- Insert statements for kompetenzen table (1-10)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(1, 'mentor', 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0),
(2, 'mentee', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(3, 'mentor', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(4, 'mentee', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(5, 'mentor', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(6, 'mentee', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(7, 'mentor', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(8, 'mentee', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(9, 'mentor', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(10, 'mentee', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1);

-- Insert statements for kompetenzen table (11-20)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(11, 'mentor', 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0),
(12, 'mentee', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(13, 'mentor', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(14, 'mentee', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(15, 'mentor', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(16, 'mentee', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(17, 'mentor', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(18, 'mentee', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(19, 'mentor', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(20, 'mentee', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1);

-- Insert statements for kompetenzen table (21-30)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(21, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(22, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(23, 'mentor', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(24, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(25, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(26, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(27, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(28, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(29, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(30, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0);

-- Insert statements for kompetenzen table (31-40)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(31, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(32, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(33, 'mentor', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(34, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(35, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(36, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(37, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(38, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(39, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(40, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0);

-- Insert statements for kompetenzen table (41-50)
INSERT INTO kompetenzen (benutzer_id, rolle, internes_netzwerk, ki, ndr_knowhow, externe_erfahrung, digitalisierung, externes_netzwerk, socialmedia, moderation, worklifebalance, präsentationen, resilienz, gender_diversity, wunsch_internes_netzwerk, wunsch_ki, wunsch_ndr_knowhow, wunsch_externe_erfahrung, wunsch_digitalisierung, wunsch_externes_netzwerk, wunsch_socialmedia, wunsch_moderation, wunsch_worklifebalance, wunsch_präsentationen, wunsch_resilienz, wunsch_gender_diversity) VALUES
(41, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(42, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(43, 'mentor', 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1),
(44, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(45, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(46, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0),
(47, 'mentor', 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1),
(48, 'mentee', 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1),
(49, 'mentor', 0, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 1, 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0),
(50, 'mentee', 1, 0, 1, 0, 1, 0, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0);

-- End of SQL insert script
-- This script contains 50 entries for each of the following tables:
-- - benutzer: User accounts with basic information
-- - profil: User profiles with personal and professional details
-- - tandempartner: Mentoring preferences for matching
-- - kompetenzen: Skills and competencies for mentoring relationships