<?php

namespace app\commands;

use app\controllers\TsvController;
use app\models\Abbanutzer;
use app\models\Campuszertifikate;
use app\models\Forum;
use app\models\Forumnutzer;
use app\models\Ldapbenutzer;
use app\models\Wikinutzer;
use Yii;
use yii\console\Controller;

class LdapController extends Controller
{
    public $dir = 'import';

    public function __construct($id, $module, $config = [])
    {
        if (!is_dir($this->dir)) {
            mkdir($this->dir);
        }
        parent::__construct($id, $module, $config);
    }

    public function actionReset()
    {
        $sql = "TRUNCATE TABLE forumnutzer";
        Yii::$app->sql->execute($sql);
    }

    public function actionIndex()
    {
        Forumnutzer::updateAll(["inactive" => 3]);
        $foren = Forum::find()->all();
        foreach ($foren as $forum) {
            echo "Starte Import " . $forum->forum . "\n";
            if (empty($forum->importfile) || !file_exists($this->dir . '/' . $forum->importfile)) {
                echo "Datei nicht gefunden " . $forum->importfile;
                continue;
            }
            $file = $this->dir . '/' . $forum->importfile;

            $file_handle = fopen($file, 'r');
            $cnt = 0;
            while (!feof($file_handle)) {
                $cnt++;

                $line = fgets($file_handle);
                if (!empty($line)) {
                    [$user_id, $name, $name2, $email, $gruppe, $inaktiv] = explode(",", $line);
                    $email = trim($email);
                    if (!empty($email)) {
                        if (!Yii::$app->ldap->bind_admin()) {
                            echo "Fehler beim LDAP bind!\n";
                            return false;
                        }
                        if (!Yii::$app->ldap->search_account($email)) {
                            echo "Fehler beim LDAP Search!\n";
                            return false;
                        }
                        $found = Yii::$app->ldap->result['count'];
                    } else {
                        $found = 0;
                    }
                    if ($found === 0) {
                        if (!substr_count($email, "@ndr.de")) {
                            echo " -extern---> ";
                        }
                        echo trim($email) . " (" . trim($name) . ") nicht im ldap gefunden";
                        $forumnutzer = Forumnutzer::find()
                            ->where(["=", "email", $email])
                            ->andWhere(["=", "forum_id", $forum->id])
                            ->andWhere(["=", "forum_userid", $user_id])
                            ->one();
                        if (empty($forumnutzer)) {
                            echo " nicht im ADCheck vorhanden";
                            $forumnutzer = new Forumnutzer();
                            $forumnutzer->status = "new";
                            $forumnutzer->status_date = date("d.m.Y");
                            $forumnutzer->addBemerkung("Eintrag wurde neu gefunden");
                        } else if ($forumnutzer->status == "new" || $forumnutzer->status == "delete" || $forumnutzer->status == "recheck") {
                            echo " schon im ADCheck vorhanden, wird auf recheck gesetzt vom Status " . $forumnutzer->status;
                            $forumnutzer->status = "recheck";
                            $forumnutzer->status_date = date("d.m.Y");
                            $forumnutzer->addBemerkung("Existierender Eintrag wurde nicht genehmigt oder gelöscht");
                        } else if ($forumnutzer->status == "signed" && $forumnutzer->needsResign()) {
                            echo " schon im ADCheck vorhanden, Bestätigung ist aber veraltet";
                            $forumnutzer->status = "new";
                            $forumnutzer->status_date = date("d.m.Y");
                            $forumnutzer->addBemerkung("Status zurückgesetzt, die Bestätigung muss nach einem Jahr erneuert werden");
                        } else {
                            echo " schon im ADCheck vorhanden, Status: " . $forumnutzer->status;
                        }
                        $forumnutzer->email = $email;
                        $forumnutzer->nutzer = $name;
                        $forumnutzer->name = $name2;
                        $forumnutzer->forum_id = $forum->id;
                        $forumnutzer->forum_userid = $user_id;
                        $forumnutzer->inactive = $inaktiv;
                        $forumnutzer->group = $gruppe;
                        $forumnutzer->save();
                        echo "\n";
                    }
                }
            }
            /**
             * Alle löschen, die in diesem lauf nicht gefunden wurden!
             */
            $forum->notifyAdmins();
            fclose($file_handle);
        }
        Forumnutzer::deleteAll(["inactive" => 3]);
    }


    public function actionAbba()
    {
        Abbanutzer::updateAll(["inactive" => 3]);
        $file = $this->dir . '/abba_benutzer.txt';

        if (empty($file) || !file_exists($file)) {
            echo "Datei nicht gefunden " . $file;
            return false;
        }

        $file_handle = fopen($file, 'r');
        $cnt = 0;
        $foundcnt = 0;
        $errcnt = 0;
        $checkedmails = [];

        while (!feof($file_handle)) {
            $cnt++;
            $line = fgets($file_handle);
            if (!empty($line) && substr_count($line, ";;")) {
                [$db_name, $name, $email] = explode(";;", $line);
                $email = trim(strtolower($email ?? ""));
                if (!empty($email)) {
                    if (!isset($checkedmails[trim($email)])) {
                        Yii::$app->ldap->bind_admin();
                        Yii::$app->ldap->search_account(trim($email));
                        $found = Yii::$app->ldap->result['count'];
                        $checkedmails[trim($email)] = $found;
                    } else {
                        $found = $checkedmails[trim($email)];
                        echo "refound " . $email . PHP_EOL;
                    }
                    if ($found === 0) {
                        echo $email . " nicht im ldap gefunden\n";
                        $abbanutzer = Abbanutzer::find()->where(["=", "email", $email])->one();
                        if (empty($abbanutzer)) {
                            $abbanutzer = new Abbanutzer();
                        }
                        $abbanutzer->db_name = $db_name;
                        $abbanutzer->name = $name;
                        $abbanutzer->email = $email;
                        $abbanutzer->found_date = date("d.m.Y");
                        $abbanutzer->inactive = 0;
                        $abbanutzer->save();
                    } else {
                        $foundcnt++;
                    }
                }
            }
        }
        /**
         * Alle löschen, die in diesem lauf nicht gefunden wurden!
         */
        Abbanutzer::deleteAll(["inactive" => 3]);
        fclose($file_handle);
        echo $foundcnt . " Datensätze von " . $cnt . " Datensätzen im LDAP gefunden\n";
    }

    public function actionXwiki()
    {
        Wikinutzer::updateAll(["inactive" => 3]);
        ini_set("memory_limit", "4096M");
        $foundcnt = 0;
        $newcnt = 0;
        $cmd = 'curl -u "ActiveDirectoryCheck:' . Yii::$app->params["wikipwd"] . '" "https://wiki.ndr.mobi/rest/wikis/xwiki/query?q=,doc.object(XWiki.XWikiUsers)%20as%20obj&type=xwql&className=XWiki.XWikiUsers&media=json"';
        $erg = exec($cmd);
        $data = json_decode($erg);
        if (empty($data->searchResults)) {
            echo "Keine Daten gefunden, breche lieber ab!";
            return false;
        }
        foreach ($data->searchResults as $item) {
            foreach ($item->object->properties as $prop) {
                if ($prop->name == "email") {
                    $email = trim($prop->value);
                }
                if ($prop->name == "first_name") {
                    $vorname = trim($prop->value);
                }
                if ($prop->name == "last_name") {
                    $nachname = trim($prop->value);
                }
                $benutzername = trim($item->pageName);
            }
            if (!empty($email)) {
                if (!isset($checkedmails[trim($email)])) {
                    Yii::$app->ldap->bind_admin();
                    Yii::$app->ldap->search_account(trim($email));
                    $found = Yii::$app->ldap->result['count'];
                    $checkedmails[trim($email)] = $found;
                } else {
                    $found = $checkedmails[trim($email)];
                    echo "refound " . $email . PHP_EOL;
                }
                if ($found === 0) {
                    echo $email;
                    $wikinutzer = Wikinutzer::find()->where(["=", "email", $email])->one();
                    if (empty($wikinutzer)) {
                        $wikinutzer = new Wikinutzer();
                        $wikinutzer->status = 0;
                        $newcnt++;
                        echo " nicht im ADCheck vorhanden";
                    } else if ($wikinutzer->status == 1 && $wikinutzer->needsResign()) {
                        echo " schon im ADCheck vorhanden, Bestätigung ist aber veraltet";
                        $wikinutzer->status = 0;
                        $wikinutzer->addBemerkung("Status zurückgesetzt, die Bestätigung muss nach einem Jahr erneuert werden");
                        $newcnt++;
                    } else if ($wikinutzer->status == 0) {
                        echo " schon im ADCheck vorhanden, bisher aber nicht bestätigt";
                        $wikinutzer->status = 0;
                        $wikinutzer->addBemerkung("Bisher nicht bestätigter Eintrag, bitte prüfen!");
                        $newcnt++;
                    } else {
                        echo " schon als bestätigter Eintrag im ADCheck vorhanden";
                    }
                    echo PHP_EOL;
                    $wikinutzer->benutzername = $benutzername;
                    $wikinutzer->name = $vorname . " " . $nachname;
                    $wikinutzer->email = $email;
                    $wikinutzer->found_date = date("d.m.Y");
                    $wikinutzer->inactive = 0;
                    $wikinutzer->save();
                } else {
                    $foundcnt++;
                }
            }
        }
        Wikinutzer::deleteAll(["inactive" => 3]);
        if ($newcnt) {
            $mail = Yii::$app->mailer->compose("notify_wiki", ["anzahl" => $newcnt]);
            $mail->setFrom(['<EMAIL>' => 'AD Check Mailer']);
            $mail->setSubject("Übersicht über ungültige Benutzer im Wiki");
            $mail->setTo("<EMAIL>");
            $mail->send();
        }
    }

    public function actionCampus()
    {

        Campuszertifikate::deleteAll();
        Campuszertifikate::dateieinlesen();
        $mailer = Yii::$app->mailer;
        $mailer->sendNotifyMailAll();
        $mailer->sendNotifyOkMail();
    }

    public function actionGetaduser()
    {
        Ldapbenutzer::deleteAll();
        Yii::$app->ldap->bind_admin();

        $alphabet = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
        foreach ($alphabet as $item) {
            Yii::$app->ldap->get_personen($item);
            $data = Yii::$app->ldap->resultArray;
            foreach ($data as $key => $person) {
                /*
                 * Nur Anwender anzeigen, keine Admin und Testaccounts!
                 * Und nicht die email Adressen ivz, ext, oder fm
                 */
                if (empty($data[$key]['mail']) || (substr_count($key, 'OU=PERSONEN') == 0)

                    || (!empty($data[$key]['employeetype']) && ($data[$key]['employeetype'] == 'Funktionsaccount'))
                    || (substr_count($data[$key]['mail'], '.<EMAIL>') > 0)
                    || (substr_count($data[$key]['mail'], '.<EMAIL>') > 0)
                    || (substr_count($data[$key]['mail'], '.<EMAIL>') > 0)
                    || (substr_count($data[$key]['mail'], '.<EMAIL>') > 0)
                    || (substr_count($data[$key]['mail'], '.<EMAIL>') > 0)
                    || (substr_count($data[$key]['mail'], '.<EMAIL>') > 0)
                    || (substr_count($data[$key]['mail'], '@tagesschau.de') > 0)
                    || (substr_count($data[$key]['mail'], '@ndrmedia.de') > 0)
                    || (substr_count($data[$key]['mail'], '@ndrstudiokueche.de') > 0)
                    || (empty($data[$key]['givenname']))
                    || (empty($data[$key]['sn']))
                    || (empty($data[$key]['employeeid']))
                ) {
                    unset($data[$key]);
                } else {

                    $ldapbenutzer = new Ldapbenutzer();
                    //$ldapbenutzer->name = $data[$key]['displayname'];
                    if ($data[$key]['sn'] != 'NULL' && !empty($data[$key]['sn'])
                        && $data[$key]['givenname'] != 'NULL' && !empty($data[$key]['givenname'])
                    ) {
                        $ldapbenutzer->name = $data[$key]['givenname'].' ' . $data[$key]['sn'];
                    }
                    $ldapbenutzer->email = $data[$key]['mail'];
                    if (!empty($data[$key]['employeeid'])) {
                        $ldapbenutzer->employeeid = $data[$key]['employeeid'];
                    }
                    if (!empty($data[$key]['manager'])) {
                        $managerelem = $data[$key]['manager'];
                        $managerdetails = explode(',', $managerelem);

                        $managerstr = $managerdetails[0];
                        $managerarray = explode(' ', $managerstr);

                        $manager_nr = $managerarray[sizeof($managerarray) - 1];
                        $ldapbenutzer->employeeid_vorgesetzter = $manager_nr;
                    }
                    $ldapbenutzer->save();

                }
            }
        }
    }
}
