<?php
/**
 * Test Data Console Controller
 * 
 * This controller provides console commands for managing test data
 * in the mentoring application.
 * 
 * Usage:
 * php yii test-data/generate - Generate test data
 * php yii test-data/clean - Clean test data
 * php yii test-data/verify - Verify test data
 */

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\console\ExitCode;
use yii\db\Exception;

class TestDataController extends Controller
{
    /**
     * Generate test data for profil, kompetenzen, and tandempartner tables
     * 
     * @return int Exit code
     */
    public function actionGenerate()
    {
        $this->stdout("🚀 Generating test data for mentoring application...\n", \yii\helpers\Console::FG_CYAN);
        
        try {
            // Read the SQL file
            $sqlFile = Yii::getAlias('@app/migrations/insert_test_data.sql');
            if (!file_exists($sqlFile)) {
                $this->stderr("❌ SQL file not found: $sqlFile\n", \yii\helpers\Console::FG_RED);
                return ExitCode::DATAERR;
            }
            
            $sql = file_get_contents($sqlFile);
            if ($sql === false) {
                $this->stderr("❌ Could not read SQL file: $sqlFile\n", \yii\helpers\Console::FG_RED);
                return ExitCode::IOERR;
            }
            
            // Split SQL into individual statements
            $statements = array_filter(
                array_map('trim', explode(';', $sql)),
                function($stmt) {
                    return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
                }
            );
            
            $db = Yii::$app->db;
            $transaction = $db->beginTransaction();
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($statements as $statement) {
                try {
                    $db->createCommand($statement)->execute();
                    $successCount++;
                    
                    // Show progress for major operations
                    if (strpos($statement, 'INSERT INTO `benutzer`') !== false) {
                        $this->stdout("✓ Created test users in benutzer table\n", \yii\helpers\Console::FG_GREEN);
                    } elseif (strpos($statement, 'INSERT INTO `profil`') !== false) {
                        $this->stdout("✓ Created test entries in profil table\n", \yii\helpers\Console::FG_GREEN);
                    } elseif (strpos($statement, 'INSERT INTO `kompetenzen`') !== false) {
                        $this->stdout("✓ Created test entries in kompetenzen table\n", \yii\helpers\Console::FG_GREEN);
                    } elseif (strpos($statement, 'INSERT INTO `tandempartner`') !== false) {
                        $this->stdout("✓ Created test entries in tandempartner table\n", \yii\helpers\Console::FG_GREEN);
                    }
                    
                } catch (Exception $e) {
                    $errorCount++;
                    $this->stdout("⚠ Warning: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_YELLOW);
                    // Continue with other statements even if one fails
                }
            }
            
            $transaction->commit();
            
            $this->stdout("\n=== Migration Summary ===\n", \yii\helpers\Console::FG_CYAN);
            $this->stdout("Successful statements: $successCount\n");
            $this->stdout("Failed statements: $errorCount\n");
            
            if ($errorCount === 0) {
                $this->stdout("✅ Test data generation completed successfully!\n", \yii\helpers\Console::FG_GREEN);
            } else {
                $this->stdout("⚠ Test data generation completed with some warnings.\n", \yii\helpers\Console::FG_YELLOW);
            }
            
            // Verify the data
            $this->actionVerify();
            
            return ExitCode::OK;
            
        } catch (\Exception $e) {
            if (isset($transaction)) {
                $transaction->rollBack();
            }
            $this->stderr("❌ Error: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            $this->stderr("Migration failed and was rolled back.\n", \yii\helpers\Console::FG_RED);
            return ExitCode::SOFTWARE;
        }
    }
    
    /**
     * Clean test data from the database
     * 
     * @return int Exit code
     */
    public function actionClean()
    {
        $this->stdout("🧹 Cleaning test data...\n", \yii\helpers\Console::FG_CYAN);
        
        if (!$this->confirm("This will delete all test data (user IDs 101-150). Continue?")) {
            $this->stdout("Operation cancelled.\n");
            return ExitCode::OK;
        }
        
        try {
            $db = Yii::$app->db;
            $transaction = $db->beginTransaction();
            
            // Delete in reverse order to respect foreign key constraints
            $db->createCommand("DELETE FROM tandempartner WHERE benutzer_id BETWEEN 101 AND 150")->execute();
            $this->stdout("✓ Cleaned tandempartner table\n", \yii\helpers\Console::FG_GREEN);
            
            $db->createCommand("DELETE FROM kompetenzen WHERE benutzer_id BETWEEN 101 AND 150")->execute();
            $this->stdout("✓ Cleaned kompetenzen table\n", \yii\helpers\Console::FG_GREEN);
            
            $db->createCommand("DELETE FROM profil WHERE benutzer_id BETWEEN 101 AND 150")->execute();
            $this->stdout("✓ Cleaned profil table\n", \yii\helpers\Console::FG_GREEN);
            
            $db->createCommand("DELETE FROM benutzer WHERE id BETWEEN 101 AND 150")->execute();
            $this->stdout("✓ Cleaned benutzer table\n", \yii\helpers\Console::FG_GREEN);
            
            $transaction->commit();
            
            $this->stdout("✅ Test data cleaned successfully!\n", \yii\helpers\Console::FG_GREEN);
            return ExitCode::OK;
            
        } catch (\Exception $e) {
            if (isset($transaction)) {
                $transaction->rollBack();
            }
            $this->stderr("❌ Error: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::SOFTWARE;
        }
    }
    
    /**
     * Verify test data in the database
     * 
     * @return int Exit code
     */
    public function actionVerify()
    {
        $this->stdout("\n=== Data Verification ===\n", \yii\helpers\Console::FG_CYAN);
        
        try {
            $db = Yii::$app->db;
            
            $benutzerCount = $db->createCommand("SELECT COUNT(*) FROM benutzer WHERE id BETWEEN 101 AND 150")->queryScalar();
            $this->stdout("Test users: $benutzerCount/50\n");
            
            $profilCount = $db->createCommand("SELECT COUNT(*) FROM profil WHERE benutzer_id BETWEEN 101 AND 150")->queryScalar();
            $this->stdout("Profile entries: $profilCount/50\n");
            
            $kompetenzenCount = $db->createCommand("SELECT COUNT(*) FROM kompetenzen WHERE benutzer_id BETWEEN 101 AND 150")->queryScalar();
            $this->stdout("Competency entries: $kompetenzenCount/50\n");
            
            $tandempartnerCount = $db->createCommand("SELECT COUNT(*) FROM tandempartner WHERE benutzer_id BETWEEN 101 AND 150")->queryScalar();
            $this->stdout("Tandempartner entries: $tandempartnerCount/50\n");
            
            $mentorCount = $db->createCommand("SELECT COUNT(*) FROM profil WHERE benutzer_id BETWEEN 101 AND 150 AND rolle = 'Mentor'")->queryScalar();
            $menteeCount = $db->createCommand("SELECT COUNT(*) FROM profil WHERE benutzer_id BETWEEN 101 AND 150 AND rolle = 'Mentee'")->queryScalar();
            $this->stdout("Mentors: $mentorCount, Mentees: $menteeCount\n");
            
            if ($benutzerCount > 0) {
                $this->stdout("\n📋 Sample test users:\n", \yii\helpers\Console::FG_CYAN);
                $sampleUsers = $db->createCommand("SELECT id, vorname, nachname, email FROM benutzer WHERE id BETWEEN 101 AND 105 ORDER BY id")->queryAll();
                foreach ($sampleUsers as $user) {
                    $this->stdout("  ID {$user['id']}: {$user['vorname']} {$user['nachname']} ({$user['email']})\n");
                }
                
                $this->stdout("\n🎯 Usage tips:\n", \yii\helpers\Console::FG_CYAN);
                $this->stdout("- Test user IDs: 101-150\n");
                $this->stdout("- Email format: <EMAIL>\n");
                $this->stdout("- Mix of Mentors and Mentees with various competencies\n");
                $this->stdout("- Different generations, locations, and departments\n");
            }
            
            return ExitCode::OK;
            
        } catch (\Exception $e) {
            $this->stderr("❌ Error: " . $e->getMessage() . "\n", \yii\helpers\Console::FG_RED);
            return ExitCode::SOFTWARE;
        }
    }
}
