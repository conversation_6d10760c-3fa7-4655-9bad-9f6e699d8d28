<?php
$this->params['title'] = 'Systeminformationen';
$this->params['breadcrumbs'][] = $this->params['title'];
?>

<div style="display: flex">
    <div class="card m-n" style="flex: 50%;">
        <div class="card-header">
            Serverinformation
        </div>
        <div class="card-body">
            <div class="form-group">
                <label class="col-lg-4 control-label">
                    Servername
                </label>
                <div class="col-lg-8">
                    <?= $_SERVER['SERVER_NAME'] ?>
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg-4 control-label">
                    GIT Branch
                </label>
                <div class="col-lg-8">
                    <span id='branch'>-</span>
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg-4 control-label">
                    letztes Update
                </label>
                <div class="col-lg-8">
                    <span id='lastupdate'>-</span>
                </div>
            </div>
        </div>
    </div>
    <div class="card m-n" style="flex: 50%;">
        <div class="card-header">
            GIT Logfile
        </div>
        <div class="card-body" id="logfile" style="height:400px;overflow-y:scroll;">
        </div>
    </div>
</div>


<?php
$this->registerJs(
    "getAdmininfos();",
    \yii\web\View::POS_READY
);

?>
<script>

    function getAdmininfos() {

        $.get('/admin/changelog_json', function (data) {

            var response = JSON.parse(data);
            $('#lastupdate').html(response.lastchange);
            $('#branch').html(response.branch);

            $.each(response.log, function (index, value) {

                $('#logfile').append("<div data-id='" + index + "'>" + value + "</div>");
            });

        });
    }

</script>
