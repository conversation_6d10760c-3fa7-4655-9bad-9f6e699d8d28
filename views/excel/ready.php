<?php

use yii\helpers\Html;
use yii\helpers\Url;

/**
 * @var $cnt int
 * @var $data []
 * @var $errors []
 *
 */

?>
<h1>Import abgeschlossen</h1>

<div id="standorte" class="well-lg alert alert-success">
    Es wurden <?= $cnt ?> Datensätze verarbeitet<BR>
</div>


<?php if (!empty($errors)) { ?>
    <div class="well-lg alert alert-danger" style="border:1px solid #002244;overflow:scroll; height:200px;">
        <?= print_r($errors,true) ?>
    </div>
<?php } ?>

<h1>Inhalt der Excel Datei</h1>

<div id="" style="border:1px solid #002244;overflow:scroll; height:200px;">

    <table>
        <?php

        foreach ($data as $row) {
            echo "<tr>";
            foreach ($row as $cell) {
                echo "<td>" . $cell . "</td>";
            }
            echo "</tr>";
        }

        ?>
    </table>

</div>
