<?php

use yii\helpers\Html;
use app\common\override\yii2\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Rolle */
/* @var $form \app\common\override\yii2\ActiveForm */

?>

<div class="form-container rolle-form">
	<h1><?= $model->isNewRecord ? 'Neue Rolle erstellen' : 'Rolle bearbeiten' ?></h1>

	<?php $form = ActiveForm::begin(); ?>

	<div class="form-group">
		<?= $form->field($model, 'rolle')->textInput(['maxlength' => true, 'placeholder' => 'Rollenname eingeben']) ?>
	</div>

	<div class="form-group">
		<?= $form->field($model, 'beschreibung')->textarea(['rows' => 6, 'placeholder' => 'Beschreibung der Rolle eingeben...']) ?>
	</div>

	<div class="form-navigation">
		<?= Html::a('Zurück zur Übersicht', ['index'], ['class' => 'btn btn-secondary']) ?>
		<?= Html::submitButton($model->isNewRecord ? 'Rolle erstellen' : 'Änderungen speichern', ['class' => 'btn btn_nav_blue']) ?>
	</div>

	<?php ActiveForm::end(); ?>

</div>
