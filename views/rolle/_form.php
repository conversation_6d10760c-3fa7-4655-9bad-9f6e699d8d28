<?php

use yii\helpers\Html;
use app\common\override\yii2\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Rolle */
/* @var $form \app\common\override\yii2\ActiveForm */

?>

<div class="form-container rolle-form">

	<?php $form = ActiveForm::begin(); ?>

	<?= $form->field($model, 'rolle')->textInput(['maxlength' => true]) ?>

	<?= $form->field($model, 'beschreibung')->textarea(['rows' => 6]) ?>

	<div class="form-group">
		<?= Html::submitButton('Speichern', ['class' => 'btn btn_nav_blue']) ?>
	</div>

	<?php ActiveForm::end(); ?>

</div>
