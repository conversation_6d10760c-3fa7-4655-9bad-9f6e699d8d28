<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Konfiguration */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-container konfiguration-form">
    <h1><?= $model->isNewRecord ? 'Neue Konfiguration erstellen' : 'Konfiguration bearbeiten' ?></h1>

    <?php $form = ActiveForm::begin(); ?>

    <div class="field-group">
        <h4>Konfigurationsparameter</h4>

        <div class="form-group">
            <?= $form->field($model, 'name')->textInput(['maxlength' => true, 'placeholder' => 'Parametername eingeben']) ?>
        </div>

        <div class="form-group">
            <?= $form->field($model, 'wert')->textInput(['maxlength' => true, 'placeholder' => 'Parameterwert eingeben']) ?>
        </div>

        <div class="form-group">
            <?= $form->field($model, 'beschreibung')->textInput(['maxlength' => true, 'placeholder' => 'Beschreibung des Parameters']) ?>
        </div>
    </div>

    <div class="field-group">
        <h4>Optionen</h4>
        <div class="form-group">
            <?= $form->field($model, 'deprecated')->checkbox(['label'=>'Diesen Parameter als "veraltet" markieren.']) ?>
        </div>
    </div>

    <div class="form-navigation">
        <?= Html::a('Zurück zur Übersicht', ['index'], ['class' => 'btn btn-secondary']) ?>
        <?= Html::submitButton($model->isNewRecord ? 'Konfiguration erstellen' : 'Änderungen speichern', ['class' => 'btn btn_nav_blue']) ?>
    </div>

    <?php ActiveForm::end(); ?>

</div>
