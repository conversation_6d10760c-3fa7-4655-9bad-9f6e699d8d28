<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;
use app\models\LabelManager;

/* @var $this yii\web\View */
/* @var $model app\models\LabelManager */
/* @var $form yii\widgets\ActiveForm */
//aktiviert die Tooltips wenn vorhanden
$js = <<<js
    $(function () {
        $('[data-toggle="tooltip"]').tooltip()
    });
js;
$this->registerJs($js);
?>

	<div class="form-container label-manager-form row">

		<div class="col-md-4">

			<?php $form = ActiveForm::begin(['id' => 'lmform']); ?>

			<div class="card">
				<div class="card-header">Listenmanagement</div>
				<div class="card-body">

					<?= $form->field($model, 'liste')->dropDownList(LabelManager::getListen(),
						['prompt' => 'Bitte auswählen',
							'readonly' => !$model->isNewRecord,
							'disabled' => !$model->isNewRecord]);
					?>

					<?= $form->field($model, 'value')->textInput(['readonly' => !$model->isNewRecord,
						'disabled' => !$model->isNewRecord, 'maxlength' => true, 'title' => 'Der Wert in der Datenbank',
						'data-toggle' => 'tooltip']) ?>
					<?= $form->field($model, 'label')->textInput(['maxlength' => true, 'title' => 'Der anzuzeigende Wert',
						'data-toggle' => 'tooltip']) ?>
					<?= $form->field($model, 'sortorder')->textInput(['title' => 'Die Reihenfolge in der die Werte in Selectboxen angezeigt werden',
						'data-toggle' => 'tooltip']) ?>
					<?= $form->field($model, 'deprecated')->checkbox(['title' => 'Wenn aktiviert, wird der Wert nicht mehr in der Oberfläche angeboten.',
						'data-toggle' => 'tooltip']); ?>
					<?= $form->field($model, 'comment')->textarea(['rows' => 6]) ?>
				</div>
			</div>

			<div class="form-group">
				<?= !$model->isNewRecord ? Html::Button('neuer Eintrag', ['onClick' => 'new_labelmanager_value()', 'class' => 'btn btn_nav_blue pull-left']) : "" ?>
				<?= Html::Button('Speichern', ['onClick' => 'save_labelmanager_value()', 'class' => 'btn btn_nav_blue pull-right']) ?>
			</div>

			<?php ActiveForm::end(); ?>

		</div>
		<div class="col-md-8">

			<div class="card">
				<div class="card-header" id="listhead">Keine Liste gewählt</div>
				<div class="card-body" id="listview" style="overflow-y:auto;max-height:400px;padding:0px">

					<?= $this->render("emptylist") ?>
				</div>
			</div>

		</div>

	</div>

<?php
$this->registerJsFile(Yii::$app->request->baseUrl . "/js/labelmanager.js", ['depends' => [\yii\web\JqueryAsset::class]]);
