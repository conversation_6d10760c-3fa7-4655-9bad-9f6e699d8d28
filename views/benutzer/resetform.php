<?php

use app\common\override\yii2\ActiveForm;
use app\models\Benutzer;
use yii\helpers\Html;
use yii\web\View;

/* @var $this View */
/* @var $model Benutzer */
/* @var $form ActiveForm */
/* @var $form ActiveForm */

?>

<div class="benutzer-form">

	<h1>Änderung des Passworts</h1>
	<p>
		<?php if (empty($model->passwort)) { ?>
			Bitte geben Sie zur Änderung Ihres Passworts Ihre E-Mail Adresse sowie Ihr bestehendes Passwort ein.
		<?php } ?>
		Geben Sie bitte das gewünschte <strong>neue Passwort</strong> ein und bestätigen Sie dieses bitte im Feld <strong>Passwort bestätigen</strong>.
	</p>

	<?php $form = ActiveForm::begin(); ?>
	<input type="hidden" value="<?= $model->id ?>" name="id">

	<div class="row">
		<?php if (empty($model->passwort)) { ?>
			<div class="col-md-6">
				<?= $form->field($model, 'email', ["size" => "small"])->textInput(['maxlength' => true]) ?>
			</div>
			<div class="col-md-6">
				<?= $form->field($model, 'passwort', ["size" => "small"])->passwordInput(['maxlength' => true]) ?>
			</div>
		<?php } else { ?>
			<?= $form->field($model, 'email', ["size" => "small"])->hiddenInput() ?>
			<?= $form->field($model, 'passwort', ["size" => "small"])->hiddenInput() ?>
		<?php } ?>
		<div class="col-md-6">
			<?= $form->field($model, 'passwort_neu1', ["size" => "small"])->passwordInput(['maxlength' => true]) ?>
		</div>
		<div class="col-md-6">
			<?= $form->field($model, 'passwort_neu2', ["size" => "small"])->passwordInput(['maxlength' => true]) ?>
		</div>
	</div>


	<div class="form-group">
		<div class="col-md-12 text-right">
			<?= Html::submitButton('Passwort speichern', ['name' => 'action', 'value' => 'save', 'class' => 'btn btn-success']) ?>
		</div>
	</div>
	<?php ActiveForm::end(); ?>

</div>
