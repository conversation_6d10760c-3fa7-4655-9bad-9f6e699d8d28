<?php

use app\models\Benutzer;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\ActiveForm;
use yii\helpers\ArrayHelper;
use app\models\Rolle;

/* @var $this yii\web\View */
/* @var $model app\models\Benutzer */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-container benutzer-form">
	<h1><?= $model->isNewRecord ? 'Neuen Benutzer erstellen' : 'Benutzer bearbeiten' ?></h1>

	<?php $form = ActiveForm::begin(); ?>

	<div class="field-group">
		<h4><PERSON><PERSON> zuweisen</h4>
		<div class="form-group">
			<?= $form->field($model, 'rollenIds')
				->checkboxList(Rolle::getRollenList(), ['class' => 'checkbox-list'])
				->hint('Wählen Sie alle Rollen aus, die dem Nutzer zugeordnet werden sollen.');
			?>
		</div>
	</div>

	<div class="field-group">
		<h4>Persönliche Daten</h4>
		<div class="row">
			<div class="col-md-6">
				<?= $form->field($model, 'email')->textInput(['maxlength' => true, 'placeholder' => 'E-Mail-Adresse eingeben']) ?>
			</div>
			<div class="col-md-6">
				<?= $form->field($model, 'email_alternativ')->textInput(['maxlength' => true, 'placeholder' => 'Alternative E-Mail-Adresse']) ?>
			</div>
		</div>
		<div class="row">
			<div class="col-md-6">
				<?= $form->field($model, 'vorname')->textInput(['maxlength' => true, 'placeholder' => 'Vorname eingeben']) ?>
			</div>
			<div class="col-md-6">
				<?= $form->field($model, 'nachname')->textInput(['maxlength' => true, 'placeholder' => 'Nachname eingeben']) ?>
			</div>
		</div>
	</div>
	<div class="field-group">
		<h4>Passwort</h4>
		<div class="row">
			<div class="col-md-6">
				<?php
				if ($model->isNewRecord) {
					$pw1hint = "Passwort";
					$pw2hint = "Passwort Bestätigung";
				} else {
					$pw1hint = "Passwort (nur eingeben, wenn es geändert werden soll!)";
					$pw2hint = "Passwort Bestätigung (nur eingeben, wenn es geändert werden soll!)";
				}
				?>
				<?= $form->field($model, 'passwort_neu1')->passwordInput(['maxlength' => true, 'placeholder' => 'Neues Passwort eingeben'])->label($pw1hint) ?>
			</div>
			<div class="col-md-6">
				<?= $form->field($model, 'passwort_neu2')->passwordInput(['maxlength' => true, 'placeholder' => 'Passwort bestätigen'])->label($pw2hint) ?>
			</div>
		</div>
	</div>

	<div class="form-navigation">
		<?php if (!$model->isNewRecord) { ?>
			<?= Html::button('Passwort zurücksetzen', ['onClick' => 'reset_password(' . $model->id . ')', 'class' => 'btn btn-secondary']) ?>
		<?php } ?>
		<?= Html::submitButton($model->isNewRecord ? 'Benutzer erstellen' : 'Änderungen speichern', ['class' => 'btn btn_nav_blue']) ?>
	</div>

	<?php ActiveForm::end(); ?>

</div>
