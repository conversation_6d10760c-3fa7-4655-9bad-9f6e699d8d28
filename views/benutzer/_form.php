<?php

use app\models\Benutzer;
use yii\helpers\Html;
use yii\web\View;
use yii\widgets\ActiveForm;
use yii\helpers\ArrayHelper;
use app\models\Rolle;

/* @var $this yii\web\View */
/* @var $model app\models\Benutzer */
/* @var $form yii\widgets\ActiveForm */
?>

<div class="form-container benutzer-form">
	<?php $form = ActiveForm::begin(); ?>
	<div class="row">
		<div class="col-md-12">
			<?= $form->field($model, 'rollenIds')
				->checkboxList(Rolle::getRollenList())
				->hint('Wählen Sie alle Rollen aus, die dem Nutzer zugeordnet werden sollen.');
			?>
		</div>
	</div>

	<div class="row">
		<div class="col-md-6">
			<?= $form->field($model, 'email')->textInput(['maxlength' => true]) ?>
		</div>
		<div class="col-md-6">
			<?= $form->field($model, 'email_alternativ')->textInput(['maxlength' => true]) ?>
		</div>
	</div>
	<div class="row">
		<div class="col-md-6">
			<?= $form->field($model, 'vorname')->textInput(['maxlength' => true]) ?>
		</div>
		<div class="col-md-6">
			<?= $form->field($model, 'nachname')->textInput(['maxlength' => true]) ?>
		</div>
	</div>
	<div class="row">
		<div class="col-md-6">
			<?php
			if ($model->isNewRecord) {
				$pw1hint = "Passwort";
				$pw2hint = "Passwort Bestätigung";
			} else {
				$pw1hint = "Passwort (nur eingeben, wenn es geändert werden soll!)";
				$pw2hint = "Passwort Bestätigung (nur eingeben, wenn es geändert werden soll!)";
			}
			?>
			<?= $form->field($model, 'passwort_neu1')->passwordInput(['maxlength' => true])->label($pw1hint) ?>
		</div>
		<div class="col-md-6">
			<?= $form->field($model, 'passwort_neu2')->passwordInput(['maxlength' => true])->label($pw2hint) ?>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<div class="form-group">
				<?php if (!$model->isNewRecord) { ?>
					<?= Html::button('Passwort zurücksetzen', ['onClick' => 'reset_password(' . $model->id . ')', 'class' => 'btn btn_nav_blue']) ?>
				<?php } ?>
				<?= Html::submitButton('Speichern', ['class' => 'btn btn_nav_blue pull-right']) ?>
			</div>
		</div>
	</div>

	<?php ActiveForm::end(); ?>

</div>
