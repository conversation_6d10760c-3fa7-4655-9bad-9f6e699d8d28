<?php

use app\models\Benutzer;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model app\models\Profil */
/* @var $form \yii\widgets\ActiveForm*/
/* @var $todo */

//echo "<pre>";var_dump($model->getOptsGeneration()); echo Yii::$app->user->getIdentity()->getId();       exit;
//if ($todo) {}
?>
<div class="checkbox-list">
    <?= $form->field($model, 'geduld')->checkbox() ?>
    <?= $form->field($model, 'fairness')->checkbox() ?>
    <?= $form->field($model, 'empathie')->checkbox() ?>
    <?= $form->field($model, 'loyalität')->checkbox() ?>
    <?= $form->field($model, 'selbstdisziplin')->checkbox() ?>
    <?= $form->field($model, 'humor')->checkbox() ?>
    <?= $form->field($model, 'verlässlichkeit')->checkbox() ?>
    <?= $form->field($model, 'kritikfähigkeit')->checkbox() ?>
    <?= $form->field($model, 'hilfsbereitschaft')->checkbox() ?>
    <?= $form->field($model, 'hands_on_mentalität')->checkbox() ?>
    <?= $form->field($model, 'neugierde')->checkbox() ?>
</div>


