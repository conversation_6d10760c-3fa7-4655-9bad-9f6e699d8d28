<?php

use yii\helpers\Html;
use app\common\override\yii2\GridView;
use yii\widgets\Pjax;
use app\common\override\yii2\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Profil */
/* @var $rolle */

$this->title = 'Kompetenzens';
$this->params['breadcrumbs'][] = $this->title;

$readonly = !empty($readonly);
?>
<div class="form-container profil-form">
    <h1 class="ff_brand_c c_darkblue fs-xxl">
        <a href="/site/index"><?= \rmrevin\yii\fontawesome\FontAwesome::icon("home", ['class' => ''])  ?></a>
        Mein Profil vervollständigen
    </h1>

    <?php $form = ActiveForm::begin(['id' => 'ProfilForm', 'method' => 'post', 'action' => '/profil/update']); ?>

    <input type="hidden" value="<?= $rolle ?>" name="Profil[rolle]" id="Profil[rolle]">
    <input type="hidden" value="<?= Yii::$app->user->getId() ?>" name="Profil[benutzer_id]" id="Profil[benutzer_id]">

    <div class="d-flex" style="flex-direction: column; gap: 20px;">
        <div class="select-item">
            <h3>Ich gehöre folgender Generation an:</h3>
            <?= $form->field($model, 'generation')->dropDownList($model::optsGeneration(),['prompt' => 'Generation auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>

        <div class="select-item">
            <h3>Mein Tätigkeitsfeld im NDR ist:</h3>
            <?= $form->field($model, 'tätigkeitsfeld')->dropDownList($model::optsTätigkeitsfeld(),['prompt' => 'Tätigkeitsfeld auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>

        <div class="select-item">
            <h3>Ich bin Führungskraft:</h3>
            <?= $form->field($model, 'führungskraft')->dropDownList($model::optsFührungskraft(),['prompt' => 'auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>

        <div class="select-item">
            <h3>Mein Standort ist:</h3>
            <?= $form->field($model, 'standort')->dropDownList($model::optsStandort(),['prompt' => 'Standort auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>

        <div class="select-item">
            <h3>Mein Geschlecht:</h3>
            <?= $form->field($model, 'geschlecht')->dropDownList($model::optsGeschlecht(),['prompt' => 'Geschlecht auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>
    </div>

    <div class="field-group">
        <h3>Das ist mir wichtig:</h3>
        <?php
        echo $this->render('_profil_wichtig', [
            'model' => $model,
            'form' => $form,
            'rolle' => $rolle,
            'readonly' => $readonly,
        ]);
        ?>
    </div>

    <div class="field-group">
        <h3>In meiner Freizeit interessiert mich:</h3>
        <?php
        echo $this->render('_profil_freizeit', [
            'model' => $model,
            'form' => $form,
            'rolle' => $rolle,
            'readonly' => $readonly,
        ]);
        ?>
    </div>

    <div class="form-navigation">
        <?= Html::a('zurück', ['/kompetenzen/index?rolle='.$rolle], ['class' => 'btn btn-secondary']) ?>
        <?= Html::submitButton('weiter', ['class' => 'btn btn_nav_blue']) ?>
    </div>

    <?php ActiveForm::end(); ?>
</div>


<!--    <br class="clearfix"/><br class="clearfix"/>-->
<!--</div>-->