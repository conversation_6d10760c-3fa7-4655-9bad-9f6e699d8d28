<?php

use yii\helpers\Html;
use app\common\override\yii2\GridView;
use yii\widgets\Pjax;
use app\common\override\yii2\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Profil */
/* @var $rolle */

$this->title = 'Kompetenzens';
$this->params['breadcrumbs'][] = $this->title;

$readonly = !empty($readonly);
?>
<div class="form-container profil-form">
    <h1 class="ff_brand_c c_darkblue fs-xxl">
        <a href="/site/index"><?= \rmrevin\yii\fontawesome\FontAwesome::icon("home", ['class' => '']) . " " ?></a>
        Mein Profil vervollständigen
    </h1>

    <?php $form = ActiveForm::begin(['id' => 'ProfilForm', 'method' => 'post', 'action' => '/profil/update']); ?>

    <?php
    echo $this->render('_form', [
        'model' => $model,
        'form' => $form,
        'rolle' => $rolle,
        'readonly' => $readonly,
    ]); ?>

    <div class="field-group">
        <h3>Das ist mir wichtig:</h3>
        <?php
        echo $this->render('_profil_wichtig', [
            'model' => $model,
            'form' => $form,
            'rolle' => $rolle,
            'readonly' => $readonly,
        ]);
        ?>
    </div>

    <div class="field-group">
        <h3>In meiner Freizeit interessiert mich:</h3>
        <?php
        echo $this->render('_profil_freizeit', [
            'model' => $model,
            'form' => $form,
            'rolle' => $rolle,
            'readonly' => $readonly,
        ]);
        ?>
    </div>

    <div class="d-flex">

        <div class="col-sm-9">
            <div>
                <div class="row" style="padding-left: 2rem;padding-right: 2rem;">
                 
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div>
            <?= Html::a('&nbsp;&nbsp;zurück&nbsp;&nbsp;', ['/kompetenzen/index?rolle='.$rolle], ['class' => 'btn btn_nav_blue']) ?>
            <?= Html::submitButton('&nbsp;&nbsp;weiter&nbsp;&nbsp;', ['class' => 'btn btn_nav_blue']) // Speichern und Weiter ?>
        </div>
        </div>
    </div>
    <?php ActiveForm::end(); ?>
</div>


<!--    <br class="clearfix"/><br class="clearfix"/>-->
<!--</div>-->