<?php
use yii\helpers\Html;
use app\common\override\yii2\GridView;
use yii\widgets\Pjax;
use app\common\override\yii2\ActiveForm;
use app\models\Benutzer;
use app\models\Kompetenzen;
use kartik\base\BootstrapInterface;
use kartik\switchinput\SwitchInput;

/* @var $this yii\web\View */
/* @var $model app\models\Profil */
/* @var $rolle */
/* @var $kompetenzen_logged_in_user \app\models\Kompetenzen */

$this->registerJsFile("@web/js/common.js", ["depends" => \yii\web\JqueryAsset::class, 'POS_HEAD']);

$this->title = 'Kompetenzens';
$this->params['breadcrumbs'][] = $this->title;

if(Yii::$app->session->hasFlash('success')) {
    echo "<div class='success'>".Yii::$app->session->getFlash('success')."</div>";
}

$readonly = !empty($readonly);
$form = ActiveForm::begin(['method' => 'post', 'action' => [Yii::$app->urlManager->createUrl(['profil/changevalues'])],]);
$href_img = '<a href="/matching/profildetails?id=' . $model['benutzer_id'] . '" class="c_bright c_bright_oh td_0  logo"><img src="/css/images/matching_dummy_female.png" height="120"></a>';
$benutzer = Benutzer::findOne($model['benutzer_id']); ?>

<h1 class="ff_brand_c c_darkblue fs-xxl">
    <a href="/site/index"><?= \rmrevin\yii\fontawesome\FontAwesome::icon("home", ['class' => '']) . " " ?></a>
</h1>
<div class="kompetenzen-index">
    <input type="hidden" value="<?= $rolle ?>" name="Profil[rolle]" id="Profil[rolle]">
    <input type="hidden" value="<?= Yii::$app->user->getId() ?>" name="Profil[benutzer_id]" id="Profil[benutzer_id]">

    <div class="row" style="display: flex; margin-bottom: 2px; ">
        <div class="col-sm-2  mb-r" style="">
            <?php echo $href_img; ?>
            <br/>

            <?php echo $benutzer->vorname . " " . $benutzer->nachname; ?>
        </div>
        <div class="col-sm-1  mb-r" style="">
        </div>
        <div class="col-sm-6 card  bg-light mb-l" style="min-height: 2rem;">
            <table style="border-spacing: 0; height: 50%; ">
                <tr class="profil-tr">
                    <td style="width: 350px;"><?=  strtoupper($rolle) ?><br/></td>
                    <td style="width: 350px;">Tätigkeit: <?= "&nbsp;" .$form->field($model, 'tätigkeitsfeld')->dropDownList($model::optsTätigkeitsfeld(), ['prompt' => 'Tätigkeitsfeld auswählen', 'class' => 'profilupdate-control-control '])->label(false) ?></td>
                </tr>
                <tr style="border-spacing: 0; " class="profil-tr">
                    <td style="width: 350px;">Generation: <?= "&nbsp;" .$form->field($model, 'generation')->dropDownList($model::optsGeneration(), ['prompt' => 'Generation auswählen', 'class' => 'profilupdate-control-control '])->label(false) ?><br/></td>
                    <td style="width: 350px;">Führungskraft: <?= "&nbsp;" .$form->field($model, 'führungskraft')->dropDownList($model::optsFührungskraft(), ['prompt' => 'Führungskraft auswählen', 'class' => 'profilupdate-control-control '])->label(false) ?></td>
                </tr>
                <tr style="border-spacing: 0; " class="profil-tr">
                    <td style="width: 350px;">Geschlecht: <?= "&nbsp;" .$form->field($model, 'geschlecht')->dropDownList($model::optsGeschlecht(), ['prompt' => 'Geschlecht auswählen', 'class' => 'profilupdate-control-control '])->label(false) ?><br/></td>
                    <td style="width: 350px;">Standort: <?= "&nbsp;" .$form->field($model, 'standort')->dropDownList($model::optsStandort(), ['prompt' => 'Standort auswählen', 'class' => 'profilupdate-control-control '])->label(false) ?></td>
                </tr>
                <tr class="profil-tr">
                    <td style="width: 350px;"><br/></td>
                    <td style="width: 350px;">Treffen in Präsenz: <?= "&nbsp;" .$form->field($model, 'geschlecht')->dropDownList($model::optsGeschlecht(), ['prompt' => 'Geschlecht auswählen', 'class' => 'profilupdate-control-control '])->label(false) ?></td>
                </tr>
                <tr class="profil-tr">
                    <td style="width: 350px;"> </td>
                    <td style="width: 350px;">Treffen: <?= "&nbsp;" .$form->field($model, 'geschlecht')->dropDownList($model::optsGeschlecht(), ['prompt' => 'Geschlecht auswählen', 'class' => 'profilupdate-control-control '])->label(false) ?></td>
                </tr>
            </table>
        </div>

        <div class="col-sm-3  mb-r" style="">

        </div>
    </div>

    <div class="row" style="display: flex; margin-bottom: 20px; ">
        <div class="col-sm-3  mb-r" style="">
        </div>
        <div class="col-sm-6  mb-l" style="min-height: 2rem;">
            <table>
                <tr>
                    <?php
                    $escapedParam = array();
                    ($model->anzeigbar === 1) ? $escapedParam['anzeigbar'] = 0: $escapedParam['anzeigbar'] = 1;
                    $escapedParam['benutzer_id'] = $model->benutzer_id;
                    $escapedParam['rolle'] = $model->rolle;

                    $escapedParam = json_encode($escapedParam);
                    ?>
                    <td>
                        <div id="sw_retardo_1" style="display: flex; align-items: center;">
                            <span style="white-space: nowrap; margin-right: 5px;">Ich bin derzeit offen für weitere Tandempartner*innen:</span>
                            <div style="display: inline-block;"><?= $form->field($model, 'anzeigbar')->widget(SwitchInput::classname(), [
                                'type' => SwitchInput::CHECKBOX,
                                'inlineLabel' => false,
                                'name' => 'anzeigbar',
                                'value' => 555,
                                'pluginEvents' => [
                                    "init.bootstrapSwitch" => "function() { sendSwitchRequest(" . $escapedParam . "); }",
                                    "switchChange.bootstrapSwitch" => "function() {  sendSwitchRequest(" . $escapedParam . ");}",
                                ]
                            ])->label(false); ?></div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td> <?= Html::a('&nbsp;&nbsp;Für andere Option (Mentor*in oder Mentee & Mentor*in) registrieren &nbsp;&nbsp;', ['/site/register'], ['class' => 'btn btn_nav_blue_500px']) ?></td>
                </tr>

            </table>
        </div>

        <div class="col-sm-3  mb-r" style="">
        </div>
    </div>

    <div class="row" style="display: flex; margin-bottom: 20px; ">
            <div class="col-sm-4" style=" min-width:40%;">
                <div>
                    <div class="row" style="padding-left: 2rem;padding-right: 2rem; min-width:40%;">
                        <h3 style="font-weight: bold;">Das ist mir wichtig:</h3>
                    </div>
                </div>
                <br class="clearfix"/>
                <div style="margin-left: 25px;">
                    <?php
                    echo $this->render('_profil_wichtig', [
                        'model' => $model,
                        'form' => $form,
                        'rolle' => $rolle,
                        'readonly' => $readonly,
                    ]);
                    ?>
                </div>
            </div>
            <div class="col-sm-4" style=" min-width:40%;">
                <div>
                    <div class="row" style="padding-left: 2rem;padding-right: 2rem; min-width:40%;">
                        <h3 style="font-weight: bold;">In meiner Freizeit interessiert mich:</h3>
                    </div>
                </div>
                <!--        </div>-->
                <!--        <div class="col-sm-6">-->
                <br class="clearfix"/>
                <div style="margin-left: 25px;">
                    <?php
                    echo $this->render('_profil_freizeit', [
                        'model' => $model,
                        'form' => $form,
                        'rolle' => $rolle,
                        'readonly' => $readonly,
                    ]);
                    ?>
                </div>
            </div>
            <div class="col-sm-2">
                <div>
                </div>
            </div>
    </div>

    <div class="row" style="display: flex; margin-bottom: 20px; ">

        <div class="col-sm-4" style="margin: auto;">
            <table style="padding-top: 2px; padding-left: 15px; margin-top: 2px;">
                <tr style=" padding: 2px;margin-top: 0px;">
                    <th> <h3 style="font-weight: bold;">Meine Wunschkompetenzen</h3></th>
                </tr>
                <tr style="">
                    <td colspan="1">
<!--                        <div style="max-height: 80px; max-width: 300px; width: 280px; overflow: auto;">-->
                            <table style=" width: 100%;">
                                <?php foreach ($kompetenzen_logged_in_user as $key => $kptenz_val) {
                                    if ($key == "internes_netzwerk") { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'internes_netzwerk')->checkbox() ?></td>
                                        </tr>
                                    <?php   }
                                    if ($key == "ki") { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'ki')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "ndr_knowhow") { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'ndr_knowhow')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "externe_erfahrung") { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'externe_erfahrung')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "digitalisierung" ) { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'digitalisierung')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "externes_netzwerk") { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'externes_netzwerk')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "socialmedia" ) { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'socialmedia')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "moderation" ) { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'moderation')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "worklifebalance" ) { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'worklifebalance')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "präsentationen" ) { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'präsentationen')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "resilienz" ) { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'resilienz')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                    if ($key == "gender_diversity" ) { ?>
                                        <tr>
                                            <td class=""><?= $form->field($kompetenzen_logged_in_user, 'gender_diversity')->checkbox() ?></td>
                                        </tr>
                                    <?php }

                                } ?>

                            </table>
<!--                        </div>-->
                    </td>
                </tr>
            </table>
        </div>
        <div class="col-sm-8">
            <div>
                <div class="row" style="padding-left: 2rem;padding-right: 2rem;">
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="display: flex; margin-bottom: 20px; ">
        <div class="col-sm-9">
            <div>
                <div class="row" style="padding-left: 2rem;padding-right: 2rem;">
                    <button onclick="history.back()" class="btn btn_nav_blue" style="width: 100px;">Zurück</button>
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div>
                <?= Html::submitButton('&nbsp;&nbsp;Speichern&nbsp;&nbsp;', ['class' => 'btn btn_nav_blue']) // Speichern und Weiter  ?>
            </div>
        </div>
    </div>

    <?php ActiveForm::end(); ?>
</div>

<!--    <br class="clearfix"/><br class="clearfix"/>-->
<!--</div>-->