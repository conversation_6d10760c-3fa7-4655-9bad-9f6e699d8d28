<?php

use app\models\Benutzer;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model app\models\Profil */
/* @var $form \yii\widgets\ActiveForm*/
/* @var $rolle */
/* @var $todo */

//echo "<pre>";var_dump($model->getOptsGeneration()); echo Yii::$app->user->getIdentity()->getId();       exit;
//if ($todo) {}
//echo "<pre>";var_dump($model);exit;
//$obj = $model::optsGeneration();
//echo "<pre>";var_dump($model);exit;
?>
<?php
// This partial is no longer used as the form content has been moved to index.php
// Keeping this file for potential future use or backwards compatibility
?>