<?php

use app\models\Benutzer;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model app\models\Profil */
/* @var $form \yii\widgets\ActiveForm*/
/* @var $rolle */
/* @var $todo */

//echo "<pre>";var_dump($model->getOptsGeneration()); echo Yii::$app->user->getIdentity()->getId();       exit;
//if ($todo) {}
//echo "<pre>";var_dump($model);exit;
//$obj = $model::optsGeneration();
//echo "<pre>";var_dump($model);exit;
?>
<div class="form-container">
<input type="hidden" value="<?= $rolle ?>" name="Profil[rolle]" id="Profil[rolle]">
<input type="hidden" value="<?= Yii::$app->user->getId() ?>" name="Profil[benutzer_id]" id="Profil[benutzer_id]">
<div class="d-flex">
    <div class="col-sm-2">
        <div>
        </div>
    </div>
    <div class="col-sm-4">

        <div>
            <div class="row" style="padding-left: 2rem;padding-right: 2rem;">
                <h3>Ich gehöre folgender Generation an:</h3>
            </div>
        </div>
    </div>
    <div>
        <div class="col-sm-6">
            <?= $form->field($model, 'generation')->dropDownList($model::optsGeneration(),['prompt' => 'Generation auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>
    </div>
</div>

<div class="d-flex">
    <div class="col-sm-2">
        <div>
        </div>
    </div>
    <div class="col-sm-4">
        <div>
            <div class="row" style="padding-left: 2rem;padding-right: 2rem;">
                <h3>Mein Tätigkeitsfeld im NDR ist:</h3>
            </div>
        </div>
    </div>
    <div>
        <div class="col-sm-6">
            <?= $form->field($model, 'tätigkeitsfeld')->dropDownList($model::optsTätigkeitsfeld(),['prompt' => 'Tätigkeitsfeld auswählen', 'class' => 'mentoring-control '])->label(false) ?>

        </div>
    </div>
</div>

<div class="d-flex">
    <div class="col-sm-2">
        <div>
        </div>
    </div>
    <div class="col-sm-4">
        <div>
            <div class="row" style="padding-left: 2rem;padding-right: 2rem;">
                <h3>Ich bin Führungskraft:</h3>
            </div>
        </div>
    </div>
    <div>
        <div class="col-sm-6">
            <?= $form->field($model, 'führungskraft')->dropDownList($model::optsFührungskraft(),['prompt' => 'auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>
    </div>
</div>

<div class="d-flex">
    <div class="col-sm-2">
        <div>
        </div>
    </div>
    <div class="col-sm-4">
        <div>
            <div class="row" style="padding-left: 2rem;padding-right: 2rem;">
                <h3>Mein Standort ist:</h3>
            </div>
        </div>
    </div>
    <div>
        <div class="col-sm-6">
            <?= $form->field($model, 'standort')->dropDownList($model::optsStandort(),['prompt' => 'Standort auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>
    </div>
</div>

<div class="d-flex">
    <div class="col-sm-2">
        <div>
        </div>
    </div>
    <div class="col-sm-4">
        <div>
            <div class="row" style="padding-left: 2rem;padding-right: 2rem;">
                <h3>Mein Geschlecht:</h3>
            </div>
        </div>
    </div>
    <div>
        <div class="col-sm-6">
            <?= $form->field($model, 'geschlecht')->dropDownList($model::optsGeschlecht(),['prompt' => 'Geschlecht auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        </div>
    </div>
</div>
</div>