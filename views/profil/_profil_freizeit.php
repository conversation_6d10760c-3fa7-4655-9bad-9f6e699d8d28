<?php

use app\models\Benutzer;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model app\models\Profil */
/* @var $form \yii\widgets\ActiveForm*/
/* @var $todo */

//echo "<pre>";var_dump($model->getOptsGeneration()); echo Yii::$app->user->getIdentity()->getId();       exit;
//if ($todo) {}
?>
<div class="checkbox-list">
    <?= $form->field($model, 'sport')->checkbox() ?>
    <?= $form->field($model, 'filme')->checkbox() ?>
    <?= $form->field($model, 'musik')->checkbox() ?>
    <?= $form->field($model, 'reisen')->checkbox() ?>
    <?= $form->field($model, 'outdoor')->checkbox() ?>
    <?= $form->field($model, 'kunst_und_kultur')->checkbox() ?>
    <?= $form->field($model, 'yoga')->checkbox() ?>
    <?= $form->field($model, 'ehrenamt')->checkbox() ?>
</div>
<div class="form-group">
    <?= $form->field($model, 'weitere_hobbys')->textInput(['placeholder' => 'Weitere Hobbys eingeben...']) ?>
</div>
