<?php

use app\models\Benutzer;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;
use yii\widgets\Pjax;

/* @var $this yii\web\View */
/* @var $model app\models\Kompetenzen */
/* @var $form \yii\widgets\ActiveForm*/
/* @var $todo */
/* @var $rolle */

$benutzer = Benutzer::findOne(Yii::$app->user->id);

if ($todo === "chooseBeides") { ?>

    <div class="form-container kompetenzen-form">
        <?php $form = ActiveForm::begin(['id' => 'Kompetenzen', 'action' => Url::to(['kompetenzen/updatementeementor'])]); ?>

        <?= $form->field($model, 'rolle')->hiddenInput(['value' => $rolle, 'style' => 'display:none;'])->label(false) ?>
        <?= $form->field($model, 'menteementor_kategorie')->hiddenInput(['value' => 'menteementor', 'style' => 'display:none;'])->label(false) ?>
        <?= $form->field($model, 'benutzer_id')->hiddenInput(['value' => Yii::$app->user->getId(), 'style' => 'display:none;'])->label(false) ?>

        <div class="field-group">
            <h3>Meine vorhandenen Kompetenzen</h3>
            <div class="checkbox-list">
                <?= $form->field($model, 'internes_netzwerk')->checkbox() ?>
                <?= $form->field($model, 'ki')->checkbox() ?>
                <?= $form->field($model, 'ndr_knowhow')->checkbox() ?>
                <?= $form->field($model, 'externe_erfahrung')->checkbox() ?>
                <?= $form->field($model, 'digitalisierung')->checkbox() ?>
                <?= $form->field($model, 'externes_netzwerk')->checkbox() ?>
                <?= $form->field($model, 'socialmedia')->checkbox() ?>
                <?= $form->field($model, 'moderation')->checkbox() ?>
                <?= $form->field($model, 'worklifebalance')->checkbox() ?>
                <?= $form->field($model, 'präsentationen')->checkbox() ?>
                <?= $form->field($model, 'resilienz')->checkbox() ?>
                <?= $form->field($model, 'gender_diversity')->checkbox() ?>
            </div>
        </div>

        <div class="field-group">
            <h3>Meine Wunschkompetenzen</h3>
            <div class="checkbox-list">
                <?= $form->field($model, 'wunsch_internes_netzwerk')->checkbox() ?>
                <?= $form->field($model, 'wunsch_ki')->checkbox() ?>
                <?= $form->field($model, 'wunsch_ndr_knowhow')->checkbox() ?>
                <?= $form->field($model, 'wunsch_externe_erfahrung')->checkbox() ?>
                <?= $form->field($model, 'wunsch_digitalisierung')->checkbox() ?>
                <?= $form->field($model, 'wunsch_externes_netzwerk')->checkbox() ?>
                <?= $form->field($model, 'wunsch_socialmedia')->checkbox() ?>
                <?= $form->field($model, 'wunsch_moderation')->checkbox() ?>
                <?= $form->field($model, 'wunsch_worklifebalance')->checkbox() ?>
                <?= $form->field($model, 'wunsch_präsentationen')->checkbox() ?>
                <?= $form->field($model, 'wunsch_resilienz')->checkbox() ?>
                <?= $form->field($model, 'wunsch_gender_diversity')->checkbox() ?>
            </div>
        </div>
        <div class="form-navigation">
            <?= Html::a('zurück', ['site/register'], ['class' => 'btn btn-secondary']) ?>
            <?= Html::submitButton('weiter', ['class' => 'btn btn_nav_blue']) ?>
        </div>
        <?php ActiveForm::end(); ?>
    </div>

<?php }
else {
    ?>

    <div class="form-container kompetenzen-form">

        <?php $form = ActiveForm::begin(['id' => 'Kompetenzen', 'action' => Url::to(['kompetenzen/update'])]); ?>

        <?= $form->field($model, 'rolle')->hiddenInput(['value' => $rolle, 'style' => 'display:none;'])->label(false) ?>
        <?= $form->field($model, 'benutzer_id')->hiddenInput(['value' => Yii::$app->user->getId(), 'style' => 'display:none;'])->label(false) ?>

        <div class="field-group">
            <div class="checkbox-list">
                <?= $form->field($model, 'internes_netzwerk')->checkbox() ?>
                <?= $form->field($model, 'ki')->checkbox() ?>
                <?= $form->field($model, 'ndr_knowhow')->checkbox() ?>
                <?= $form->field($model, 'externe_erfahrung')->checkbox() ?>
                <?= $form->field($model, 'digitalisierung')->checkbox() ?>
                <?= $form->field($model, 'externes_netzwerk')->checkbox() ?>
                <?= $form->field($model, 'socialmedia')->checkbox() ?>
                <?= $form->field($model, 'moderation')->checkbox() ?>
                <?= $form->field($model, 'worklifebalance')->checkbox() ?>
                <?= $form->field($model, 'präsentationen')->checkbox() ?>
                <?= $form->field($model, 'resilienz')->checkbox() ?>
                <?= $form->field($model, 'gender_diversity')->checkbox() ?>
            </div>
        </div>

        <div class="form-navigation">
            <?= Html::a('zurück', ['site/register'], ['class' => 'btn btn-secondary']) ?>
            <?= Html::submitButton('weiter', ['class' => 'btn btn_nav_blue']) ?>
        </div>
        <?php ActiveForm::end(); ?>
    </div>
<?php } ?>