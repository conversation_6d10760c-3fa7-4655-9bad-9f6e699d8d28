<?php

use app\models\Benutzer;
use yii\helpers\Html;
use yii\widgets\ActiveForm;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $model app\models\Tandempartner */
/* @var $form \yii\widgets\ActiveForm*/
/* @var $rolle */
/* @var $todo */

//echo "<pre>";var_dump($model->getOptsGeneration()); echo Yii::$app->user->getIdentity()->getId();       exit;
//if ($todo) {}
?>

<div class="d-flex">
    <div class="col-sm-4">
        <h3>Wie ?</h3>
    </div>
    <div class="col-sm-6">
<!--            --><?php //= $form->field($model, 'treffen_art')->dropDownList($model::optsTreffenArt(), ['prompt' => 'auswählen', 'class' => 'mentoring-control '])->label(false) ?>
        <?= $form->field($model, 'treffen_art')->dropDownList($model::optsTreffenArt(), ['prompt' => 'auswählen', 'class' => 'mentoring-control '])->label(false) ?>
    </div>
</div>
<div class="d-flex">
    <div class="col-sm-4">
        <h3>Wie oft ?</h3>
    </div>
    <div class="col-sm-6">
        <?= $form->field($model, 'treffen_frequenz')->dropDownList($model::optsTreffenFrequenz(), ['prompt' => 'auswählen', 'class' => 'mentoring-control '])->label(false) ?>
    </div>
</div>
