<?php

use yii\helpers\Html;
use app\common\override\yii2\GridView;
use yii\widgets\Pjax;
use app\common\override\yii2\ActiveForm;

/* @var $this yii\web\View */
/* @var $model app\models\Tandempartner */
/* @var $rolle */

//$this->title = 'Mein*e Tandempartner*in sollte ...';
$this->params['breadcrumbs'][] = $this->title;

$readonly = !empty($readonly);
?>
<div class="form-container tandempartner-form">
    <h1 class="ff_brand_c c_darkblue fs-xxl">
        <a href="/site/index"><?= \rmrevin\yii\fontawesome\FontAwesome::icon("home", ['class' => '']) ?></a>
        Tandempartner-Präferenzen
    </h1>

    <?php $form = ActiveForm::begin(['id' => 'TandempartnerForm', 'method' => 'post', 'action' => '/tandempartner/update']); ?>

    <div class="field-group">
        <h3>Mein*e Tandempartner*in sollte ...</h3>
        <?php
        echo $this->render('_form', [
            'model' => $model,
            'form' => $form,
            'rolle' => $rolle,
            'readonly' => $readonly,
        ]); ?>
    </div>

    <div class="field-group">
        <h3>Ich möchte meine*n Tandempartner wie folgt treffen:</h3>
        <?php
        echo $this->render('_treffen', [
            'model' => $model,
            'form' => $form,
            'rolle' => $rolle,
            'readonly' => $readonly,
        ]);
        ?>
    </div>

    <div class="form-navigation">
        <?= Html::a('zurück', ['/profil/index?rolle=' . $rolle], ['class' => 'btn btn-secondary']) ?>
        <?= Html::submitButton('Registrierung abschließen', ['class' => 'btn btn_nav_blue']) ?>
    </div>

    <?php ActiveForm::end(); ?>
</div>


<!--    <br class="clearfix"/><br class="clearfix"/>-->
<!--</div>-->