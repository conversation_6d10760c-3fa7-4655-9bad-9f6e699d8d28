<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Benutzer;

/**
 * BenutzerSearch represents the model behind the search form of `app\models\Benutzer`.
 */
class BenutzerSearch extends Benutzer
{
	/**
	 * {@inheritdoc}
	 */
	public function rules()
	{
		return [
			[['id', 'domaene_id'], 'integer'],
			[['benutzername', 'nachname', 'vorname', 'email', 'passwort', 'email_alternativ', 'employeeid'], 'safe'],
		];
	}

	/**
	 * {@inheritdoc}
	 */
	public function scenarios()
	{
		// bypass scenarios() implementation in the parent class
		return Model::scenarios();
	}

	/**
	 * Creates data provider instance with search query applied
	 *
	 * @param array $params
	 *
	 * @return ActiveDataProvider
	 */
	public function search($params)
	{
		$query = Benutzer::find();

		// add conditions that should always apply here

		$dataProvider = new ActiveDataProvider([
			'query' => $query,
		]);

		$this->load($params);

		if (!$this->validate()) {
			// uncomment the following line if you do not want to return any records when validation fails
			// $query->where('0=1');
			return $dataProvider;
		}

		// grid filtering conditions
		$query->andFilterWhere([
			'id' => $this->id,
			'domaene_id' => $this->domaene_id,
		]);

		$query->andFilterWhere(['like', 'benutzername', $this->benutzername])
			->andFilterWhere(['like', 'nachname', $this->nachname])
			->andFilterWhere(['like', 'vorname', $this->vorname])
			->andFilterWhere(['like', 'email', $this->email])
			->andFilterWhere(['like', 'passwort', $this->passwort])
			->andFilterWhere(['like', 'email_alternativ', $this->email_alternativ])
			->andFilterWhere(['like', 'employeeid', $this->employeeid]);

		return $dataProvider;
	}
}
