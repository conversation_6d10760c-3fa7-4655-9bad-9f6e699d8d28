<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[Tandempartner]].
 *
 * @see Tandempartner
 */
class TandempartnerQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Tandempartner[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Tandempartner|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
