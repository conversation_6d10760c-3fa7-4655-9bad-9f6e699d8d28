<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[<PERSON>tor]].
 *
 * @see Mentor
 */
class MentorQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Mentor[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Mentor|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
