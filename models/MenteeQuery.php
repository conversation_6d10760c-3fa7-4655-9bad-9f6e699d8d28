<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[Men<PERSON>]].
 *
 * @see Mentee
 */
class MenteeQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Mentee[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Mentee|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
