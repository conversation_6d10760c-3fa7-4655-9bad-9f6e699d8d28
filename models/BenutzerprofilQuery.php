<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[Benutzerprofil]].
 *
 * @see Benutzerprofil
 */
class BenutzerprofilQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Benutzerprofil[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Benutzerprofil|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
