<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "tandempartner".
 *
 * @property int $id
 * @property int $benutzer_id
 * @property string $rolle
 * @property string $generation
 * @property string $geschlecht
 * @property string $standort
 * @property string $tätigkeitsfeld
 * @property string $führungskraft
 * @property string|null $treffen_art
 * @property string|null $treffen_frequenz
 */
class Tandempartner extends \yii\db\ActiveRecord
{

    /**
     * ENUM field values
     */
    const GENERATION_BABYBOOMER_BIS_JAHRGANG_1964 = 'Babyboomer bis Jahrgang 1964';
    const GENERATION_GENERATION_X_BIS_JAHRGANG_1980 = 'Generation X bis Jahrgang 1980';
    const GENERATION_GENERATION_Y_BIS_JAHRGANG_1995 = 'Generation Y bis Jahrgang 1995';
    const GENERATION_GENERATION_X_BIS_JAHRGANG_2010 = 'Generation Z bis Jahrgang 2010';
    const GENERATION_EGAL = 'Egal';
    const GESCHLECHT_MANNLICH = 'Männ<PERSON>';
    const GESCHLECHT_WEIBLICH = 'Weiblich';
    const GESCHLECHT_DIVERS = 'Divers';
    const GESCHLECHT_KEINE_ANGABE = 'keine Angabe';
    const GESCHLECHT_EGAL = 'Egal';
    const STANDORT_HH = 'HH';
    const STANDORT_MV = 'MV';
    const STANDORT_NDS = 'NDS';
    const STANDORT_SH = 'SH';
    const STANDORT_AUSLANDSSTUDIO = 'Auslandsstudio';
    const STANDORT_EGAL = 'Egal';
    const TATIGKEITSFELD_PROGRAMMSPEZIFISCH = 'Programmspezifisch';
    const TATIGKEITSFELD_VERWALTUNGSSPEZIFISCH = 'Verwaltungsspezifisch';
    const TATIGKEITSFELD_PRODUKTIONSSPEZIFISCH = 'Produktionsspezifisch';
    const TATIGKEITSFELD_EGAL = 'Egal';
    const FUHRUNGSKRAFT_JA = 'Ja';
    const FUHRUNGSKRAFT_NEIN = 'Nein';
    const FUHRUNGSKRAFT_EGAL = 'Egal';
    const TREFFEN_ART_ONLINE = 'Online';
    const TREFFEN_ART_IN_PRASENZ = 'in Präsenz';
    const TREFFEN_ART_EGAL = 'Egal';
    const TREFFEN_FREQUENZ_REGELMASSIG_1_PRO_MONAT = 'Regelmäßig 1* pro Monat';
    const TREFFEN_FREQUENZ_REGELMASSIG_ALLE_2_WOCHEN = 'Regelmäßig alle 2 Wochen';
//    const TREFFEN_FREQUENZ_REGELMASSIG_1_PRO_WOCHE = 'Regelmäßig 1* pro Woche';
    const TREFFEN_FREQUENZ_EGAL = 'Egal';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'tandempartner';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['treffen_frequenz', 'treffen_art'], 'default', 'value' => 'Egal'],
            [['benutzer_id', 'rolle', 'generation', 'geschlecht', 'standort', 'tätigkeitsfeld', 'führungskraft'], 'required'],
            [['benutzer_id'], 'integer'],
            [['generation', 'geschlecht', 'standort', 'tätigkeitsfeld', 'führungskraft', 'treffen_art', 'treffen_frequenz'], 'string'],
            [['generation', 'geschlecht', 'standort', 'tätigkeitsfeld', 'führungskraft', 'treffen_art', 'treffen_frequenz'], 'safe'],
            [['rolle'], 'string', 'max' => 12],
            ['generation', 'in', 'range' => array_keys(self::optsGeneration())],
            ['geschlecht', 'in', 'range' => array_keys(self::optsGeschlecht())],
            ['standort', 'in', 'range' => array_keys(self::optsStandort())],
            ['tätigkeitsfeld', 'in', 'range' => array_keys(self::optsTätigkeitsfeld())],
            ['führungskraft', 'in', 'range' => array_keys(self::optsFührungskraft())],
//            ['treffen_art', 'in', 'range' => array_keys(self::optsTreffenArt())],
            ['treffen_frequenz', 'in', 'range' => array_keys(self::optsTreffenFrequenz())],
            [['benutzer_id', 'rolle'], 'unique', 'targetAttribute' => ['benutzer_id', 'rolle']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'benutzer_id' => 'Benutzer ID',
            'rolle' => 'Rolle',
            'generation' => 'Generation',
            'geschlecht' => 'Geschlecht',
            'standort' => 'Standort',
            'tätigkeitsfeld' => 'Tätigkeitsfeld',
            'führungskraft' => 'Führungskraft',
            'treffen_art' => 'Treffen Art',
            'treffen_frequenz' => 'Treffen Frequenz',
        ];
    }

    /**
     * {@inheritdoc}
     * @return TandempartnerQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new TandempartnerQuery(get_called_class());
    }

//    /* New getter for Tandempartner */
//    public function getAllTandempartner($searchrolle, $profil, $treffen_art, $treffen_frequenz)
//    {
////var_dump($profil); exit;
//        if ($searchrolle == 'mentormentee') {
//
//        }
//        else {
//           return Tandempartner::find()->orderBy(['standort' => SORT_ASC])
//                                       ->where(['=', 'rolle', $searchrolle]) // 25.02.2025 NUR freigegebene Services
//                                       ->andwhere(['=', 'geschlecht', $profil->geschlecht])
//                                       ->andwhere(['=', 'standort', $profil->standort])
//                                       ->andwhere(['=', 'tätigkeitsfeld', $profil->tätigkeitsfeld])
//                                       ->andwhere(['=', 'führungskraft', $profil->führungskraft])
//                                       ->andwhere(['=', 'treffen_art', $profil->treffen_art])
//                                       ->andwhere(['=', 'treffen_frequenz', $profil->treffen_frequenz])
//                                       ->all();
//        }
//    }


    /**
     * column generation ENUM value labels
     * @return string[]
     */
    public static function optsGeneration()
    {
        return [
            self::GENERATION_BABYBOOMER_BIS_JAHRGANG_1964 => 'Babyboomer bis Jahrgang 1964',
            self::GENERATION_GENERATION_X_BIS_JAHRGANG_1980 => 'Generation X bis Jahrgang 1980',
            self::GENERATION_GENERATION_Y_BIS_JAHRGANG_1995 => 'Generation Y bis Jahrgang 1995',
            self::GENERATION_GENERATION_X_BIS_JAHRGANG_2010 => 'Generation Z bis Jahrgang 2010',
            self::GENERATION_EGAL => 'Egal',
        ];
    }

    /**
     * column geschlecht ENUM value labels
     * @return string[]
     */
    public static function optsGeschlecht()
    {
        return [
            self::GESCHLECHT_MANNLICH => 'Männlich',
            self::GESCHLECHT_WEIBLICH => 'Weiblich',
            self::GESCHLECHT_DIVERS => 'Divers',
            self::GESCHLECHT_KEINE_ANGABE => 'keine Angabe',
            self::GESCHLECHT_EGAL => 'Egal',
        ];
    }

    /**
     * column standort ENUM value labels
     * @return string[]
     */
    public static function optsStandort()
    {
        return [
            self::STANDORT_HH => 'HH',
            self::STANDORT_MV => 'MV',
            self::STANDORT_NDS => 'NDS',
            self::STANDORT_SH => 'SH',
            self::STANDORT_AUSLANDSSTUDIO => 'Auslandsstudio',
            self::STANDORT_EGAL => 'Egal',
        ];
    }

    /**
     * column tätigkeitsfeld ENUM value labels
     * @return string[]
     */
    public static function optsTätigkeitsfeld()
    {
        return [
            self::TATIGKEITSFELD_PROGRAMMSPEZIFISCH => 'Programmspezifisch',
            self::TATIGKEITSFELD_VERWALTUNGSSPEZIFISCH => 'Verwaltungsspezifisch',
            self::TATIGKEITSFELD_PRODUKTIONSSPEZIFISCH => 'Produktionsspezifisch',
            self::TATIGKEITSFELD_EGAL => 'Egal'
        ];
    }

    /**
     * column führungskraft ENUM value labels
     * @return string[]
     */
    public static function optsFührungskraft()
    {
        return [
            self::FUHRUNGSKRAFT_JA => 'Ja',
            self::FUHRUNGSKRAFT_NEIN => 'Nein',
            self::FUHRUNGSKRAFT_EGAL => 'Egal',
        ];
    }

    /**
     * column treffen_art ENUM value labels
     * @return string[]
     */
    public static function optsTreffenArt()
    {
        return [
            self::TREFFEN_ART_ONLINE => 'Online',
            self::TREFFEN_ART_IN_PRASENZ => 'in Präsenz',
            self::TREFFEN_ART_EGAL => 'Egal',
        ];
    }

    /**
     * column treffen_frequenz ENUM value labels
     * @return string[]
     */
    public static function optsTreffenFrequenz()
    {
        return [
            self::TREFFEN_FREQUENZ_REGELMASSIG_1_PRO_MONAT => 'Regelmäßig 1* pro Monat',
            self::TREFFEN_FREQUENZ_REGELMASSIG_ALLE_2_WOCHEN => 'Regelmäßig alle 2 Wochen',
//            self::TREFFEN_FREQUENZ_REGELMASSIG_1_PRO_WOCHE => 'Regelmäßig 1* pro Woche',
            self::TREFFEN_FREQUENZ_EGAL => 'Egal',
        ];
    }

    /**
     * @return string
     */
    public function displayGeneration()
    {
        return self::optsGeneration()[$this->generation];
    }

    /**
     * @return bool
     */
    public function isGenerationBabyboomerBisJahrgang1964()
    {
        return $this->generation === self::GENERATION_BABYBOOMER_BIS_JAHRGANG_1964;
    }

    public function setGenerationToBabyboomerBisJahrgang1964()
    {
        $this->generation = self::GENERATION_BABYBOOMER_BIS_JAHRGANG_1964;
    }

    /**
     * @return bool
     */
    public function isGenerationGenerationXBisJahrgang1980()
    {
        return $this->generation === self::GENERATION_GENERATION_X_BIS_JAHRGANG_1980;
    }

    public function setGenerationToGenerationXBisJahrgang1980()
    {
        $this->generation = self::GENERATION_GENERATION_X_BIS_JAHRGANG_1980;
    }

    /**
     * @return bool
     */
    public function isGenerationGenerationYBisJahrgang1995()
    {
        return $this->generation === self::GENERATION_GENERATION_Y_BIS_JAHRGANG_1995;
    }

    public function setGenerationToGenerationYBisJahrgang1995()
    {
        $this->generation = self::GENERATION_GENERATION_Y_BIS_JAHRGANG_1995;
    }

    /**
     * @return bool
     */
    public function isGenerationGenerationXBisJahrgang2010()
    {
        return $this->generation === self::GENERATION_GENERATION_X_BIS_JAHRGANG_2010;
    }

    public function setGenerationToGenerationXBisJahrgang2010()
    {
        $this->generation = self::GENERATION_GENERATION_X_BIS_JAHRGANG_2010;
    }

    /**
     * @return bool
     */
    public function isGenerationEgal()
    {
        return $this->generation === self::GENERATION_EGAL;
    }

    public function setGenerationToEgal()
    {
        $this->generation = self::GENERATION_EGAL;
    }

    /**
     * @return string
     */
    public function displayGeschlecht()
    {
        return self::optsGeschlecht()[$this->geschlecht];
    }

    /**
     * @return bool
     */
    public function isGeschlechtMannlich()
    {
        return $this->geschlecht === self::GESCHLECHT_MANNLICH;
    }

    public function setGeschlechtToMannlich()
    {
        $this->geschlecht = self::GESCHLECHT_MANNLICH;
    }

    /**
     * @return bool
     */
    public function isGeschlechtWeiblich()
    {
        return $this->geschlecht === self::GESCHLECHT_WEIBLICH;
    }

    public function setGeschlechtToWeiblich()
    {
        $this->geschlecht = self::GESCHLECHT_WEIBLICH;
    }

    /**
     * @return bool
     */
    public function isGeschlechtDivers()
    {
        return $this->geschlecht === self::GESCHLECHT_DIVERS;
    }

    public function setGeschlechtToDivers()
    {
        $this->geschlecht = self::GESCHLECHT_DIVERS;
    }

    /**
     * @return bool
     */
    public function isGeschlechtKeineAngabe()
    {
        return $this->geschlecht === self::GESCHLECHT_KEINE_ANGABE;
    }

    public function setGeschlechtToKeineAngabe()
    {
        $this->geschlecht = self::GESCHLECHT_KEINE_ANGABE;
    }

    /**
     * @return bool
     */
    public function isGeschlechtEgal()
    {
        return $this->geschlecht === self::GESCHLECHT_EGAL;
    }

    public function setGeschlechtToEgal()
    {
        $this->geschlecht = self::GESCHLECHT_EGAL;
    }

    /**
     * @return string
     */
    public function displayStandort()
    {
        return self::optsStandort()[$this->standort];
    }

    /**
     * @return bool
     */
    public function isStandortHh()
    {
        return $this->standort === self::STANDORT_HH;
    }

    public function setStandortToHh()
    {
        $this->standort = self::STANDORT_HH;
    }

    /**
     * @return bool
     */
    public function isStandortMv()
    {
        return $this->standort === self::STANDORT_MV;
    }

    public function setStandortToMv()
    {
        $this->standort = self::STANDORT_MV;
    }

    /**
     * @return bool
     */
    public function isStandortNds()
    {
        return $this->standort === self::STANDORT_NDS;
    }

    public function setStandortToNds()
    {
        $this->standort = self::STANDORT_NDS;
    }

    /**
     * @return bool
     */
    public function isStandortSh()
    {
        return $this->standort === self::STANDORT_SH;
    }

    public function setStandortToSh()
    {
        $this->standort = self::STANDORT_SH;
    }

    /**
     * @return bool
     */
    public function isStandortAuslandsstudio()
    {
        return $this->standort === self::STANDORT_AUSLANDSSTUDIO;
    }

    public function setStandortToAuslandsstudio()
    {
        $this->standort = self::STANDORT_AUSLANDSSTUDIO;
    }

    /**
     * @return bool
     */
    public function isStandortEgal()
    {
        return $this->standort === self::STANDORT_EGAL;
    }

    public function setStandortToEgal()
    {
        $this->standort = self::STANDORT_EGAL;
    }

    /**
     * @return string
     */
    public function displayTätigkeitsfeld()
    {
        return self::optsTätigkeitsfeld()[$this->tätigkeitsfeld];
    }

    /**
     * @return bool
     */
    public function isTätigkeitsfeldProgrammspezifisch()
    {
        return $this->tätigkeitsfeld === self::TATIGKEITSFELD_PROGRAMMSPEZIFISCH;
    }

    public function setTätigkeitsfeldToProgrammspezifisch()
    {
        $this->tätigkeitsfeld = self::TATIGKEITSFELD_PROGRAMMSPEZIFISCH;
    }

    /**
     * @return bool
     */
    public function isTätigkeitsfeldVerwaltungsspezifisch()
    {
        return $this->tätigkeitsfeld === self::TATIGKEITSFELD_VERWALTUNGSSPEZIFISCH;
    }

    public function setTätigkeitsfeldToVerwaltungsspezifisch()
    {
        $this->tätigkeitsfeld = self::TATIGKEITSFELD_VERWALTUNGSSPEZIFISCH;
    }

    /**
     * @return bool
     */
    public function isTätigkeitsfeldProduktionsspezifisch()
    {
        return $this->tätigkeitsfeld === self::TATIGKEITSFELD_PRODUKTIONSSPEZIFISCH;
    }

    public function setTätigkeitsfeldToProduktionsspezifisch()
    {
        $this->tätigkeitsfeld = self::TATIGKEITSFELD_PRODUKTIONSSPEZIFISCH;
    }

    /**
     * @return string
     */
    public function displayFührungskraft()
    {
        return self::optsFührungskraft()[$this->führungskraft];
    }

    /**
     * @return bool
     */
    public function isFührungskraftJa()
    {
        return $this->führungskraft === self::FUHRUNGSKRAFT_JA;
    }

    public function setFührungskraftToJa()
    {
        $this->führungskraft = self::FUHRUNGSKRAFT_JA;
    }

    /**
     * @return bool
     */
    public function isFührungskraftNein()
    {
        return $this->führungskraft === self::FUHRUNGSKRAFT_NEIN;
    }

    public function setFührungskraftToNein()
    {
        $this->führungskraft = self::FUHRUNGSKRAFT_NEIN;
    }

    /**
     * @return bool
     */
    public function isFührungskraftEgal()
    {
        return $this->führungskraft === self::FUHRUNGSKRAFT_EGAL;
    }

    public function setFührungskraftToEgal()
    {
        $this->führungskraft = self::FUHRUNGSKRAFT_EGAL;
    }

    /**
     * @return string
     */
    public function displayTreffenArt()
    {
        return self::optsTreffenArt()[$this->treffen_art];
    }

    /**
     * @return bool
     */
    public function isTreffenArtOnline()
    {
        return $this->treffen_art === self::TREFFEN_ART_ONLINE;
    }

    public function setTreffenArtToOnline()
    {
        $this->treffen_art = self::TREFFEN_ART_ONLINE;
    }

    /**
     * @return bool
     */
    public function isTreffenArtInPrasenz()
    {
        return $this->treffen_art === self::TREFFEN_ART_IN_PRASENZ;
    }

    public function setTreffenArtToInPrasenz()
    {
        $this->treffen_art = self::TREFFEN_ART_IN_PRASENZ;
    }

    /**
     * @return bool
     */
    public function isTreffenArtEgal()
    {
        return $this->treffen_art === self::TREFFEN_ART_EGAL;
    }

    public function setTreffenArtToEgal()
    {
        $this->treffen_art = self::TREFFEN_ART_EGAL;
    }

    /**
     * @return string
     */
    public function displayTreffenFrequenz()
    {
        return self::optsTreffenFrequenz()[$this->treffen_frequenz];
    }

    /**
     * @return bool
     */
    public function isTreffenFrequenzRegelmassig1ProMonat()
    {
        return $this->treffen_frequenz === self::TREFFEN_FREQUENZ_REGELMASSIG_1_PRO_MONAT;
    }

    public function setTreffenFrequenzToRegelmassig1ProMonat()
    {
        $this->treffen_frequenz = self::TREFFEN_FREQUENZ_REGELMASSIG_1_PRO_MONAT;
    }

    /**
     * @return bool
     */
    public function isTreffenFrequenzRegelmassigAlle2Wochen()
    {
        return $this->treffen_frequenz === self::TREFFEN_FREQUENZ_REGELMASSIG_ALLE_2_WOCHEN;
    }

    public function setTreffenFrequenzToRegelmassigAlle2Wochen()
    {
        $this->treffen_frequenz = self::TREFFEN_FREQUENZ_REGELMASSIG_ALLE_2_WOCHEN;
    }

    /**
     * @return bool
     */
//    public function isTreffenFrequenzRegelmassig1ProWoche()
//    {
//        return $this->treffen_frequenz === self::TREFFEN_FREQUENZ_REGELMASSIG_1_PRO_WOCHE;
//    }

//    public function setTreffenFrequenzToRegelmassig1ProWoche()
//    {
//        $this->treffen_frequenz = self::TREFFEN_FREQUENZ_REGELMASSIG_1_PRO_WOCHE;
//    }

    /**
     * @return bool
     */
    public function isTreffenFrequenzEgal()
    {
        return $this->treffen_frequenz === self::TREFFEN_FREQUENZ_EGAL;
    }

    public function setTreffenFrequenzToEgal()
    {
        $this->treffen_frequenz = self::TREFFEN_FREQUENZ_EGAL;
    }
}
