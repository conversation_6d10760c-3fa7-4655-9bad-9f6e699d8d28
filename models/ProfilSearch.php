<?php

namespace app\models;

use yii\base\Model;
use yii\data\SqlDataProvider;
use app\models\Profil;
use Yii;
use yii\debug\models\timeline\DataProvider;
use yii\debug\panels\TimelinePanel;

/**
 * ProfilSearch represents the model behind the search form of `app\models\Profil`.
 */
class ProfilSearch extends Profil
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'benutzer_id', 'geduld', 'fairness', 'empathie', 'loyalität', 'selbstdisziplin', 'humor', 'verlässlichkeit', 'kritikfähigkeit', 'hilfsbereitschaft', 'hands_on_mentalität', 'neugierde', 'sport', 'filme', 'musik', 'reisen', 'outdoor', 'kunst_und_kultur', 'yoga', 'ehrenamt'], 'integer'],
            [['rolle', 'generation', 'geschlecht', 'standort', 'tätigkeitsfeld', 'führungskraft', 'weitere_hobbys'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     * @param string|null $formName Form name to be used into `->load()` method.
     *
     * @return SqlDataProvider
     */
    public function search($tandempartner, $kpt_logged_in_user, $searchrolle)
    {
        $query = Profil::find();
        if ($searchrolle == 'menteementor') {
            $add_sql =  ' LEFT JOIN kompetenzen AS kpt ON profil.benutzer_id = kpt.benutzer_id
                          AND profil.benutzer_id <> '.Yii::$app->user->id;
        }
        else {
            $add_sql =  ' LEFT JOIN kompetenzen AS kpt ON profil.benutzer_id = kpt.benutzer_id'.
                        ' WHERE profil.rolle = "'.$searchrolle.'"
                          AND kpt.rolle = "'.$searchrolle.'"
                          AND profil.rolle <> ""';
        }

        $tandempartner->generation != "Egal" ?          $add_sql .= ' AND profil.generation = "'.$tandempartner->generation.'"' : "";
        $tandempartner->geschlecht != "Egal" ?          $add_sql .= ' AND profil.geschlecht = "'.$tandempartner->geschlecht.'"' : "";
        $tandempartner->standort != "Egal" ?            $add_sql .= ' AND profil.standort = "'.$tandempartner->standort.'"' : "";
        $tandempartner->tätigkeitsfeld != "Egal" ?      $add_sql .= ' AND profil.tätigkeitsfeld = "'.$tandempartner->tätigkeitsfeld.'"' : "";
        $tandempartner->führungskraft != "Egal" ?       $add_sql .= ' AND profil.führungskraft = "'.$tandempartner->führungskraft.'"' : "";
        // Check if any kompetenzen values are non-zero
        $has_kompetenzen = false;
        foreach ($kpt_logged_in_user as $key => $value) {
            if ($value != 0) {
                $has_kompetenzen = true;
                break;
            }
        }

        if ($has_kompetenzen) {

            $add_sql .= ' AND ( ';
            $first = true;
            if ($searchrolle == 'menteementor') { // Für MenteeMentor Rollen wird anders gesucht

                    if ($kpt_logged_in_user['internes_netzwerk'] != 0) {
                        $add_sql .= '    kpt.wunsch_internes_netzwerk = ' . $kpt_logged_in_user['internes_netzwerk'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['ki'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.ki = ' . $kpt_logged_in_user['ki'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['ndr_knowhow'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_ndr_knowhow = ' . $kpt_logged_in_user['ndr_knowhow'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['externe_erfahrung'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_externe_erfahrung = ' . $kpt_logged_in_user['externe_erfahrung'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['digitalisierung'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_digitalisierung = ' . $kpt_logged_in_user['digitalisierung'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['externes_netzwerk'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_externes_netzwerk = ' . $kpt_logged_in_user['externes_netzwerk'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['socialmedia'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_socialmedia = ' . $kpt_logged_in_user['socialmedia'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['moderation'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_moderation = ' . $kpt_logged_in_user['moderation'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['worklifebalance'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_worklifebalance = ' . $kpt_logged_in_user['worklifebalance'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['präsentationen'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_präsentationen = ' . $kpt_logged_in_user['präsentationen'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['resilienz'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.wunsch_resilienz = ' . $kpt_logged_in_user['resilienz'];
                        $first = false;
                    }

                    if ($kpt_logged_in_user['gender_diversity'] != 0) {
                        $add_sql .= ($first ? '' : ' OR ') . 'kpt.gender_diversity = ' . $kpt_logged_in_user['gender_diversity'];
                        $first = false;
                    }

                }

            else {

                if ($kpt_logged_in_user['internes_netzwerk'] != 0) {
                    $add_sql .= '    kpt.internes_netzwerk = ' . $kpt_logged_in_user['internes_netzwerk'];
                    $first = false;
                }

                if ($kpt_logged_in_user['ki'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.ki = ' . $kpt_logged_in_user['ki'];
                    $first = false;
                }

                if ($kpt_logged_in_user['ndr_knowhow'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.ndr_knowhow = ' . $kpt_logged_in_user['ndr_knowhow'];
                    $first = false;
                }

                if ($kpt_logged_in_user['externe_erfahrung'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.externe_erfahrung = ' . $kpt_logged_in_user['externe_erfahrung'];
                    $first = false;
                }

                if ($kpt_logged_in_user['digitalisierung'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.digitalisierung = ' . $kpt_logged_in_user['digitalisierung'];
                    $first = false;
                }

                if ($kpt_logged_in_user['externes_netzwerk'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.externes_netzwerk = ' . $kpt_logged_in_user['externes_netzwerk'];
                    $first = false;
                }

                if ($kpt_logged_in_user['socialmedia'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.socialmedia = ' . $kpt_logged_in_user['socialmedia'];
                    $first = false;
                }

                if ($kpt_logged_in_user['moderation'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.moderation = ' . $kpt_logged_in_user['moderation'];
                    $first = false;
                }

                if ($kpt_logged_in_user['worklifebalance'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.worklifebalance = ' . $kpt_logged_in_user['worklifebalance'];
                    $first = false;
                }

                if ($kpt_logged_in_user['präsentationen'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.präsentationen = ' . $kpt_logged_in_user['präsentationen'];
                    $first = false;
                }

                if ($kpt_logged_in_user['resilienz'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.resilienz = ' . $kpt_logged_in_user['resilienz'];
                    $first = false;
                }

                if ($kpt_logged_in_user['gender_diversity'] != 0) {
                    $add_sql .= ($first ? '' : ' OR ') . 'kpt.gender_diversity = ' . $kpt_logged_in_user['gender_diversity'];
                    $first = false;
                }
            }
            $add_sql .= ' ) ';
        } else {
            // If no kompetenzen values are set, don't add any kompetenzen conditions
            // This allows matching based on other criteria even if no kompetenzen are specified
        }
        $query = 'SELECT COUNT(*) FROM profil '. $add_sql;
        $real_query = 'SELECT profil.*, kpt.* FROM profil ' . $add_sql;
//echo $real_query; exit;

        $count = Yii::$app->db->createCommand(
                $query,
                 [])->queryScalar();
        if ($count > 0) {
            $dataProvider = new SqlDataProvider([
                'sql' => $real_query,
                'totalCount' => $count,
                'pagination' => [
                    'pageSize' => 100,
                ],
//            'sort' => [
//                'benutzer_id'
//            ],
            ]);

            return $dataProvider;
        }
        else {
            return false;
        }

    }
}
