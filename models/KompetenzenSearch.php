<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Kompetenzen;

/**
 * KompetenzenSearch represents the model behind the search form of `app\models\Kompetenzen`.
 */
class KompetenzenSearch extends Kompetenzen
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'internes_netzwerk', 'ki', 'ndr_knowhow', 'externe_erfahrung', 'digitalisierung', 'externes_netzwerk', 'socialmedia', 'moderation', 'worklifebalance', 'päsentationen', 'resilienz', 'gender_diversity'], 'integer'],
            [['rolle'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = Kompetenzen::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            'internes_netzwerk' => $this->internes_netzwerk,
            'ki' => $this->ki,
            'ndr_knowhow' => $this->ndr_knowhow,
            'externe_erfahrung' => $this->externe_erfahrung,
            'digitalisierung' => $this->digitalisierung,
            'externes_netzwerk' => $this->externes_netzwerk,
            'socialmedia' => $this->socialmedia,
            'moderation' => $this->moderation,
            'worklifebalance' => $this->worklifebalance,
            'päsentationen' => $this->päsentationen,
            'resilienz' => $this->resilienz,
        ]);

        $query->andFilterWhere([ 'rolle' => $this->rolle]);

        return $dataProvider;
    }
}
