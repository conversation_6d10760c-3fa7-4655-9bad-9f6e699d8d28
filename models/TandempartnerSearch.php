<?php

namespace app\models;

use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\Tandempartner;

/**
 * TandempartnerSearch represents the model behind the search form of `app\models\Tandempartner`.
 */
class TandempartnerSearch extends Tandempartner
{
    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['id', 'benutzer_id'], 'integer'],
            [['rolle', 'generation', 'geschlecht', 'standort', 'tätigkeitsfeld', 'führungskraft', 'treffen_art', 'treffen_frequenz'], 'safe'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     * @param string|null $formName Form name to be used into `->load()` method.
     *
     * @return ActiveDataProvider
     */
    public function search($profil, $searchrolle, $treffen_art, $treffen_frequenz)
    {
//echo "treffen_art:".$treffen_art; echo "treffen_frequenz:".$treffen_frequenz; exit;
        $query = Tandempartner::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
//            'id' => $this->id,
            'benutzer_id' => $this->benutzer_id,
        ]);

        $query->andFilterWhere(['=', 'rolle', $searchrolle])
                ->andFilterWhere(['=', 'generation', $profil->generation])
                ->andFilterWhere(['=', 'geschlecht', $profil->geschlecht])
                ->andFilterWhere(['=', 'standort', $profil->standort])
                ->andFilterWhere(['=', 'tätigkeitsfeld', $profil->tätigkeitsfeld])
                ->andFilterWhere(['=', 'führungskraft', $profil->führungskraft])
                ->andFilterWhere(['=', 'treffen_art', $treffen_art])
                ->andFilterWhere(['=', 'treffen_frequenz', $treffen_frequenz]);

        return $dataProvider;
    }
}