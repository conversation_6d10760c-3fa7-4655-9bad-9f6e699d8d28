<?php

namespace app\models;

use Yii;
use yii\web\HttpException;

/**
 * This is the model class for table "Konfiguration".
 *
 * @property int $id
 * @property string $name
 * @property string $wert
 * @property string $beschreibung
 * @property int $deprecated
 */
class Konfiguration extends Basismodel
{
    /* Fehlertext für Exception - fehlende Konfiguration */
    const noConfig = 'Ein interner Fehler (fehlende Konfiguration) ist aufgetreten. Bitte melden Sie sich bei dem Administrator dieser Anwendung.';

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'konfiguration';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['name'], 'string', 'max' => 255],
            [['wert', 'beschreibung'], 'string', 'max' => 1024],
            [['deprecated'], 'string', 'max' => 4],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'name' => 'Name',
            'wert' => 'Wert',
            'beschreibung' => 'Beschreibung',
            'deprecated' => 'Veraltet',
        ];
    }

    /**
     * Bezieht die Konfigurationsdaten aus der DB. Wirft exception, wenn kein
     * Datensatz ermittelbar ist.
     *
     * @param string $name
     *
     * @return string
     * @throws HttpException
     */
    public static function getParameterByName($name, $throw = true)
    {
        $parameter = Konfiguration::find()->where(['name' => $name])->andWhere(['deprecated' => 0])->one();

        if (isset($parameter) && !empty($parameter)) {
            return $parameter->wert;
        } else if ($throw && Yii::$app instanceof \yii\web\Application) {
            Yii::error("Notwendiger Parameter (" . $name . ") konnte nicht aufgeloest werden!");
            throw new HttpException(500, self::noConfig . ' (var ' . $name . ')');
        } else {
            return "(Parameter " . $name . " not set)";
        }
    }

    public static function isset($name)
    {
        $parameter = Konfiguration::find()->where(['name' => $name])->andWhere(['deprecated' => 0])->one();
        return !empty($parameter);
    }

    /**
     * @param $name
     *
     * @return string
     * @throws \yii\web\HttpException
     */

    public static function get($name)
    {
        return self::getParameterByName($name);
    }

    /**
     * @param $name
     * @param $wert
     * @param $beschreibung
     *
     * @return bool
     */

    public static function set($name, $wert, $beschreibung = "")
    {
        self::updateAll(["deprecated" => 1], ["name" => $name]);
        $konf = new self();
        $konf->name = $name;
        $konf->wert = $wert;
        $konf->beschreibung = $beschreibung;
        return $konf->save();
    }

}
