<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "mentor".
 *
 * @property int $id
 */
class <PERSON><PERSON> extends Basismodel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mentor';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
        ];
    }

    /**
     * {@inheritdoc}
     * @return MentorQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new MentorQuery(get_called_class());
    }
}
