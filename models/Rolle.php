<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "rolle".
 *
 * @property int $id
 * @property string|null $rolle // mentor, mentee, menteementor
 * @property string|null $beschreibung
 */
class Rolle extends Basismodel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'rolle';
    }

    /**
     * Gibt Liste zurück, die für die Verteilung der Rollen in der Benutzer-
     * komponente verwendet wird.
     *
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getRollen()
    {
        return Rolle::find()->select(['id', 'rolle'])->all();
    }

    public static function getRollenname()
    {
//        return Rolle::find()->select(['id' => , 'rolle'])->all();
        $rolle_id = RolleBenutzer::findOne(['benutzer_id' => Yii::$app->user->id]);
        $Rolle = Rolle::findOne(['id' => $rolle_id['rolle_id']]);
        $rolle = strtolower($Rolle->rolle);
        return $rolle;
    }

    public static function getRolleDisplayname($rolle)
    {
        $rollen_name = "";
        strtolower($rolle) == "mentor"  ? $rollen_name = "Mentor*in" : $rollen_name = "Mentee";
        return $rollen_name;
    }

    /**
     * Diese Methode gibt den Update/Create-Views des Benutzers alle bestehenden
     * Rollen zurück, sodass sie in als Checkbox-List ausgegeben werden können.
     *
     * @return array
     */
    public static function getRollenList()
    {
        $items = [];
        foreach (self::find()->orderBy('rolle')->all() as $rolle) {
            if (Yii::$app->user->isAdmin || Yii::$app->user->hasRoleId($rolle->id)) {
                $items[$rolle->id] = $rolle->rolle;
            }
        }
        return $items;
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['beschreibung'], 'string'],
            [['rolle'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'rolle' => 'Name der Rolle',
            'beschreibung' => 'Beschreibung der Rolle',
        ];
    }

    /**
     * Ermittelt alle Benutzer, die zu einer Rolle gehören.
     *
     * @return \yii\db\ActiveQuery
     */
    public function getUsers()
    {
        return $this->hasMany(Benutzer::class, ['id' => 'benutzer_id'])
                    ->viaTable('Roles_users', ['rolle_id' => 'id']);
    }
}
