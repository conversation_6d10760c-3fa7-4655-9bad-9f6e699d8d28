<?php

namespace app\models;

use yii\base\Exception;
use yii\helpers\ArrayHelper;

class Basismodel extends \yii\db\ActiveRecord
{

    public $saveResult = null;
    public $date_attributes = [];
    public $datetime_attributes = [];
    public $time_attributes = [];
    public $number_attributes = [];

    static function liste($options = [])
    {
        $pk = self::primaryKey();
        if (!empty($pk)) {
            $key = $pk[0];
        } else {
            throw new Exception("Kein primärer Schlüssel für die Tabelle " . self::class . " definiert");
        }
        $options = array_merge([
            'key' => $key,
            'field' => 'displayname',
        ], $options);

        $aq = self::find();
        if (!empty($options['where'])) {
            $aq->andWhere($options['where']);
        }

        $rw = ArrayHelper::map($aq->all(), $options['key'], $options['field']);
        asort($rw);
        return $rw;
    }

    /**
     * Liefert die ID aus der Tabelle zu dem Eintrag $entry
     *
     * @param $entry String
     *
     * @return int|mixed Integer
     */

    static function ID($entry)
    {
        $inst = self::find()->where(['=', strtolower(self::tableName()), $entry])->one();
        return empty($inst) ? 0 : $inst->id;
    }

    public static function tableName()
    {
        return self::getCalledModelName();
    }

    protected static function getCalledModelName()
    {
        return substr(strrchr(get_called_class(), '\\'), 1);
    }

    public function getClassname()
    {
        return (new \ReflectionClass($this))->getShortName();
    }

    /**
     * Diese Methode soll von allen Modellen ueberschrieben werden. Sie soll kontextabhaengig pruefen, ob
     * das Modell vom aktuellen Nutzer geschrieben werden kann.
     *
     * @return bool
     */
    public function allowUpdate()
    {
        return false;
    }

    /**
     * Diese Methode soll von allen Modellen ueberschrieben werden. Sie soll pruefen, ob der aktuell angemeldete
     * Nutzer der Besitzer des Objekts ist.
     *
     * @return bool
     */
    public function isUserOwner()
    {
        return false;
    }

    /**
     * Diese Methode soll von allen Modellen ueberschrieben werden. Sie soll pruefen, ob der aktuell angemeldete
     * Nutzer ein besonderes Leserecht auf das Objekt hat.
     *
     * @return bool
     */
    public function sonstigesLeserecht()
    {
        return false;
    }

    public function getDisplayname()
    {
        $field = $this->tableName();
        if ($this->hasAttribute($field)) {
            return $this->$field;
        } else {
            return $field . " " . $this->id;
        }
    }

    public function afterFind()
    {
        $this->specialfields();
        parent::afterFind();
    }

    public function specialfields()
    {
        foreach ($this->number_attributes as $attr) {
            if ($this->hasAttribute($attr) && $this->$attr != null) {
                $this->$attr = str_replace('.', ',', $this->$attr);
            }
        }
        foreach ($this->date_attributes as $attr) {
            if ($this->hasAttribute($attr) && $this->$attr != null) {
                $this->$attr = Yii::$app->formatter->asDate($this->$attr, 'php:d.m.Y');
            }
        }
        foreach ($this->datetime_attributes as $attr) {
            if ($this->hasAttribute($attr) && $this->$attr != null) {
                $this->$attr = Yii::$app->formatter->asDate($this->$attr, 'php:d.m.Y H:i');
            }
        }
        foreach ($this->time_attributes as $attr) {
            if ($this->hasAttribute($attr) && $this->$attr != null) {
                $this->$attr = Yii::$app->formatter->asDate($this->$attr, 'php:H:i');
            }
        }
    }

    public function afterSave($insert, $changedAttributes)
    {
        $this->specialfields();
        parent::afterSave($insert, $changedAttributes);
    }

    public function beforeDelete()
    {
        if (!parent::beforeDelete()) {
            return false;
        }

        if (isset($this->deleted_at)) {
            $this->deleted_at = date('Y-m-d H:i:s');
            $this->save();
        }

        return true;
    }

    public function beforeSave($insert)
    {
        if (!parent::beforeSave($insert)) {
            return false;
        }

        if (isset($this->created_at) && empty($this->created_at)) {
            $this->created_at = date('Y-m-d H:i:s');
        }

        if (isset($this->updated_at)) {
            $this->updated_at = date('Y-m-d H:i:s');
        }

        foreach ($this->number_attributes as $attr) {
            if ($this->hasAttribute($attr) && $this->$attr != null) {
                $this->$attr = str_replace(',', '.', $this->$attr);
            }
        }
        foreach ($this->date_attributes as $attr) {
            if ($this->hasAttribute($attr) && $this->$attr != null) {
                $this->$attr = Yii::$app->formatter->asDate($this->$attr, 'php:Y-m-d');
            }
        }
        foreach ($this->datetime_attributes as $attr) {
            if ($this->hasAttribute($attr) && $this->$attr != null) {
                $this->$attr = Yii::$app->formatter->asDate($this->$attr, 'php:Y-m-d H:i:s');
            }
        }
        foreach ($this->time_attributes as $attr) {
            if ($this->hasAttribute($attr) && $this->$attr != null) {
                $this->$attr = Yii::$app->formatter->asDate($this->$attr, 'php:H:i:s');
            }
        }

        return true;
    }

    public function getErrorstring()
    {
        $rw = "";
        foreach ($this->errors as $errorentry) {
            foreach ($errorentry as $error) {
                $rw .= $error . "\n";
            }
        }
        return $rw;
    }

    public function getNamesingular()
    {
        return ucfirst($this->tableName());
    }

    /* liefert für die Dropdown Felder eine Liste der Datensätze */

    public function getNameplural()
    {
        return ucfirst($this->tableName() . "en");
    }


}
