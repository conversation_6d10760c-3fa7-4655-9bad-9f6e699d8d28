<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "rolle_benutzer".
 *
 * @property int $id
 * @property int|null $rolle_id
 * @property int|null $benutzer_id
 */
class RolleBenutzer extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'rolle_benutzer';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['rolle_id', 'benutzer_id'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'rolle_id' => 'Rollen ID',
            'benutzer_id' => 'Benutzer ID',
        ];
    }
}
