<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "profil".
 *
 * @property int $id
 * @property int $benutzer_id
 * @property string $rolle
 * @property string $generation
 * @property string $geschlecht
 * @property string $standort
 * @property string $tätigkeitsfeld
 * @property string $führungskraft
 * @property int|null $geduld
 * @property int|null $fairness
 * @property int|null $empathie
 * @property int|null $loyalität
 * @property int|null $selbstdisziplin
 * @property int|null $humor
 * @property int|null $verlässlichkeit
 * @property int|null $kritikfähigkeit
 * @property int|null $hilfsbereitschaft
 * @property int|null $hands_on_mentalität
 * @property int|null $neugierde
 * @property int|null $sport
 * @property int|null $filme
 * @property int|null $musik
 * @property int|null $reisen
 * @property int|null $outdoor
 * @property int|null $kunst_und_kultur
 * @property int|null $yoga
 * @property int|null $ehrenamt
 * @property string|null $weitere_hobbys
 * @property int|null $anzeigbar
 */
class Profil extends \yii\db\ActiveRecord
{

    /**
     * ENUM field values
     */
    const GENERATION_BABYBOOMER_BIS_JAHRGANG_1964 = 'Babyboomer bis Jahrgang 1964';
    const GENERATION_GENERATION_X_BIS_JAHRGANG_1980 = 'Generation X bis Jahrgang 1980';
    const GENERATION_GENERATION_Y_BIS_JAHRGANG_1995 = 'Generation Y bis Jahrgang 1995';
    const GENERATION_GENERATION_Z_BIS_JAHRGANG_2010 = 'Generation Z bis Jahrgang 2010';
    const GESCHLECHT_MANNLICH = 'Männlich';
    const GESCHLECHT_WEIBLICH = 'Weiblich';
    const GESCHLECHT_DIVERS = 'Divers';
    const GESCHLECHT_KEINE_ANGABE = 'keine Angabe';
    const STANDORT_HH = 'HH';
    const STANDORT_MV = 'MV';
    const STANDORT_NDS = 'NDS';
    const STANDORT_SH = 'SH';
    const STANDORT_AUSLANDSSTUDIO = 'Auslandsstudio';
    const TATIGKEITSFELD_PROGRAMMSPEZIFISCH = 'Programmspezifisch';
    const TATIGKEITSFELD_VERWALTUNGSSPEZIFISCH = 'Verwaltungsspezifisch';
    const TATIGKEITSFELD_PRODUKTIONSSPEZIFISCH = 'Produktionsspezifisch';
    const FUHRUNGSKRAFT_JA = 'Ja';
    const FUHRUNGSKRAFT_NEIN = 'Nein';

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'profil';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['geduld', 'fairness', 'empathie', 'loyalität', 'selbstdisziplin', 'humor', 'verlässlichkeit', 'kritikfähigkeit', 'hilfsbereitschaft', 'hands_on_mentalität', 'neugierde', 'sport', 'filme', 'musik', 'reisen', 'outdoor', 'kunst_und_kultur', 'yoga', 'ehrenamt', 'weitere_hobbys'], 'default', 'value' => null],
            [['benutzer_id', 'rolle', 'generation', 'geschlecht', 'standort', 'tätigkeitsfeld', 'führungskraft'], 'required'],
            [['benutzer_id', 'geduld', 'fairness', 'empathie', 'loyalität', 'selbstdisziplin', 'humor', 'verlässlichkeit', 'kritikfähigkeit', 'hilfsbereitschaft', 'hands_on_mentalität', 'neugierde', 'sport', 'filme', 'musik', 'reisen', 'outdoor', 'kunst_und_kultur', 'yoga', 'ehrenamt', 'anzeigbar'], 'integer'],
            [['generation', 'geschlecht', 'standort', 'tätigkeitsfeld', 'führungskraft'], 'string'],
            [['rolle'], 'string', 'max' => 12],
            [['weitere_hobbys'], 'string', 'max' => 500],
            ['generation', 'in', 'range' => array_keys(self::optsGeneration())],
            ['geschlecht', 'in', 'range' => array_keys(self::optsGeschlecht())],
            ['standort', 'in', 'range' => array_keys(self::optsStandort())],
            ['tätigkeitsfeld', 'in', 'range' => array_keys(self::optsTätigkeitsfeld())],
            ['führungskraft', 'in', 'range' => array_keys(self::optsFührungskraft())],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'benutzer_id' => 'Benutzer ID',
            'rolle' => 'Rolle',
            'generation' => 'Generation',
            'geschlecht' => 'Geschlecht',
            'standort' => 'Standort',
            'tätigkeitsfeld' => 'Tätigkeitsfeld',
            'führungskraft' => 'Führungskraft',
            'geduld' => 'Geduld',
            'fairness' => 'Fairness',
            'empathie' => 'Empathie',
            'loyalität' => 'Loyalität',
            'selbstdisziplin' => 'Selbstdisziplin',
            'humor' => 'Humor',
            'verlässlichkeit' => 'Verlässlichkeit',
            'kritikfähigkeit' => 'Kritikfähigkeit',
            'hilfsbereitschaft' => 'Hilfsbereitschaft',
            'hands_on_mentalität' => 'Hands-On-Mentalität',
            'neugierde' => 'Neugierde',
            'sport' => 'Sport',
            'filme' => 'Filme',
            'musik' => 'Musik',
            'reisen' => 'Reisen',
            'outdoor' => 'Outdoor',
            'kunst_und_kultur' => 'Kunst und Kultur',
            'yoga' => 'Yoga',
            'ehrenamt' => 'Ehrenamt',
            'weitere_hobbys' => 'Weitere Hobbys',
            'anzeigbar' => 'In Matchingliste anzeigen'
        ];
    }

    /**
     * {@inheritdoc}
     * @return ProfilQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new ProfilQuery(get_called_class());
    }

    /* New getter for Profil */
    public function getAllProfils($rolle)
    {
        return Profil::find()->orderBy(['standort' => SORT_ASC])
            ->where(['=', 'rolle', $rolle]) // 25.02.2025 NUR freigegebene Services
            ->all();
    }

    public function getEmail($id)
    {
        $benutzer = Benutzer::find()
            ->join('LEFT JOIN', 'profil', 'benutzer.id = profil.benutzer_id')
            ->where('profil.benutzer_id = ' . $id)
            ->one();
        return $benutzer->email;
    }

    public function getProfil_name($id)
    {
        $benutzer = Benutzer::find()
            ->join('LEFT JOIN', 'profil', 'benutzer.id = profil.benutzer_id')
            ->where('profil.benutzer_id = ' . $id)
            ->one();
        return $benutzer->vorname." ".$benutzer->nachname;
    }

    public function getProfil_image($id)
    {
//        $benutzer = Benutzer::find()
//            ->join('LEFT JOIN', 'profil', 'benutzer.id = profil.benutzer_id')
//            ->where('profil.benutzer_id = ' . $id)
//            ->one();
        return "/css/images/matching_dummy_human.png";
    }

    /**
     * column generation ENUM value labels
     * @return string[]
     */
    public static function optsGeneration()
    {
        return [
            self::GENERATION_BABYBOOMER_BIS_JAHRGANG_1964 => 'Babyboomer bis Jahrgang 1964',
            self::GENERATION_GENERATION_X_BIS_JAHRGANG_1980 => 'Generation X bis Jahrgang 1980',
            self::GENERATION_GENERATION_Y_BIS_JAHRGANG_1995 => 'Generation Y bis Jahrgang 1995',
            self::GENERATION_GENERATION_Z_BIS_JAHRGANG_2010 => 'Generation Z bis Jahrgang 2010',
        ];
    }

    /**
     * column geschlecht ENUM value labels
     * @return string[]
     */
    public static function optsGeschlecht()
    {
        return [
            self::GESCHLECHT_MANNLICH => 'Männlich',
            self::GESCHLECHT_WEIBLICH => 'Weiblich',
            self::GESCHLECHT_DIVERS => 'Divers',
            self::GESCHLECHT_KEINE_ANGABE => 'keine Angabe',
        ];
    }

    /**
     * column standort ENUM value labels
     * @return string[]
     */
    public static function optsStandort()
    {
        return [
            self::STANDORT_HH => 'HH',
            self::STANDORT_MV => 'MV',
            self::STANDORT_NDS => 'NDS',
            self::STANDORT_SH => 'SH',
            self::STANDORT_AUSLANDSSTUDIO => 'Auslandsstudio',
        ];
    }

    /**
     * column tätigkeitsfeld ENUM value labels
     * @return string[]
     */
    public static function optsTätigkeitsfeld()
    {
        return [
            self::TATIGKEITSFELD_PROGRAMMSPEZIFISCH => 'Programmspezifisch',
            self::TATIGKEITSFELD_VERWALTUNGSSPEZIFISCH => 'Verwaltungsspezifisch',
            self::TATIGKEITSFELD_PRODUKTIONSSPEZIFISCH => 'Produktionsspezifisch',
        ];
    }

    /**
     * column führungskraft ENUM value labels
     * @return string[]
     */
    public static function optsFührungskraft()
    {
        return [
            self::FUHRUNGSKRAFT_JA => 'Ja',
            self::FUHRUNGSKRAFT_NEIN => 'Nein',
        ];
    }

    /**
     * @return string
     */
    public function displayGeneration()
    {
        return self::optsGeneration()[$this->generation];
    }

    /**
     * @return bool
     */
    public function isGenerationBabyboomerBisJahrgang1964()
    {
        return $this->generation === self::GENERATION_BABYBOOMER_BIS_JAHRGANG_1964;
    }

    public function setGenerationToBabyboomerBisJahrgang1964()
    {
        $this->generation = self::GENERATION_BABYBOOMER_BIS_JAHRGANG_1964;
    }

    /**
     * @return bool
     */
    public function isGenerationGenerationXBisJahrgang1980()
    {
        return $this->generation === self::GENERATION_GENERATION_X_BIS_JAHRGANG_1980;
    }

    public function setGenerationToGenerationXBisJahrgang1980()
    {
        $this->generation = self::GENERATION_GENERATION_X_BIS_JAHRGANG_1980;
    }

    /**
     * @return bool
     */
    public function isGenerationGenerationYBisJahrgang1995()
    {
        return $this->generation === self::GENERATION_GENERATION_Y_BIS_JAHRGANG_1995;
    }

    public function setGenerationToGenerationYBisJahrgang1995()
    {
        $this->generation = self::GENERATION_GENERATION_Y_BIS_JAHRGANG_1995;
    }

    /**
     * @return bool
     */
    public function isGenerationGenerationZBisJahrgang2010()
    {
        return $this->generation === self::GENERATION_GENERATION_Z_BIS_JAHRGANG_2010;
    }

    public function setGenerationToGenerationZBisJahrgang2010()
    {
        $this->generation = self::GENERATION_GENERATION_Z_BIS_JAHRGANG_2010;
    }

    /**
     * @return string
     */
    public function displayGeschlecht()
    {
        return self::optsGeschlecht()[$this->geschlecht];
    }

    /**
     * @return bool
     */
    public function isGeschlechtMannlich()
    {
        return $this->geschlecht === self::GESCHLECHT_MANNLICH;
    }

    public function setGeschlechtToMannlich()
    {
        $this->geschlecht = self::GESCHLECHT_MANNLICH;
    }

    /**
     * @return bool
     */
    public function isGeschlechtWeiblich()
    {
        return $this->geschlecht === self::GESCHLECHT_WEIBLICH;
    }

    public function setGeschlechtToWeiblich()
    {
        $this->geschlecht = self::GESCHLECHT_WEIBLICH;
    }

    /**
     * @return bool
     */
    public function isGeschlechtDivers()
    {
        return $this->geschlecht === self::GESCHLECHT_DIVERS;
    }

    public function setGeschlechtToDivers()
    {
        $this->geschlecht = self::GESCHLECHT_DIVERS;
    }

    /**
     * @return bool
     */
    public function isGeschlechtKeineAngabe()
    {
        return $this->geschlecht === self::GESCHLECHT_KEINE_ANGABE;
    }

    public function setGeschlechtToKeineAngabe()
    {
        $this->geschlecht = self::GESCHLECHT_KEINE_ANGABE;
    }

    /**
     * @return string
     */
    public function displayStandort()
    {
        return self::optsStandort()[$this->standort];
    }

    /**
     * @return bool
     */
    public function isStandortHh()
    {
        return $this->standort === self::STANDORT_HH;
    }

    public function setStandortToHh()
    {
        $this->standort = self::STANDORT_HH;
    }

    /**
     * @return bool
     */
    public function isStandortMv()
    {
        return $this->standort === self::STANDORT_MV;
    }

    public function setStandortToMv()
    {
        $this->standort = self::STANDORT_MV;
    }

    /**
     * @return bool
     */
    public function isStandortNds()
    {
        return $this->standort === self::STANDORT_NDS;
    }

    public function setStandortToNds()
    {
        $this->standort = self::STANDORT_NDS;
    }

    /**
     * @return bool
     */
    public function isStandortSh()
    {
        return $this->standort === self::STANDORT_SH;
    }

    public function setStandortToSh()
    {
        $this->standort = self::STANDORT_SH;
    }

    /**
     * @return bool
     */
    public function isStandortAuslandsstudio()
    {
        return $this->standort === self::STANDORT_AUSLANDSSTUDIO;
    }

    public function setStandortToAuslandsstudio()
    {
        $this->standort = self::STANDORT_AUSLANDSSTUDIO;
    }

    /**
     * @return string
     */
    public function displayTätigkeitsfeld()
    {
        return self::optsTätigkeitsfeld()[$this->tätigkeitsfeld];
    }

    /**
     * @return bool
     */
    public function isTätigkeitsfeldProgrammspezifisch()
    {
        return $this->tätigkeitsfeld === self::TATIGKEITSFELD_PROGRAMMSPEZIFISCH;
    }

    public function setTätigkeitsfeldToProgrammspezifisch()
    {
        $this->tätigkeitsfeld = self::TATIGKEITSFELD_PROGRAMMSPEZIFISCH;
    }

    /**
     * @return bool
     */
    public function isTätigkeitsfeldVerwaltungsspezifisch()
    {
        return $this->tätigkeitsfeld === self::TATIGKEITSFELD_VERWALTUNGSSPEZIFISCH;
    }

    public function setTätigkeitsfeldToVerwaltungsspezifisch()
    {
        $this->tätigkeitsfeld = self::TATIGKEITSFELD_VERWALTUNGSSPEZIFISCH;
    }

    /**
     * @return bool
     */
    public function isTätigkeitsfeldProduktionsspezifisch()
    {
        return $this->tätigkeitsfeld === self::TATIGKEITSFELD_PRODUKTIONSSPEZIFISCH;
    }

    public function setTätigkeitsfeldToProduktionsspezifisch()
    {
        $this->tätigkeitsfeld = self::TATIGKEITSFELD_PRODUKTIONSSPEZIFISCH;
    }

    /**
     * @return string
     */
    public function displayFührungskraft()
    {
        return self::optsFührungskraft()[$this->führungskraft];
    }

    /**
     * @return bool
     */
    public function isFührungskraftJa()
    {
        return $this->führungskraft === self::FUHRUNGSKRAFT_JA;
    }

    public function setFührungskraftToJa()
    {
        $this->führungskraft = self::FUHRUNGSKRAFT_JA;
    }

    /**
     * @return bool
     */
    public function isFührungskraftNein()
    {
        return $this->führungskraft === self::FUHRUNGSKRAFT_NEIN;
    }

    public function setFührungskraftToNein()
    {
        $this->führungskraft = self::FUHRUNGSKRAFT_NEIN;
    }
}
