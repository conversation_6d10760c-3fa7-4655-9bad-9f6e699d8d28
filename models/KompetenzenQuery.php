<?php

namespace app\models;

/**
 * This is the ActiveQuery class for [[Kompetenzen]].
 *
 * @see Kompetenzen
 */
class KompetenzenQuery extends \yii\db\ActiveQuery
{
    /*public function active()
    {
        return $this->andWhere('[[status]]=1');
    }*/

    /**
     * {@inheritdoc}
     * @return Kompetenzen[]|array
     */
    public function all($db = null)
    {
        return parent::all($db);
    }

    /**
     * {@inheritdoc}
     * @return Kompetenzen|array|null
     */
    public function one($db = null)
    {
        return parent::one($db);
    }
}
