<?php

namespace app\models;


/**
 * This is the model class for table "Domaene".
 *
 * @property int $id
 * @property string $domaene
 */
class Domaene extends Basismodel
{

    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'domaene';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['domaene'], 'required'],
            [['domaene'], 'string', 'max' => 255],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'domaene' => 'Domain',
        ];
    }

}
