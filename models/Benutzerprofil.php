<?php

namespace app\models\enums;

use Yii;
use yii2mod\enum\helpers\BaseEnum;

/**
 * This is the model class for table "benutzerprofil".
 *
 * @property int $id
 * @property string $generation
 * @property string $taetigkeitsfeld
 * @property boolean|null $fuehrungskraft
 * @property string $standort
 * @property string $geschlecht
 */
class Benutzerprofil extends BaseEnum
{
    /**
     * {@inheritdoc}
     */
    /* Generation*/
    const BOOMER = 0;
    const GENX = 1;
    const GENY = 2;
    const GENZ = 3;

    public static $list_generation = [
        self::BOOMER => 'Babyboomer bis Jahrgang 1964',
        self::GENX => 'Generation X bis Jahrgang 1980',
        self::GENY => 'Generation Y bis Jahrgang 1995',
        self::GENZ => 'Generation X bis Jahrgang 2010',
    ];

    /* Taetigkeitsfeld */
    const PROGRAMTECHNISCH = 0;
    const VERWALTUNGSSPEZIFISCH = 1;
    const PRODUKTIONSSPEZIFISCH = 2;

    public static $list_taetigkeitsfeld = [
        self::PROGRAMTECHNISCH => 'Programtechnisch',
        self::VERWALTUNGSSPEZIFISCH => 'Verwaltungsspezifisch',
        self::PRODUKTIONSSPEZIFISCH => 'Produktionsspezifisch'
    ];

    /* Standort*/
    const HH = 0;
    const MV = 1;
    const NDS = 2;
    const SH = 3;
    const AUSLANDSSTUDIO = 4;

    public static $list_standort = [
        self::HH => 'HH',
        self::MV => 'MV',
        self::NDS => 'NDS',
        self::SH => 'SH',
        self::AUSLANDSSTUDIO => 'Auslandsstudio',
    ];

    /* Geschlecht*/
    const MAENNLICH = 0;
    const WEIBLICH = 1;
    const DIVERS = 2;
    const KEINE_ANGABE = 3;

    public static $list_geschlecht = [
        self::MAENNLICH => 'MAENNLICH',
        self::WEIBLICH => 'Weiblich',
        self::DIVERS => 'Divers',
        self::KEINE_ANGABE => 'Keine Angabe',
    ];

    public static function tableName()
    {
        return 'benutzerprofil';
    }

    private static function optsGeneration()
    {
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['fuehrungskraft'], 'default', 'value' => null],
            [['generation', 'taetigkeitsfeld', 'standort', 'geschlecht'], 'required'],
            [['generation', 'taetigkeitsfeld', 'standort', 'geschlecht'], 'string'],
            [['fuehrungskraft'], 'integer'],
            ['generation', 'in', 'range' => array_keys(self::optsGeneration())],
            ['taetigkeitsfeld', 'in', 'range' => array_keys(self::optsTaetigkeitsfeld())],
            ['standort', 'in', 'range' => array_keys(self::optsStandort())],
            ['geschlecht', 'in', 'range' => array_keys(self::optsGeschlecht())],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'generation' => 'Generation',
            'taetigkeitsfeld' => 'Tätigkeitsfeld',
            'fuehrungskraft' => 'Führungskraft',
            'standort' => 'Standort',
            'geschlecht' => 'Geschlecht',
        ];
    }

    /**
     * {@inheritdoc}
     * @return BenutzerprofilQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new BenutzerprofilQuery(get_called_class());
    }
}
