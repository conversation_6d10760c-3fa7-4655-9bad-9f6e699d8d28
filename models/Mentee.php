<?php

namespace app\models;

use Yii;

/**
 * This is the model class for table "mentee".
 *
 * @property int $id
 */
class Mentee extends Basismodel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'mentee';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
        ];
    }

    /**
     * {@inheritdoc}
     * @return MenteeQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new MenteeQuery(get_called_class());
    }
}
