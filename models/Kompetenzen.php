<?php

namespace app\models;

use Yii;
use yii\bootstrap4\Html;
use yii\helpers\ArrayHelper;

/**
 * This is the model class for table "kompetenzen".
 *
 * @property int $id
 * @property string $rolle
 * @property string $benutzer_id
 * @property int|null $internes_netzwerk
 * @property int|null $ki
 * @property int|null $ndr_knowhow
 * @property int|null $externe_erfahrung
 * @property int|null $digitalisierung
 * @property int|null $externes_netzwerk
 * @property int|null $socialmedia
 * @property int|null $moderation
 * @property int|null $worklifebalance
 * @property int|null $präsentationen
 * @property int|null $resilienz
 * @property int|null $gender_diversity
 *
 * @property int|null $wunsch_internes_netzwerk
 * @property int|null $wunsch_ki
 * @property int|null $wunsch_ndr_knowhow
 * @property int|null $wunsch_externe_erfahrung
 * @property int|null $wunsch_digitalisierung
 * @property int|null $wunsch_externes_netzwerk
 * @property int|null $wunsch_socialmedia
 * @property int|null $wunsch_moderation
 * @property int|null $wunsch_worklifebalance
 * @property int|null $wunsch_präsentationen
 * @property int|null $wunsch_resilienz
 * @property int|null $wunsch_gender_diversity
 */
class Kompetenzen extends Basismodel
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'kompetenzen';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['rolle', 'benutzer_id', 'internes_netzwerk', 'ki', 'ndr_knowhow', 'externe_erfahrung', 'digitalisierung', 'externes_netzwerk', 'socialmedia', 'moderation', 'worklifebalance', 'präsentationen', 'resilienz', 'gender_diversity',
                'wunsch_internes_netzwerk', 'wunsch_ki', 'wunsch_ndr_knowhow', 'wunsch_externe_erfahrung', 'wunsch_digitalisierung', 'wunsch_externes_netzwerk', 'wunsch_socialmedia', 'wunsch_moderation', 'wunsch_worklifebalance', 'wunsch_präsentationen', 'wunsch_resilienz', 'wunsch_gender_diversity'], 'safe'],
//            [['rolle', 'benutzer_id', 'internes_netzwerk', 'ki', 'ndr_knowhow', 'externe_erfahrung', 'digitalisierung', 'externes_netzwerk', 'socialmedia', 'moderation', 'worklifebalance', 'präsentationen', 'resilienz', 'gender_diversity'], 'integer'],
            [['rolle'], 'string', 'max' => 12],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'rolle' => 'Rolle',
            'benutzer_id' => 'Benutzer ID',
            'internes_netzwerk' => 'Ein gutes internes Netzwerk',
            'ki' => 'Künstliche Intelligenz',
            'ndr_knowhow' => 'NDR Know-How',
            'externe_erfahrung' => 'Erfahrung aus anderen Unternehmen',
            'digitalisierung' => 'Digitalisierung',
            'externes_netzwerk' => 'Ein gutes externes Netzwerk',
            'socialmedia' => 'Social Media',
            'moderation' => 'Moderation',
            'worklifebalance' => 'Work-Life-Balance',
            'präsentationen' => 'Präsentationen',
            'resilienz' => 'Resilienz',
            'gender_diversity' => 'Gender und Diversity',

            'wunsch_internes_netzwerk' => 'Ein gutes internes Netzwerk',
            'wunsch_ki' => 'Künstliche Intelligenz',
            'wunsch_ndr_knowhow' => 'NDR Know-How',
            'wunsch_externe_erfahrung' => 'Erfahrung aus anderen Unternehmen',
            'wunsch_digitalisierung' => 'Digitalisierung',
            'wunsch_externes_netzwerk' => 'Ein gutes externes Netzwerk',
            'wunsch_socialmedia' => 'Social Media',
            'wunsch_moderation' => 'Moderation',
            'wunsch_worklifebalance' => 'Work-Life-Balance',
            'wunsch_präsentationen' => 'Präsentationen',
            'wunsch_resilienz' => 'Resilienz',
            'wunsch_gender_diversity' => 'Gender und Diversity',
        ];
    }

    public function getId()
    {
        return $this->id;
    }

    /**
     * {@inheritdoc}
     * @return KompetenzenQuery the active query used by this AR class.
     */
    public static function find()
    {
        return new KompetenzenQuery(get_called_class());
    }

    public function getKompetenzen($benutzer_id, $rolle) {
        if ($rolle == "" OR $benutzer_id == 0) {
            return false;
        }
//        elseif ($rolle == "menteementor") {
//
//        }
        else {
            $rows = (new \yii\db\Query())
                ->select(['internes_netzwerk', 'ki', 'ndr_knowhow', 'externe_erfahrung', 'digitalisierung', 'externes_netzwerk', 'socialmedia', 'moderation', 'worklifebalance', 'präsentationen', 'resilienz', 'gender_diversity'])
                ->from('kompetenzen')
                ->where(['rolle' => $rolle, 'benutzer_id' => $benutzer_id])
                ->one();
            return $rows;
        }

    }

}
