<?php

namespace app\models;

use Yii;
use yii\base\NotSupportedException;
use yii\db\ActiveQuery;
use yii\helpers\ArrayHelper;
//use app\models\BenutzerKompetenzen;
//use app\models\BenutzerHobbies;
//use app\models\BenutzerGeneration;
//use app\models\BenutzerEigenschaften;
//use app\models\BenutzerStandort;
//use app\models\BenutzerTaetigkeit;
//use app\models\BenutzerGesuchtesTaetigkeitsfeld;
//use app\models\BenutzerGesuchteGeneration;
//use app\models\BenutzerGesuchteFuehrungskraft;
//use app\models\BenutzerGesuchtesGeschlecht;
//use app\models\BenutzerGesuchterStandort;
//use app\models\BenutzerGesuchterWieOft;
//use app\models\BenutzerGesuchterWieTreffen;

/**
 * This is the model class for table "benutzer".
 *
 * @property int $id
 * @property string|null $benutzername
 * @property int|null $domaene_id
 * @property int|null $wunschkompetenzen_value
 * @property int|null $erfahrungskompetenzen_value
 * @property string|null $nachname
 * @property string|null $vorname
 * @property string|null $email
 * @property string|null $passwort
 * @property string|null $email_alternativ
 * @property string|null $employeeid
 * @property string|null $oid
 * @property string|null $passwort_neu1
 * @property string|null $passwort_neu2
 */
class Benutzer extends Basismodel implements \yii\web\IdentityInterface
{

    public $domaeneStr = "";
    public $passwort_neu1;
    public $passwort_neu2;

    public static function tableName()
    {
        return 'benutzer';
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentity($id)
    {
        try {
            return static::findOne($id);
        } catch (\yii\base\InvalidConfigException $e) {
            if ($e->getMessage() == "The table does not exist: Benutzer") {
                Yii::$app->sql->parsefile("../migrations/create_basic_tables.sql");
            } else {
                throw $e;
            }
        }
        return false;
    }

    /**
     * {@inheritdoc}
     */
    public static function findIdentityByAccessToken($token, $type = null)
    {
        throw new NotSupportedException();
    }

    public function rules()
    {
        return [
            ['email', 'required'],
            ['email', 'unique'],
            [['email', 'benutzername', 'vorname', 'nachname', 'passwort', 'employeeid'], 'string', 'max' => 255],
            ['domaene_id', 'integer'],
            [['email', 'email_alternativ'], 'email', 'message' => 'Eingetragener Wert ist keine Mail-Adresse!'],
            [['passwort_neu1'], 'validateNewpassword', 'skipOnEmpty' => false],
            [['passwort_neu2'], 'safe'],
        ];
    }

    public function validateNewpassword($attribute, $params, $validator)
    {
        if ($this->passwort_neu1 != $this->passwort_neu2) {
            $this->addError('passwort_neu2', 'Die Passworte stimmen nicht überein!');
            return false;
        } else if ($this->isNewRecord && empty($this->passwort_neu1)) {
            $this->setPassword("Ca137x");
            return true;
        } else if (!empty($this->passwort_neu1)) {
            $this->setPassword($this->passwort_neu1);
            return true;
        } else {
            return true;
        }
    }

    public function setPassword($password)
    {
        $hash = Yii::$app->getSecurity()->generatePasswordHash($password);
        $this->passwort = $hash;
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'benutzername' => 'Benutzername',
            'domaene_id' => 'Landesfunkhaus',
            'vorname' => 'Vorname',
            'nachname' => 'Nachname',
            'email' => 'E-Mail Adresse',
            'passwort' => 'Passwort',
            'passwort_neu1' => 'Neues Passwort',
            'passwort_neu2' => 'Passwort bestätigen',
            'email_alternativ' => 'Altern. Adresse',
            'employeeid' => 'Personalnummer im AD',
            'rolleeIds' => 'zugewiesene Rollen',
            'rollenString' => 'Rollen',
            'oid' => 'Benutzer ID aus dem Azure'
        ];
    }

    public function getWunschkompetenzenobj()
    {
        return $this->hasOne(LabelManager::class, ["value" => "wunschkompetenzen_value"])->andWhere(["liste" => "Wunschkompetenzen"])->alias("Wunschkompetenzen");
//        return $this->hasOne(LabelManager::class, ["value" => "helplinekategorie_value"])->andWhere(["liste" => "helplinekategorie"])->alias("helplinekategorie");
    }

    /**
     * Erstellt "Vorname Nachname" des Nutzers
     *
     * @return string
     */
    public function getFullname()
    {
        return $this->vorname . " " . $this->nachname;
    }

    public function getDisplayname()
    {
        return empty(trim($this->fullname))? $this->email : $this->fullname;
    }

    /**
     * Getter fuer Domaene
     *
     * @return \yii\db\ActiveQuery
     */
    public function getDomaene()
    {
        return $this->hasOne(Domaene::class, ['id' => 'domaene_id']);
    }

    /**
     * {@inheritdoc}
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * {@inheritdoc}
     */
    public function getAuthKey()
    {
        return md5($this->passwort);
    }

    /**
     * {@inheritdoc}
     */
    public function validateAuthKey($authKey)
    {
        return $this->authKey === $authKey;
    }

    /**
     * LDAP Login Funktion
     *
     * Beim Aufruf dieser Funktion stehen im Model nur Benutzername und Passwort,
     * die aus der Loginform übergeben werden. Es handelt sich also nicht um ein
     * ActiveRecord.
     *
     * Es wird versucht, sich mit diesen Daten am LDAP anzumelden
     * Bei Erfolg wird der lokale Benutzer aktualisiert bzw angelegt
     *
     *
     * @return bool whether the user is logged in successfully
     */
    public function LDAPlogin($rememberMe = false, $noldap = false)
    {
        if ($noldap === true) {
            return $this->login($rememberMe);
        }

        if (!filter_var($this->email, FILTER_VALIDATE_EMAIL)) {
            $this->addError('email', 'Das Login ist nur mit der E-Mail möglich! (Windows 10 Account)');
            return false;
        }

        Yii::$app->ldap->init();

        if (Yii::$app->ldap->bind($this->email, $this->passwort)) {
            return $this->doLocalLogin($rememberMe);
        }

        if (!Yii::$app->ldap->stoptrying && $this->login($rememberMe)) {
            return true;
        }

        /**
         * Alle Loginversuche fehlgeschlagen, gib was zurück!
         */

        if (!empty(Yii::$app->ldap->ldap_ext_errno)) {
            $this->addError('passwort', Yii::$app->ldap->ldap_ext_errstr);
        } else {
            $this->addError('passwort', 'Unbekannter Windows Benutzer (' . $this->email . ') oder falsches Passwort!');
        }

        return false;
    }

    /**
     * Der normale Login ohne LDAP Funktion
     *
     * Beim Aufruf dieser Funktion stehen im Model nur EMail und Passwort,
     * die aus der Loginform übergeben werden. Es handelt sich also nicht um ein
     * ActiveRecord. Es wird dann ein zweites Model anhand der EMail gesucht und
     * das Passwort von diesem mit dem übergebenen Passwort verglichen!
     *
     * @return bool whether the user is logged in successfully
     */
    public function login($rememberMe = false)
    {
        $dbmodel = Benutzer::findByUsername($this->email);
        if (empty($dbmodel)) {
            return false;
        }
        if ($this->validatePassword($dbmodel->passwort)) {
            return Yii::$app->user->login($dbmodel, $rememberMe ? 3600 * 24 * 30 : 0);
        } else {
            return false;
        }
    }

    /**
     * Finds user by username (or E-Mail)
     *
     * @param string $username
     *
     * @return static|null
     */
    public static function findByUsername($username)
    {
        if ($username) {
            if (substr_count($username, '@') > 0) {
                /**
                 * Der Username ist vermutlich eine E-Mailadresse !
                 */
                $options = ['email' => $username];
            } else {
                $options = ['benutzername' => $username];
            }
            return self::findOne($options);
        }
        else return false;
    }

    /**
     * Validates password
     *
     * @param string $password password to validate
     *
     * @return bool if password provided is valid for current user
     */
    public function validatePassword($passwort)
    {
        if (empty($this->passwort)) {
            $this->addError('passwort', 'Passwort kann nicht leer sein!');
        } else if (Yii::$app->getSecurity()->validatePassword($this->passwort, $passwort)) {
            return true;
        }
        $this->addError('passwort', 'Unbekannter Windows Benutzer oder falsches Passwort!');
        return false;
    }

    /**
     * Legt einen Eintrag in der lokalen Benutzertabelle an, wenn er noch nicht existiert und loggt den Benutzer ein
     *
     * @param string $username
     *
     * @return bool
     * @throws \yii\base\Exception
     */
    public function doLocalLogin($rememberMe)
    {
        $ldap = Yii::$app->ldap;
        $ldap->search_account($this->email);

        if ($ldap->result['count'] > 0) {
            $results = $ldap->resultArray;
            $dbmodel = static::findOrCreateLocalUser(array_shift($results));
        } else {
            $this->addError('email', 'Der Benutzer wurde im LDAP nicht gefunden!');
            return false;
        }
        $dbmodel->setPassword($this->passwort);
        if (empty($dbmodel->email)) {
            $this->addError('email', 'Im AD ist für diesen Account keine E-Mail hinterlegt, der Login ist nur mit normalen Nutzeraccounts möglich!');

            return false;
        } else {
            if (!$dbmodel->save()) {
                $this->addError('email', 'LDAP Benutzer gefunden, aber lokales Profil konnte nicht gespeichert werden!');

                return false;
            } else {
                return Yii::$app->user->login($dbmodel, $rememberMe ? 3600 * 24 * 30 : 0);
            }
        }
    }

    public static function findOrCreateLocalUser($data)
    {
        if (!empty($data['oid'])) {
            $dbmodel = Benutzer::findOne(['oid' => $data['oid']]);
        }
        if (empty($dbmodel) && !empty($data['employeeid'])) {
            $dbmodel = Benutzer::findOne(['employeeid' => $data['employeeid']]);
        }
        if (empty($dbmodel) && !empty($data['mail'])) {
            $dbmodel = Benutzer::findOne(['email' => $data['mail']]);
        }
        if (empty($dbmodel)) {
            /* Benutzer anlegen */
            $dbmodel = new Benutzer;
        }

        if (!empty($data['mail'])) {
            $dbmodel->email = $data['mail'];
        } else if (!empty($data['userprincipalname'])) {
            $dbmodel->email = $data['userprincipalname'];
        }
        $dbmodel->vorname = $data['givenname'] ?? null;
        $dbmodel->nachname = $data['sn'] ?? null;
        $dbmodel->benutzername = $data['samaccountname'] ?? null;
        $dbmodel->employeeid = $data['employeeid'] ?? null;
        $dbmodel->oid = $data['oid'] ?? null;

        $dbmodel->domaene_id = !empty($data['st']) ? Domaene::ID($data['st']) : 0;

        return $dbmodel;
    }

    /**
     * Finds user by employeeid
     *
     * @param string $username
     *
     * @return static|null
     */
    public static function findByEmployeeid($employeeid)
    {
        $options = ['employeeid' => $employeeid];
        return self::findOne($options);
    }

    /**
     * Gibt Liste der Rollen des Nutzers zurück
     *
     * @return ActiveQuery
     * @throws \yii\base\InvalidConfigException
     */
    public function getRollen()
    {
        return $this->hasMany(Rolle::class, ['id' => 'rolle_id'])
            ->viaTable('rolle_benutzer', ['benutzer_id' => 'id']);
    }

    /**
     * Gibt die IDs aller Rolle als Array zurück
     *
     */

    public function getRollenIds()
    {
        return ArrayHelper::map($this->rollen, "id", "id");
    }

    /**
     * Gibt eine für die Views lesbare, kommaseparierte Liste aller Rollen eines Nutzers
     * zurück
     *
     * @return string
     */
    public function getRollenString()
    {
        $readable = "";
        foreach ($this->rollen as $rolle) {
            $readable .= $rolle->rolle . ", ";
        }
        return substr($readable, 0, -2);
    }

    /**
     * Speichert die Rollen eines Nutzers. Dazu werden bestehende Rollen erst
     * komplett gelöscht und dann neu angelegt. $roleArray enthält die Ids der
     * Rollen, die der Nutzer im Formular ausgewählt hat.
     *
     * @param [] $roleArray
     */
    public function saveRollen($roleArray)
    {
        $this->deleteAllRoles();
        if (is_array($roleArray)) {
            foreach ($roleArray as $num => $roleId) {
                if (Yii::$app->user->isAdmin || Yii::$app->user->hasRoleId($roleId)) {
                    $role = new RolleBenutzer();
                    $role->benutzer_id = $this->id;
                    $role->rolle_id = $roleId;
                    $role->save();
                }
            }
        }
    }

    /**
     * Löscht alle Rollen eines Nutzers.
     */
    public function deleteAllRoles()
    {
        RolleBenutzer::deleteAll(['benutzer_id' => $this->id]);
    }

    /**
     * Löscht alle Rollen für den eingeloggten MA im Mentoring Programm und setzt die gewählte Rolle.
     *
     * @param integer $rolle_id
     */
    public function addRolle($rolle_id)
    {
        $this->deleteAllRoles();
        $relation = new RolleBenutzer();
        $relation->benutzer_id = $this->id;
        $relation->rolle_id = $rolle_id;
        $relation->save();
    }

    /**
     * Gibt die Email-Adresse des Nutzers zurück. Ist eine alternative
     * Mailadresse angegeben, wird diese zurückgeben.
     *
     * @return string
     */
    public function getAddress()
    {
        return !empty($this->email_alternativ) ? $this->email_alternativ : $this->email;
    }

    /**
     * Prüft, ob Nutzer die Rolle $rolleName besitzt oder ein Admin ist.
     *
     * @param string $rolleName
     *
     * @return boolean
     */
    public function hatRolle($rolleName)
    {
        $isAdmin = false;
        $isInGroup = false;
        foreach ($this->rollen as $rolle) {
            if ($rolle->rolle === $rolleName) {
                $isInGroup = true;
            } else if ($this->isAdmin()) {
                $isAdmin = true;
            }
        }
        return ($isAdmin || $isInGroup);
    }

    /**
     * Prüft, ob aktueller Nutzer die Rolle Admin hat oder dessen
     * Nutzername ADMIN ist.
     *
     * @return bool
     */
    public function isAdmin()
    {
        foreach ($this->rollen as $rolle) {
            if (strtolower($rolle->rolle ?? "") === "admin") {
                return true;
            }
        }
        return false;
    }

    public function resetPassword()
    {
        $password = md5(time());
        $this->setPassword($password);
        if ($this->save()) {
            $mail = Yii::$app->mailer->compose("passwort", ["model" => $this, "password" => $password])
                ->setFrom('<EMAIL>')
                ->setTo($this->email)
                ->setSubject('Ihr Passwort für den Zugang zur Anwendung ' . Konfiguration::getParameterByName("app_name", false))
                ->setTextBody('Bitte nutzen Sie für den ersten Login Ihre E-Mail und das Passwort ' . $password);
            return $mail->send();
        }
        return false;
    }


    public function save($runValidation = true, $attributeNames = null)
    {
        if (!parent::save($runValidation, $attributeNames)) {
            Yii::error("Fehler beim Speichern des Benutzers " . $this->displayname . ": " . $this->getErrorstring(), "mailtarget");
            return false;

        }
        return true;
    }

    public function delete()
    {
        $this->deleteAllRoles();
        return parent::delete();
    }



}
