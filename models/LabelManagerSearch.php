<?php

namespace app\models;

use Yii;
use yii\base\Model;
use yii\data\ActiveDataProvider;
use app\models\LabelManager;

/**
 * LabelManagerSearch represents the model behind the search form of `app\models\LabelManager`.
 */
class LabelManagerSearch extends LabelManager
{
    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['id','deprecated'], 'integer'],
            [['liste', 'value', 'comment'], 'safe'],
        ];
    }

    /**
     * @inheritdoc
     */
    public function scenarios()
    {
        // bypass scenarios() implementation in the parent class
        return Model::scenarios();
    }

    /**
     * Creates data provider instance with search query applied
     *
     * @param array $params
     *
     * @return ActiveDataProvider
     */
    public function search($params)
    {
        $query = LabelManager::find();

        // add conditions that should always apply here

        $dataProvider = new ActiveDataProvider([
            'query' => $query,
        ]);

        $this->load($params);

        if (!$this->validate()) {
            // uncomment the following line if you do not want to return any records when validation fails
            // $query->where('0=1');
            return $dataProvider;
        }

        // grid filtering conditions
        $query->andFilterWhere([
            'id' => $this->id,
            
        ]);
        $query->andFilterWhere(['like', 'liste', $this->liste])
            ->andFilterWhere(['like', 'value', $this->value])
            ->andFilterWhere(['like', 'comment', $this->comment])
            ->andFilterWhere(['like','deprecated',$this->deprecated]);
        return $dataProvider;
    }
}
