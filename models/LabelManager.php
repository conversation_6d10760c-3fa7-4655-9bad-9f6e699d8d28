<?php

namespace app\models;

use Yii;
use yii\helpers\ArrayHelper;
use yii\jui\Selectable;

/**
 * This is the model class for table "Labelmanager".
 *
 * @property int $id
 * @property string $liste
 * @property int $key
 * @property int $value
 * @property string $label
 * @property string $comment
 * @property int $deprecated
 */
class LabelManager extends Basismodel
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'labelmanager';
    }

    public static function getLabels()
    {
        $inst = new self();
        return $inst->attributeLabels();
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'liste' => 'Liste',
            'value' => 'Wert in der Datenbank',
            'label' => 'Wert (angezeigtes Label)',
            'comment' => 'Kommentar',
            'deprecated' => 'Wert ist veraltet',
            'sortorder' => 'Sortierung',
        ];
    }

    /**
     * Findet den Value zu einem Label einer Liste
     *
     * @param string $liste
     * @param string $value
     *
     * @return Integer
     */
    public static function translateValueToLabel($liste, $value)
    {
        $label = LabelManager::find()->where(['value' => $value, 'liste' => $liste])->one();
        if (isset($label) && !empty($label)) {
            return $label->label;
        } else {
            return null;
        }
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['liste', 'value', 'label'], 'required'],
            [['value'], 'checkunique'],
            [['comment'], 'string'],
            [['liste'], 'string', 'max' => 255],
            [['deprecated', 'sortorder', 'value'], 'integer'],
        ];
    }

    public function checkunique($attribute, $params, $validator)
    {
        $query = LabelManager::find()->where(['value' => $this->value, 'liste' => $this->liste, 'deprecated' => 0]);
        if (!$this->isNewRecord) {
            $query->andWhere(["!=", "id", $this->id]);
        }
        $exists = $query->one();

        if ($exists) {
            $this->addError($attribute, 'Der Wert ' . $this->value . ' wird für die Liste ' . $this->liste . ' schon verwendet. Nächster Wert: ' . LabelManager::nextvalue($this->liste));
        }
    }

    /**
     * Gibt eine Liste aller Listen zurück
     *
     * @return array
     */

    public static function getListen()
    {
        return ArrayHelper::map(static::find()->select('liste')->distinct()->all(), 'liste', 'liste');
    }

    public static function nextvalue($liste)
    {
        $max = self::find()->select("max(value)")->where(["=", "liste", $liste])->scalar();
        if (empty($max)) {
            return 1;
        } else {
            return $max + 1;
        }
    }

    public static function getorcreatelabel($label, $liste)
    {
        $exists = self::find()->where(["AND", ["=", "liste", $liste], ["=", "label", $label]])->one();
        if (!$exists) {
            $lm = new self();
            $lm->value = self::nextvalue($liste);
            $lm->liste = $liste;
            $lm->label = $label;
            $lm->deprecated = 0;
            $lm->save();
            return $lm;
        } else {
            return $exists;
        }
    }

    public static function getListArray($liste)
    {
        return ArrayHelper::map(self::find()->where(["AND", ["!=", "deprecated", 1], ["=", "liste", $liste]])->all(), "value", "label");
    }

}
