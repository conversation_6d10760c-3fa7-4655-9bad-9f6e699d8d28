<?php

namespace app\controllers;

use app\models\Benutzer;
use app\models\Kompetenzen;
use app\models\Tandempartner;
use PHPUnit\Framework\Constraint\IsNull;
use Yii;
use app\models\Profil;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\models\Rolle;

use app\models\KompetenzenSearch;


/**
 * ProfilController implements the CRUD actions for Profil model.
 */
class ProfilController extends Controller
{
    /**
     * @inheritDoc
     */
//    public function behaviors()
//    {
//        return array_merge(
//            parent::behaviors(),
//            [
//                'verbs' => [
//                    'class' => VerbFilter::className(),
//                    'actions' => [
//                        'delete' => ['POST'],
//                    ],
//                ],
//            ]
//        );
//    }

    /**
     * Lists all Profil models.
     *
     * @return string
     */
    public function actionIndex($rolle)
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $GET = Yii::$app->request->get();
        $rolle = $GET['rolle'];

        if (strlen($rolle) < 4) {
            return $this->render('/site/index', [
            ]);
        }

        $model = Profil::findOne(['benutzer_id' => Yii::$app->user->getIdentity()->getId()]);
        if (empty($model)) {
            $model = new Profil(['benutzer_id' => Yii::$app->user->getIdentity()->getId(), 'rolle' => $rolle]);
        }
        else {
            if ($model->rolle != $rolle) {
                $model->rolle = $rolle;
                $model->save();
            }
        }
//echo "<pre>";var_dump($model);exit;

        return $this->render('index', [
                'model' => $model,
                'rolle' => $rolle,
                'todo' => 'chooseBeides',
            ]);

        var_dump($model); echo "rolle:".$rolle;
    }

    /**
     * Displays a single Profil model.
     * @param int $id ID
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Profil model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new Profil();

        if ($this->request->isPost) {
            if ($model->load($this->request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Profil model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $id ID
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
//    public function actionUpdate($id)
//    {
//        $model = $this->findModel($id);
//
//        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
//            return $this->redirect(['view', 'id' => $model->id]);
//        }
//
//        return $this->render('update', [
//            'model' => $model,
//        ]);
//    }

    public function actionUpdate()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $POST = Yii::$app->request->post();
        if (empty($POST)) {
            return $this->redirect(['/site/index']);
        }
//var_dump($POST);exit;
        if(!empty($POST["Profil"]['benutzer_id'])) {
            $model= new Profil();
            $this->view->title = 'Es wurden zum ersten Mal Profil angegeben';
        } else {
            $model = Profil::findOne(['benutzer_id' => $POST["Profil"]['benutzer_id'], 'rolle' => $POST["Profil"]['rolle']]);
        }

        $rolle = $POST["Profil"]['rolle'];
//        $this->view->params['breadcrumbs'][] = ['label' => 'Profils', 'url' => ['index']];
//        $this->view->params['breadcrumbs'][] = $this->view->title;

        if (isset($POST["Profil"]['benutzer_id'])) {
            if ($model->load($POST) && $save = $model->validate()) {
                $sql = "SELECT id FROM `profil` WHERE rolle = '" . $rolle . "' AND benutzer_id = " . Yii::$app->user->getIdentity()->getId();
                $result = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($result);exit;
                if (!isset($result[0]["id"])) {
                    $save = $model->save();

                    return $this->redirect(['/tandempartner/index',
                        'rolle' => $rolle]);
                } else {
//                  $save = $model->update(); // 14.04.2025: David Rieß: "update()" should have worked, but it didn't
                    $sql = "UPDATE `profil`
                               SET `rolle`              = '". $rolle . "',                                  `benutzer_id`           = ". Yii::$app->user->getIdentity()->getId(). ",        `generation`        = '". $_POST['Profil']['generation'] . "',          `geschlecht`        = '" . $_POST['Profil']['geschlecht'] . "',     
                                   `tätigkeitsfeld`        = '" . $_POST['Profil']['tätigkeitsfeld'] . "',         `führungskraft`     = '" . $_POST['Profil']['führungskraft'] . "', 
                                   `geduld`             = '". $_POST['Profil']['geduld'] . "',              `fairness`              = '" . $_POST['Profil']['fairness'] . "',               `empathie`          = '" . $_POST['Profil']['empathie'] . "',           `loyalität`         = '". $_POST['Profil']['loyalität'] . "',
                                   `loyalität`          = '". $_POST['Profil']['loyalität'] . "',           `humor`                 = '" . $_POST['Profil']['humor'] . "',                  `verlässlichkeit`   = '" . $_POST['Profil']['verlässlichkeit'] . "',    `kritikfähigkeit`   = '". $_POST['Profil']['kritikfähigkeit'] . "',
                                   `hilfsbereitschaft`  = '". $_POST['Profil']['hilfsbereitschaft'] . "',   `hands_on_mentalität`   = '" . $_POST['Profil']['hands_on_mentalität'] . "',    `neugierde`         = '" . $_POST['Profil']['neugierde'] . "',          `sport`             = '". $_POST['Profil']['sport'] . "',
                                   `filme`              = '". $_POST['Profil']['filme'] . "',               `musik`                 = '" . $_POST['Profil']['musik'] . "',                  `reisen`            = '" . $_POST['Profil']['reisen'] . "',             `outdoor`           = '". $_POST['Profil']['outdoor'] . "',
                                   `kunst_und_kultur`   = '". $_POST['Profil']['kunst_und_kultur'] . "',    `yoga`                  = '" . $_POST['Profil']['yoga'] . "',                   `ehrenamt`          = '" . $_POST['Profil']['ehrenamt'] . "',           `weitere_hobbys`    = '". $_POST['Profil']['weitere_hobbys'] . "'
                             WHERE `id` = " . $result[0]["id"];
//echo $sql;
                    $update = Yii::$app->db->createCommand($sql)->queryAll();

                    return $this->redirect(['/tandempartner/index',
                        'rolle' => $rolle]);
                }
            }
            else {
                throw new NotFoundHttpException('Fehler beim Update des Profils1.');
            }
        }
        else {
            throw new NotFoundHttpException('Fehler beim Update des Profils2.');
        }

    }

    /**
     * Deletes an existing Profil model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $id ID
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Profil model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Profil the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Profil::findOne(['id' => $id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionChangevalues()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $POST = Yii::$app->request->post();
        $GET = Yii::$app->request->get();
        if (empty($POST)) {
            if (!isset($GET['benutzer_id'])) {
                return $this->redirect(['/site/index']);
            }
        }
        $rolle = Rolle::getRollenname();
        if(empty($GET['benutzer_id'])) {
            $model= new Profil();
            $this->view->title = 'Es wurden zum ersten Mal Profil angegeben';
        } else {

            $model = Profil::findOne(['benutzer_id' => $GET['benutzer_id'], 'rolle' => $rolle]);
        }
        if (is_null($model)) {
            return $this->redirect(['/site/index']);
        };

        if (isset($POST["Profil"]['benutzer_id'])) {
            if ($model->load($POST) ) { //&& $validate = $model->validate()
                $sql = "SELECT id FROM `profil` WHERE rolle = '" . $rolle . "' AND benutzer_id = " . Yii::$app->user->getIdentity()->getId();
                $result = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($result);exit;
                if (!isset($result[0]["id"])) {
                    $save = $model->save();

                    // Save competency data to kompetenzen table
                    $this->saveKompetenzenData($POST, $rolle);
                    
                    // Save tandempartner data
                    $this->saveTandempartnerData($POST, $rolle);

                    Yii::$app->session->setFlash('success', "Die Profil Daten wurden gespeichert", true);
                    return $this->redirect(['/site/index',
                        'rolle' => $rolle]);
                } else {
//                  $save = $model->update(); // 14.04.2025: David Rieß: "update()" should have worked, but it didn't
                    $sql = "UPDATE `profil`
                               SET `rolle`              = '". $rolle . "',                                  `benutzer_id`           = ". Yii::$app->user->getIdentity()->getId(). ",        `generation`        = '". $_POST['Profil']['generation'] . "',          `geschlecht`        = '" . $_POST['Profil']['geschlecht'] . "',
                                   `tätigkeitsfeld`        = '" . $_POST['Profil']['tätigkeitsfeld'] . "',         `führungskraft`     = '" . $_POST['Profil']['führungskraft'] . "',
                                   `geduld`             = '". $_POST['Profil']['geduld'] . "',              `fairness`              = '" . $_POST['Profil']['fairness'] . "',               `empathie`          = '" . $_POST['Profil']['empathie'] . "',           `loyalität`         = '". $_POST['Profil']['loyalität'] . "',
                                   `loyalität`          = '". $_POST['Profil']['loyalität'] . "',           `humor`                 = '" . $_POST['Profil']['humor'] . "',                  `verlässlichkeit`   = '" . $_POST['Profil']['verlässlichkeit'] . "',    `kritikfähigkeit`   = '". $_POST['Profil']['kritikfähigkeit'] . "',
                                   `hilfsbereitschaft`  = '". $_POST['Profil']['hilfsbereitschaft'] . "',   `hands_on_mentalität`   = '" . $_POST['Profil']['hands_on_mentalität'] . "',    `neugierde`         = '" . $_POST['Profil']['neugierde'] . "',          `sport`             = '". $_POST['Profil']['sport'] . "',
                                   `filme`              = '". $_POST['Profil']['filme'] . "',               `musik`                 = '" . $_POST['Profil']['musik'] . "',                  `reisen`            = '" . $_POST['Profil']['reisen'] . "',             `outdoor`           = '". $_POST['Profil']['outdoor'] . "',
                                   `kunst_und_kultur`   = '". $_POST['Profil']['kunst_und_kultur'] . "',    `yoga`                  = '" . $_POST['Profil']['yoga'] . "',                   `ehrenamt`          = '" . $_POST['Profil']['ehrenamt'] . "',           `weitere_hobbys`    = '". $_POST['Profil']['weitere_hobbys'] . "'
                             WHERE `id` = " . $result[0]["id"];
//echo $sql;
                    $update = Yii::$app->db->createCommand($sql)->queryAll();

                    // Save competency data to kompetenzen table
                    $this->saveKompetenzenData($POST, $rolle);
                    
                    // Save tandempartner data
                    $this->saveTandempartnerData($POST, $rolle);

                    Yii::$app->session->setFlash('success', "Die Profil Daten wurden gespeichert", true);
                    return $this->redirect(['/site/index',
                        'rolle' => $rolle]);
                }
            }
            else {
                throw new NotFoundHttpException('InvalidArgumentException');
            }
        }
        else {
            $kompetenzen_logged_in_user = Kompetenzen::find()->where(['benutzer_id' => Yii::$app->user->id, 'rolle' => $rolle])->one();
            $tandempartner = Tandempartner::find()->where(['benutzer_id' => Yii::$app->user->id, 'rolle' => $rolle])->one();

            // If no competency record exists, create a new empty one for the form
            if (!$kompetenzen_logged_in_user) {
                $kompetenzen_logged_in_user = new Kompetenzen();
                $kompetenzen_logged_in_user->benutzer_id = Yii::$app->user->id;
                $kompetenzen_logged_in_user->rolle = $rolle;
            }
            
            // If no tandempartner record exists, create a new empty one for the form
            if (!$tandempartner) {
                $tandempartner = new Tandempartner();
                $tandempartner->benutzer_id = Yii::$app->user->id;
                $tandempartner->rolle = $rolle;
            }

            return $this->render('changevalues', [
                'model' => $model,
                'rolle' => $rolle,
                'tandempartner' => $tandempartner,
                'kompetenzen_logged_in_user' => $kompetenzen_logged_in_user
            ]);
        }
    }

    public function actionChangeanzeigbar($anzeigbar, $benutzer_id, $rolle)
    {
        ($anzeigbar != 1) ? $anzeigbar = 0: $anzeigbar = 1;
        if (($model = Profil::findOne(['benutzer_id' => $benutzer_id, 'rolle' => $rolle])) !== null) {
            $model->anzeigbar = $anzeigbar;
            $res = $model->save();
            if ($res)
            $message = "Ihr Status wurde gespeichert";
            else $message = "Fehler beim Speichern";
            return json_encode($message);
        }
        else {
            $message = "Fehler beim Speichern";
            return json_encode($message);
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * Save competency data to kompetenzen table
     * @param array $POST The POST data containing competency information
     * @param string $rolle The user role
     * @return bool Success status
     */
     function saveKompetenzenData($POST, $rolle)
    {
        try {
            $benutzer_id = Yii::$app->user->getIdentity()->getId();

            // Debug: Log the POST data for competencies
            if (isset($POST['Kompetenzen'])) {
                Yii::info('Kompetenzen POST data: ' . json_encode($POST['Kompetenzen']), 'profil');
            }

            // Check if competency record exists
            $existingKompetenzen = Kompetenzen::findOne(['benutzer_id' => $benutzer_id, 'rolle' => $rolle]);

            if ($existingKompetenzen) {
                // Update existing record
                $kompetenzenModel = $existingKompetenzen;
            } else {
                // Create new record
                $kompetenzenModel = new Kompetenzen();
                $kompetenzenModel->benutzer_id = $benutzer_id;
                $kompetenzenModel->rolle = $rolle;
            }

            // Map competency fields from POST data
            $competencyFields = [
                'internes_netzwerk',
                'ki',
                'ndr_knowhow',
                'externe_erfahrung',
                'digitalisierung',
                'externes_netzwerk',
                'socialmedia',
                'moderation',
                'worklifebalance',
                'präsentationen',
                'resilienz',
                'gender_diversity'
            ];

            // Set competency values from POST data (checkbox values)
            foreach ($competencyFields as $field) {
                if (isset($POST['Kompetenzen'][$field])) {
                    // Checkbox is checked
                    $kompetenzenModel->$field = 1;
                } else {
                    // Checkbox is unchecked (unchecked checkboxes don't appear in POST)
                    $kompetenzenModel->$field = 0;
                }
            }

            // Also handle any additional competency fields that might be in the POST data
            if (isset($POST['Kompetenzen'])) {
                foreach ($POST['Kompetenzen'] as $fieldName => $fieldValue) {
                    if (in_array($fieldName, $competencyFields)) {
                        $kompetenzenModel->$fieldName = $fieldValue ? 1 : 0;
                    }
                }
            }

            // Validate and save the competency data
            if ($kompetenzenModel->validate()) {
                if ($kompetenzenModel->save()) {
                    Yii::info('Kompetenzen data saved successfully for user ' . $benutzer_id . ' with role ' . $rolle, 'profil');
                    return true;
                } else {
                    // Log save errors for debugging
                    Yii::error('Kompetenzen save failed: ' . json_encode($kompetenzenModel->errors), 'profil');
                    return false;
                }
            } else {
                // Log validation errors for debugging
                Yii::error('Kompetenzen validation failed: ' . json_encode($kompetenzenModel->errors), 'profil');
                return false;
            }

        } catch (\Exception $e) {
            // Log the exception for debugging
            Yii::error('Exception in saveKompetenzenData: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Save tandempartner data to tandempartner table
     * @param array $POST The POST data containing tandempartner information
     * @param string $rolle The user role
     * @return bool Success status
     */
     function saveTandempartnerData($POST, $rolle)
    {
        try {
            $benutzer_id = Yii::$app->user->getIdentity()->getId();

            // Debug: Log the POST data for tandempartner
            if (isset($POST['Tandempartner'])) {
                Yii::info('Tandempartner POST data: ' . json_encode($POST['Tandempartner']), 'profil');
            }

            // Check if tandempartner record exists
            $existingTandempartner = Tandempartner::findOne(['benutzer_id' => $benutzer_id, 'rolle' => $rolle]);

            if ($existingTandempartner) {
                // Update existing record
                $tandempartnerModel = $existingTandempartner;
            } else {
                // Create new record
                $tandempartnerModel = new Tandempartner();
                $tandempartnerModel->benutzer_id = $benutzer_id;
                $tandempartnerModel->rolle = $rolle;
            }

            // Map tandempartner fields from POST data
            $tandempartnerFields = [
                'standort',
                'treffen_art',
                'treffen_frequenz'
            ];

            // Set tandempartner values from POST data
            if (isset($POST['Tandempartner'])) {
                foreach ($tandempartnerFields as $field) {
                    if (isset($POST['Tandempartner'][$field])) {
                        $tandempartnerModel->$field = $POST['Tandempartner'][$field];
                    }
                }
            }

            // Validate and save the tandempartner data
            if ($tandempartnerModel->validate()) {
                if ($tandempartnerModel->save()) {
                    Yii::info('Tandempartner data saved successfully for user ' . $benutzer_id . ' with role ' . $rolle, 'profil');
                    return true;
                } else {
                    // Log save errors for debugging
                    Yii::error('Tandempartner save failed: ' . json_encode($tandempartnerModel->errors), 'profil');
                    return false;
                }
            } else {
                // Log validation errors for debugging
                Yii::error('Tandempartner validation failed: ' . json_encode($tandempartnerModel->errors), 'profil');
                return false;
            }

        } catch (\Exception $e) {
            // Log the exception for debugging
            Yii::error('Exception in saveTandempartnerData: ' . $e->getMessage());
            return false;
        }
    }

}
