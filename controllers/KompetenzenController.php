<?php

namespace app\controllers;

use app\models\Kompetenzen;
use app\models\KompetenzenSearch;
use PHPUnit\Framework\Constraint\IsEmpty;
use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\models\Benutzer;

class KompetenzenController extends Controller
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::class,
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all Kompetenzen models.
     * @return mixed
     */
    public function actionIndex()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $Benutzer = Benutzer::findOne(Yii::$app->user->id);

        $GET = Yii::$app->request->get();
        if (!isset($GET['rolle'])) {
            return $this->render('/site/index');
        }
        else {
            $rolle = $GET['rolle'];
        }

        $model = Kompetenzen::findOne(['benutzer_id' => Yii::$app->user->getIdentity()->getId()]);
        if ($model === null) {
            $model = new Kompetenzen(['benutzer_id' => Yii::$app->user->getIdentity()->getId(), 'rolle' => $rolle]);
        }
        // Jeder Benutzer hat nur EINE Rolle (mentee, mentor ODER menteementor)
        // Check if $rolle is the same as in DB:
        else {
            if ($model->rolle != $rolle) {
                $model->rolle = $rolle;
                $model->save();
            }
        }

        if ($rolle === 'mentee') {
            $Benutzer->addRolle(3);
//            if ($model->rolle != 'mentee') { // Benutzer hat andere Rolle gewählt
//                echo "Sie haben sich bereits als '".$model[0]['rolle']."' angemeldet und können keine andere Rolle wählen";
//                exit;
//            };
            return $this->render('index', [
                'model' => $model,
                'rolle' => $rolle,
                'todo' => 'chooseWunsch'
            ]);
        }
        elseif ($rolle === 'mentor') {
            $Benutzer->addRolle(4);
//            if ($model->rolle != 'mentor') { // Benutzer hat andere Rolle gewählt
//                echo "Sie haben sich bereits als '".$model[0]['rolle']."' angemeldet und können keine andere Rolle wählen";
//                exit;
//            };
            return $this->render('index', [
                'model' => $model,
                'rolle' => $rolle,
                'todo' => 'chooseErfahrung'
            ]);
        }
        elseif ($rolle === 'menteementor')  {
            $Benutzer->addRolle(5);
//            if ($model->rolle != 'menteementor') { // Benutzer hat andere Rolle gewählt
//                echo "Sie haben sich bereits als '".$model[0]['rolle']."' angemeldet und können keine andere Rolle wählen";
//                exit;
//            };
            return $this->render('index', [
                'model' => $model,
                'rolle' => $rolle,
                'todo' => 'chooseBeides',
            ]);
        }
    }

    /**
     * Displays a single Kompetenzen model.
     * @param int $id ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
//    public function actionView($id)
//    {
//        if (Yii::$app->user->isGuest) {
//            return $this->goHome();
//        }
//        return $this->render('update', [
//            'model' => $this->findModel($id),
//			'readonly' => true
//        ]);
//    }

    /**
     * Creates a new Kompetenzen model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
		return $this->actionUpdate(0);
    }

    /**
     * Updates an existing Kompetenzen model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $id ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */


    public function actionUpdatementeementor()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }
        $POST = Yii::$app->request->post();
        if(!empty($POST["Kompetenzen"]['benutzer_id'])) {
            $model= new Kompetenzen();
            $this->view->title = 'Es wurden zum ersten Mal Kompetenzen angegeben';
        } else {
            $model = Kompetenzen::findOne(['benutzer_id' => $POST["Kompetenzen"]['benutzer_id'], 'rolle' => $POST["Kompetenzen"]['rolle']]);
        }

        $rolle = $POST["Kompetenzen"]['rolle'];
//        $this->view->params['breadcrumbs'][] = ['label' => 'Kompetenzens', 'url' => ['index']];
//        $this->view->params['breadcrumbs'][] = $this->view->title;

        if (isset($POST["Kompetenzen"]['benutzer_id'])) {
            if ($model->load($POST) && $save = $model->validate()) {
                $sql = "SELECT id FROM `kompetenzen` WHERE rolle = '" . $rolle . "' AND benutzer_id = " . Yii::$app->user->getIdentity()->getId();
                $result = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($result);exit;
                if (!isset($result[0]["id"])) {
                    $save = $model->save();
                    return $this->redirect(['/profil/index',
                        'rolle' => $rolle]);
                } else {
//                        $save = $model->update(); // 14.04.2025: David Rieß: "update()" should have worked, but it didn't
                    $sql = "UPDATE `kompetenzen`
                                   SET `rolle`              = '". $_POST['Kompetenzen']['rolle'] . "', 
                                       `benutzer_id`        = ". $_POST['Kompetenzen']['benutzer_id'] . ", 
                                       `internes_netzwerk`  = ". $_POST['Kompetenzen']['internes_netzwerk'] . ", 
                                       `ki`                 = " . $_POST['Kompetenzen']['ki'] . ",
                                       `ndr_knowhow`        = " . $_POST['Kompetenzen']['ndr_knowhow'] . ", 
                                       `externe_erfahrung`  = ". $_POST['Kompetenzen']['externe_erfahrung'] . ", 
                                       `digitalisierung`    = ". $_POST['Kompetenzen']['digitalisierung'] . ", 
                                       `externes_netzwerk`  = " . $_POST['Kompetenzen']['externes_netzwerk'] . ", 
                                       `socialmedia`        = ". $_POST['Kompetenzen']['socialmedia'] . ", 
                                       `moderation`         = ". $_POST['Kompetenzen']['moderation'] . ",
                                       `worklifebalance`    = " . $_POST['Kompetenzen']['worklifebalance'] . ", 
                                       `präsentationen`     = ". $_POST['Kompetenzen']['präsentationen'] . ", 
                                       `resilienz`          = ". $_POST['Kompetenzen']['resilienz'] . ",
                                       `gender_diversity`   = " . $_POST['Kompetenzen']['gender_diversity'] . ",                                       
                                       `wunsch_internes_netzwerk`  = ". $_POST['Kompetenzen']['wunsch_internes_netzwerk'] . ", 
                                       `wunsch_ki`                 = " . $_POST['Kompetenzen']['wunsch_ki'] . ",
                                       `wunsch_ndr_knowhow`        = " . $_POST['Kompetenzen']['wunsch_ndr_knowhow'] . ", 
                                       `wunsch_externe_erfahrung`  = ". $_POST['Kompetenzen']['wunsch_externe_erfahrung'] . ", 
                                       `wunsch_digitalisierung`    = ". $_POST['Kompetenzen']['wunsch_digitalisierung'] . ", 
                                       `wunsch_externes_netzwerk`  = " . $_POST['Kompetenzen']['wunsch_externes_netzwerk'] . ", 
                                       `wunsch_socialmedia`        = ". $_POST['Kompetenzen']['wunsch_socialmedia'] . ", 
                                       `wunsch_moderation`         = ". $_POST['Kompetenzen']['wunsch_moderation'] . ",
                                       `wunsch_worklifebalance`    = " . $_POST['Kompetenzen']['wunsch_worklifebalance'] . ", 
                                       `wunsch_präsentationen`     = ". $_POST['Kompetenzen']['wunsch_präsentationen'] . ", 
                                       `wunsch_resilienz`          = ". $_POST['Kompetenzen']['wunsch_resilienz'] . ",
                                       `wunsch_gender_diversity`   = " . $_POST['Kompetenzen']['wunsch_gender_diversity'] . "
                                  WHERE `id` = " . $result[0]["id"];
//echo $sql;
                    $update = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($save); exit
                    return $this->redirect(['/profil/index',
                        'rolle' => $rolle]);
                }
            }
            else {
                throw new NotFoundHttpException('Fehler beim Update der Kompetenzen.');
            }
        }
        else {
            throw new NotFoundHttpException('Fehler beim Update der Kompetenzen.');
        }

    }


    public function actionUpdate()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }
        $POST = Yii::$app->request->post();
        if(!empty($POST["Kompetenzen"]['benutzer_id'])) {
            $model= new Kompetenzen();
            $this->view->title = 'Es wurden zum ersten Mal Kompetenzen angegeben';
        } else {
            $model = Kompetenzen::findOne(['benutzer_id' => $POST["Kompetenzen"]['benutzer_id'], 'rolle' => $POST["Kompetenzen"]['rolle']]);
        }

        $rolle = $POST["Kompetenzen"]['rolle'];
//        $this->view->params['breadcrumbs'][] = ['label' => 'Kompetenzens', 'url' => ['index']];
//        $this->view->params['breadcrumbs'][] = $this->view->title;

        if (isset($POST["Kompetenzen"]['benutzer_id'])) {
                if ($model->load($POST) && $save = $model->validate()) {
                    $sql = "SELECT id FROM `kompetenzen` WHERE rolle = '" . $rolle . "' AND benutzer_id = " . Yii::$app->user->getIdentity()->getId();
                    $result = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($result);exit;
                    if (!isset($result[0]["id"])) {
                        $save = $model->save();
                        return $this->redirect(['/profil/index',
                            'rolle' => $rolle]);
                    } else {
//                        $save = $model->update(); // 14.04.2025: David Rieß: "update()" should have worked, but it didn't
                        $sql = "UPDATE `kompetenzen`
                                   SET `rolle`              = '". $_POST['Kompetenzen']['rolle'] . "', `benutzer_id`                = ". $_POST['Kompetenzen']['benutzer_id'] . ", `internes_netzwerk`     = ". $_POST['Kompetenzen']['internes_netzwerk'] . ", `ki` = " . $_POST['Kompetenzen']['ki'] . ",
                                       `ndr_knowhow`        = " . $_POST['Kompetenzen']['ndr_knowhow'] . ", `externe_erfahrung`     = ". $_POST['Kompetenzen']['externe_erfahrung'] . ", `digitalisierung` = ". $_POST['Kompetenzen']['digitalisierung'] . ", 
                                       `externes_netzwerk`  = " . $_POST['Kompetenzen']['externes_netzwerk'] . ", `socialmedia`     = ". $_POST['Kompetenzen']['socialmedia'] . ", `moderation`            = ". $_POST['Kompetenzen']['moderation'] . ",
                                       `worklifebalance`    = " . $_POST['Kompetenzen']['worklifebalance'] . ", `präsentationen`    = ". $_POST['Kompetenzen']['präsentationen'] . ", `resilienz`          = ". $_POST['Kompetenzen']['resilienz'] . ",
                                       `gender_diversity`   = " . $_POST['Kompetenzen']['gender_diversity'] . "
                                  WHERE `id` = " . $result[0]["id"];
//echo $sql;
                        $update = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($save); exit
                        return $this->redirect(['/profil/index',
                                'rolle' => $rolle]);
                    }
                }
                else {
                    throw new NotFoundHttpException('Fehler beim Update der Kompetenzen.');
                }
        }
        else {
            throw new NotFoundHttpException('Fehler beim Update der Kompetenzen.');
        }

    }


    /**
     * Deletes an existing Kompetenzen model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $id ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Kompetenzen model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Kompetenzen the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Kompetenzen::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
