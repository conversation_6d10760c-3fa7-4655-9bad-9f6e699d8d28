<?php

namespace app\controllers;

use Yii;
use app\models\LabelManager;
use app\models\LabelManagerSearch;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;

/**
 * LabelManagerController implements the CRUD actions for LabelManager model.
 */
class LabelManagerController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => Yii::$app->user->isAdmin,
                        'roles' => ['@'],
                    ],
                ],
            ],
        ];
    }

    /**
     * Lists all LabelManager models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new LabelManagerSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);
        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Finds the LabelManager model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param integer $id
     *
     * @return LabelManager the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = LabelManager::findOne($id)) !== null) {
            return $model;
        }
        throw new NotFoundHttpException('The requested page does not exist.');
    }

    /**
     * Anlegen und update in einer Funktion
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id = 0)
    {
        if (!empty($id)) {
            $model = $this->findModel($id);
            $this->view->title = 'Update des Labels  "' . $model->value . '"';
        } else {
            $model = new LabelManager();
            $this->view->title = 'Anlage neuer Listenwerte';
        }
        $this->view->params['breadcrumbs'][] = ['label' => 'Labelübersicht', 'url' => ['index']];
        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['update', 'id' => $model->id]);
        }
        return $this->render('update', [
            'model' => $model,
            'id' => $model->id,
        ]);
    }

    /**
     * Deletes an existing LabelManager model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();
        return $this->redirect(['index']);
    }

    /**
     * Liefert die Liste aller values als HTML Table
     *
     * @return false|string
     */
    public function actionListe()
    {
        $liste = Yii::$app->request->post("liste");
        if (empty($liste)) {
            $html = $this->renderPartial("emptylist");
            $title = "Keine Liste gewählt";
        } else {
            $dataProvider = new ActiveDataProvider([
                "query" => LabelManager::find()->where(["liste" => $liste])->orderBy(['deprecated' => SORT_ASC, 'sortorder' => SORT_ASC, 'label' => SORT_ASC]),
                'pagination' => false,
                'sort' => false,
            ]);
            $html = $this->renderPartial("listview", ['dataProvider' => $dataProvider]);
            $title = "Einträge in der Liste " . $liste;
        }
        return json_encode(['err' => 0, 'html' => $html, 'title' => $title]);
    }
}
