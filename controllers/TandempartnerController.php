<?php

namespace app\controllers;

use app\models\Benutzer;
use Yii;
use app\models\Tandempartner;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

use app\models\KompetenzenSearch;


/**
 * TandempartnerController implements the CRUD actions for Tandempartner model.
 */
class TandempartnerController extends Controller
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all Tandempartner models.
     *
     * @return string
     */
    public function actionIndex($rolle)
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $GET = Yii::$app->request->get();
        $rolle = $GET['rolle'];
        
        if (strlen($rolle) < 4) {
            return $this->render('/site/index', [
            ]);
        }

        $model = Tandempartner::findOne(['benutzer_id' => Yii::$app->user->getIdentity()->getId()]);
        if (empty($model)) {
            $model = new Tandempartner(['benutzer_id' => Yii::$app->user->getIdentity()->getId(), 'rolle' => $rolle]);
        }
        else {
            if ($model->rolle != $rolle) {
                $model->rolle = $rolle;
                $model->save();
            }
        }
//echo "<pre>";var_dump($model);exit;

        return $this->render('index', [
            'model' => $model,
            'rolle' => $rolle,
            'todo' => 'chooseBeides',
        ]);

        var_dump($model); echo "rolle:".$rolle;
    }

    /**
     * Displays a single Tandempartner model.
     * @param int $id ID
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Tandempartner model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new Tandempartner();

        if ($this->request->isPost) {
            if ($model->load($this->request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    public function actionUpdate()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $POST = Yii::$app->request->post();
        if (empty($POST)) {
            return $this->redirect(['/site/index']);
        }
//var_dump($POST);exit;
        if(!empty($POST["Tandempartner"]['benutzer_id'])) {
            $model= new Tandempartner();
            $this->view->title = 'Es wurden zum ersten Mal Tandempartner angegeben';
        } else {
            $model = Tandempartner::findOne(['benutzer_id' => $POST["Tandempartner"]['benutzer_id'], 'rolle' => $POST["Tandempartner"]['rolle']]);
        }

        $rolle = $POST["Tandempartner"]['rolle'];
//        $this->view->params['breadcrumbs'][] = ['label' => 'Tandempartners', 'url' => ['index']];
//        $this->view->params['breadcrumbs'][] = $this->view->title;

        if (isset($POST["Tandempartner"]['benutzer_id'])) {
            if ($model->load($POST) ) { //&& $save = $model->validate() 24.4.2025 Validating had problem with column "treffen_art"
                $sql = "SELECT id FROM `tandempartner` WHERE rolle = '" . $rolle . "' AND benutzer_id = " . Yii::$app->user->getIdentity()->getId();
                $result = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($result);exit;
                if (!isset($result[0]["id"])) {
                    $save = $model->save();

                    return $this->redirect(['/matching/indexscreen',
                        'rolle' => $rolle]);
                } else {
//                  $save = $model->update(); // 14.04.2025: David Rieß: "update()" should have worked, but it didn't
                    $sql = "UPDATE `tandempartner`
                               SET `rolle`          = '". $rolle . "',                                  `benutzer_id`      = ". Yii::$app->user->getIdentity()->getId(). ",         `generation`    = '". $_POST['Tandempartner']['generation'] . "', `geschlecht` = '" . $_POST['Tandempartner']['geschlecht'] . "',
                                   `standort`       = '". $_POST['Tandempartner']['standort'] . "',     `tätigkeitsfeld`   = '" . $_POST['Tandempartner']['tätigkeitsfeld'] . "',   `führungskraft` = '" . $_POST['Tandempartner']['führungskraft'] . "', 
                                   `treffen_art`    = '". $_POST['Tandempartner']['treffen_art'] . "',  `treffen_frequenz` = '" . $_POST['Tandempartner']['treffen_frequenz'] . "'
                             WHERE `id` = " . $result[0]["id"];
                    $update = Yii::$app->db->createCommand($sql)->queryAll();

                    return $this->redirect(['/matching/indexscreen',
                        'rolle' => $rolle]);
                }
            }
            else {
//var_dump($save);
                throw new NotFoundHttpException('Fehler beim Update des Tandempartners1.');
            }
        }
        else {
            throw new NotFoundHttpException('Fehler beim Update des Tandempartners2.');
        }

    }

    /**
     * Deletes an existing Tandempartner model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $id ID
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Tandempartner model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Tandempartner the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Tandempartner::findOne(['id' => $id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
