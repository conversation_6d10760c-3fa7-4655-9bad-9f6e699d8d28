<?php

namespace app\controllers;

use app\models\Benutzer;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\Response;
use yii\filters\VerbFilter;

class SiteController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'only' => ['logout'],
                'rules' => [
                    [
                        'actions' => ['logout'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function actions()
    {
    }

    /**
     * Displays homepage.
     *
     * @return string
     */
    public function actionIndex()
    {

        $model = new Benutzer();
        return $this->render('index', ['model' => $model]);
    }

    /**
     * Login action.
     *
     * @return Response|string
     */
    public function actionLogin()
    {
        $POST = Yii::$app->request->post();

        $model = new Benutzer();

        if (!empty($POST) && $model->load($POST)) {
            if ($model->LDAPlogin(Yii::$app->request->post('rememberme') == 1)) {
                return $this->render('index', [
                    'model' => $model,
                ]);
            }
        }

        return $this->render('index', [
            'model' => $model,
        ]);
    }

    public function actionLoginlocal()
    {
        $POST = Yii::$app->request->post();

        if (!Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $model = new Benutzer();

        if (!empty($POST) && $model->load($POST)) {
            if ($model->login(Yii::$app->request->post('rememberme') == 1)) {
                return $this->render('index', [
                    'model' => $model,
                ]);
            }
        }

        return $this->render('loginlocal', [
            'model' => $model,
        ]);
    }

    /**
     * Logout action.
     *
     * @return Response
     */
    public function actionLogout()
    {
        Yii::$app->user->logout();

        return $this->goHome();
    }

    public function actionAsk()
    {
        return $this->render("ask");
    }

    public function actionError($message = null)
    {
        return $this->render('error', ['message' => $message]);
    }


    public function actionRegister()
    {
        $model = new Benutzer();
        return $this->render('register', ['model' => $model,
            'rolle' => 'mentee']);
    }
}
