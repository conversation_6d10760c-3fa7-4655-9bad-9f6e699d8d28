<?php

namespace app\controllers;

use app\models\Benutzer;
use Yii;
use app\models\Partner_Profil;
use yii\data\ActiveDataProvider;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

use app\models\KompetenzenSearch;


/**
 * Partner_ProfilController implements the CRUD actions for Partner_Profil model.
 */
class Partner_ProfilController extends Controller
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::className(),
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /**
     * Lists all Partner_Profil models.
     *
     * @return string
     */
    public function actionIndex($rolle)
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }
        if (strlen($rolle) < 4) {
            return $this->render('/site/index', [
            ]);
        }

        $model = Partner_Profil::findOne(['benutzer_id' => Yii::$app->user->getIdentity()->getId(), 'rolle' => $rolle]);
        if (empty($model)) {
            $model = new Partner_Profil(['benutzer_id' => Yii::$app->user->getIdentity()->getId(), 'rolle' => $rolle]);
        }
//echo "<pre>";var_dump($model);exit;

        return $this->render('index', [
                'model' => $model,
                'rolle' => $rolle,
                'todo' => 'chooseBeides',
            ]);

        var_dump($model); echo "rolle:".$rolle;
    }

    /**
     * Displays a single Partner_Profil model.
     * @param int $id ID
     * @return string
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Partner_Profil model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return string|\yii\web\Response
     */
    public function actionCreate()
    {
        $model = new Partner_Profil();

        if ($this->request->isPost) {
            if ($model->load($this->request->post()) && $model->save()) {
                return $this->redirect(['view', 'id' => $model->id]);
            }
        } else {
            $model->loadDefaultValues();
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Partner_Profil model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $id ID
     * @return string|\yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
//    public function actionUpdate($id)
//    {
//        $model = $this->findModel($id);
//
//        if ($this->request->isPost && $model->load($this->request->post()) && $model->save()) {
//            return $this->redirect(['view', 'id' => $model->id]);
//        }
//
//        return $this->render('update', [
//            'model' => $model,
//        ]);
//    }

    public function actionUpdate()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $POST = Yii::$app->request->post();
        if (empty($POST)) {
            return $this->redirect(['/site/index']);
        }
//var_dump($POST);exit;
        if(!empty($POST["Partner_Profil"]['benutzer_id'])) {
            $model= new Partner_Profil();
            $this->view->title = 'Es wurden zum ersten Mal Partner_Profil angegeben';
        } else {
            $model = Partner_Profil::findOne(['benutzer_id' => $POST["Partner_Profil"]['benutzer_id'], 'rolle' => $POST["Partner_Profil"]['rolle']]);
        }

        $rolle = $POST["Partner_Profil"]['rolle'];
//        $this->view->params['breadcrumbs'][] = ['label' => 'Partner_Profils', 'url' => ['index']];
//        $this->view->params['breadcrumbs'][] = $this->view->title;

        if (isset($POST["Partner_Profil"]['benutzer_id'])) {
            if ($model->load($POST) && $save = $model->validate()) {
                $sql = "SELECT id FROM `Partner_Profil` WHERE rolle = '" . $rolle . "' AND benutzer_id = " . Yii::$app->user->getIdentity()->getId();
                $result = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($result);exit;
                if (!isset($result[0]["id"])) {
                    $save = $model->save();
                    return $this->redirect(['/partnerprofil/index',
                        'rolle' => $rolle]);
                } else {
//                  $save = $model->update(); // 14.04.2025: David Rieß: "update()" should have worked, but it didn't
                    $sql = "UPDATE `partnerprofil`
                               SET `rolle`              = '". $rolle . "', `benutzer_id`                = ". Yii::$app->user->getIdentity()->getId(). ", `generation`     = '". $_POST['Partner_Profil']['generation'] . "', `geschlecht` = '" . $_POST['Partner_Profil']['geschlecht'] . "',
                                    `standort` = '". $_POST['Partner_Profil']['standort'] . "', `tätigkeitsfeld` = '" . $_POST['Partner_Profil']['tätigkeitsfeld'] . "', `führungskraft`        = '" . $_POST['Partner_Profil']['führungskraft'] . "', 
                                    `tätigkeitsfeld` = '" . $_POST['Partner_Profil']['tätigkeitsfeld'] . "', `führungskraft`        = '" . $_POST['Partner_Profil']['führungskraft'] . "'
                             WHERE `id` = " . $result[0]["id"];
//echo $sql;
                    $update = Yii::$app->db->createCommand($sql)->queryAll();
//var_dump($save); exit
                    return $this->redirect(['/partnerprofil/index',
                        'rolle' => $rolle]);
                }
            }
            else {
                throw new NotFoundHttpException('Fehler beim Update des Partner_Profils1.');
            }
        }
        else {
            throw new NotFoundHttpException('Fehler beim Update des Partner_Profils2.');
        }

    }

    /**
     * Deletes an existing Partner_Profil model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param int $id ID
     * @return \yii\web\Response
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Partner_Profil model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Partner_Profil the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Partner_Profil::findOne(['id' => $id])) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
