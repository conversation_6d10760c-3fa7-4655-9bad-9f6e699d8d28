<?php

namespace app\controllers;

use app\models\Rolle;
use app\models\RolleBenutzer;
use app\models\Tandempartner;
use app\models\Profil;
use app\models\Kompetenzen;
use app\models\TandempartnerSearch;
use app\models\KompetenzenSearch;
use app\models\ProfilSearch;
use PHPUnit\Framework\Constraint\IsEmpty;
use Yii;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use app\models\Benutzer;
use app\controllers\LdapController;

class MatchingController extends Controller
{
    /**
     * @inheritDoc
     */
    public function behaviors()
    {
        return array_merge(
            parent::behaviors(),
            [
                'verbs' => [
                    'class' => VerbFilter::class,
                    'actions' => [
                        'delete' => ['POST'],
                    ],
                ],
            ]
        );
    }

    /* just for displaying the image */
    public function actionIndexscreen()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $GET = Yii::$app->request->get();

        if (!isset($GET['rolle'])) {
            return $this->render('/site/index');
        }
        else {
            $rolle = $GET['rolle'];
        }

        return $this->render('indexscreen', [
            'rolle' => $rolle,
            'todo' => 'chooseBeides',
        ]);
    }

    /**
     * Lists all Tandempartner models.
     * @return mixed
     */
    public function actionIndex()
    {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        $benutzer = Benutzer::findOne(Yii::$app->user->id);
        $rolle_id = RolleBenutzer::findOne(['benutzer_id' => Yii::$app->user->id]);
        $Rolle = Rolle::findOne(['id' => $rolle_id['rolle_id']]);
        $rolle = strtolower($Rolle->rolle);

        // MATCHING:
        // Es werden für das PROFIL des eingeloggten anhand der Werte IN TANDEMPARTNER ("Mein Tandempartner sollte...")  passende andere PROFILE gesucht
        // Es muss zusätzlich EINE "WunschKompetenz" (aus Tabelle KOMPETENZEN) mit den Kompetenzen des möglichen Partners übereinstimmen UND
        // ALLES aus TANDEMPARTNER (außer 'egal') muss in PROFIL vorhanden sein
        // KOMPETENZEN: Eigene KOMPETENZEN werden in allen anderen KOMPETENZEN gesucht
        if ($rolle === "Mentee" OR $rolle === "mentee") {
            $searchrolle = "mentor"; // Mentor soll Erfahrungskompetenzen haben, die der Mentee als Wunschkompetenzen angibt
        }
        elseif ($rolle === "Mentor" OR $rolle === "mentor") {
            $searchrolle = "mentee";
        }
        elseif ($rolle === 'menteementor') {
            //@Todo implement 'mentormentee'
            $searchrolle = "menteementor";
            sleep(0);
        }
        // EIGENES Profil des eingeloggten Benutzers:
        $profil = Profil::findOne(['benutzer_id' => Yii::$app->user->id, 'rolle' => $rolle]);

        // Eigenschaften, die der Wunschkandidat haben MUSS
        $tandempartner = Tandempartner::findOne(['benutzer_id' => Yii::$app->user->id, 'rolle' => $rolle]);
        if (!isset($tandempartner->generation)) {
                return $this->render('no_matching_results', [
                ]);
        }
//        $kompetenzen_logged_in_user = Kompetenzen::findOne(['benutzer_id' => Yii::$app->user->id, 'rolle' => $rolle]);
        $kompetenzen = new Kompetenzen();
        $kompetenzen_logged_in_user = $kompetenzen->getKompetenzen( Yii::$app->user->id, $rolle);

//echo "<pre>";var_dump($kompetenzen_logged_in_user); exit;

        $searchModel = new ProfilSearch();
        $dataProvider = $searchModel->search($tandempartner, $kompetenzen_logged_in_user, $searchrolle);
//echo "<pre>";var_dump($dataProvider); exit;
        if ($dataProvider != false) {
            $models = $dataProvider->getModels();
        }
        else {
            return $this->render('no_matching_results', [
            ]);
        }
        //echo "<pre>";var_dump($models); //exit;
        // Now count the number of matches for
        // 1. Kompetenzen and if the number of Kompetenzen is the same: the number
        // 2. Werte for sorting the found profiles
//        $komp
        $arr_count_matches = array();
        foreach ($models as $single_model) {
            $matches = 0;
//echo $single_model['benutzer_id'];
            foreach ($kompetenzen_logged_in_user as $key_komp => $item) {
                $single_model[$key_komp] === $item ? $matches++ : null;
//echo "cur_found:".$single_model[$key_komp]."///".$item;
//echo "<br />";
//                echo $logged_in.'/'.$match."<br />";
//                if ()
            }
            $arr_count_matches[$single_model['benutzer_id']] = $matches;
        }

        arsort($arr_count_matches, SORT_REGULAR);
//var_dump($arr_count_matches); echo "<br />";
        $sorted_models = array();
        foreach($arr_count_matches as $key => $v) {
//            echo "id:".$key."<br />";
            foreach ($models as $single_model) {
                if ($single_model['benutzer_id'] == $key AND $single_model['id'] > 0) {
                    $sorted_models[] = $single_model;
                }
            }

        }
//        echo "<pre>";var_dump($sorted_models);
//        exit;
//echo "<pre>";var_dump($models); exit;

        Yii::$app->session->set("returnAction", "matching/index");

        // Show MS365 Pictures of NDR employees:
        // Examples in: "SIV AD Check" (Arzu Köhrmann)

        if ($dataProvider->getTotalCount() > 0) {
            return $this->render('index', [
                'rolle' => $rolle,
                'searchrolle' => $searchrolle,
                'searchModel' => $searchModel,
                'dataProvider' => $dataProvider,
                'benutzer' => $benutzer,
                'hasfilter' => 0,
                'model' => $sorted_models,
                'params' => [
                    'page' => isset($_GET['page']) ? $_GET['page'] : 1,
                ]
            ]);

        }
        else {
            return $this->render('no_matching_results', [
            ]);
        }
    }

    public function actionProfildetails($id) {
        if (Yii::$app->user->isGuest) {
            return $this->goHome();
        }

        if (!$id) {
            return $this->goHome();
        }

//        $Benutzer = Benutzer::findOne(Yii::$app->user->id);
//        $rolle_id = RolleBenutzer::findOne(['benutzer_id' => Yii::$app->user->id]);
//        $Rolle = Rolle::findOne(['id' => $rolle_id['rolle_id']]);
//        $rolle = strtolower($Rolle->rolle);
        $profil = Profil::findOne(['benutzer_id' => $id]);
        $kompetenzen = Kompetenzen::findOne(['benutzer_id' => $id]);
        $tandempartner = Tandempartner::findOne(['benutzer_id' => $id]);

        return $this->render('profil_details', [
            'model' => $profil,
            'kompetenzen' => $kompetenzen,
            'tandempartner' => $tandempartner

            ]);
    }


    /**
     * Displays a single Tandempartner model.
     * @param int $id ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
//    public function actionView($id)
//    {
//        if (Yii::$app->user->isGuest) {
//            return $this->goHome();
//        }
//        return $this->render('update', [
//            'model' => $this->findModel($id),
//			'readonly' => true
//        ]);
//    }

    /**
     * Creates a new Tandempartner model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */

    /**
     * Displays a single BusinessView model.
     * @param int $id ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDetails($id)
    {
        Yii::$app->session->set("returnAction", "details");

        $profil = Profil::find()->where(['id' => $id])->one();
//        $this->view->title = $profil->;

        try {
            $model = $this->findModel($profil['id']);
        } catch (Exception $e) {

        }

        if ($model) {
            $Files = Anhang::findOne(['profil_id' => $id, 'gehoert_zu' => '']);
        } else {
            $model = new BusinessView();
            $model->profil_id = $id;
            $model->image_logo = "";
            $model->profil_name = "";
            $Files = null;
        }
        if (!$profil) {
            $Files = new Anhang();
        }
//        $tandempartner
//        var_dump($model);exit;
        return $this->render('details', [
            'model' => $model,
//            '$tandempartner' => $tandempartner,
            'profil' => $profil,
            'readonly' => true,
            'Files' => $Files

        ]);
    }

    public function actionCreate()
    {
        return $this->actionUpdate(0);
    }

    /**
     * Updates an existing Tandempartner model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param int $id ID
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
//    public function actionUpdate()
//    {
//        if (Yii::$app->user->isGuest) {
//            return $this->goHome();
//        }
//        $POST = Yii::$app->request->post();
//        if(!empty($POST["Tandempartner"]['benutzer_id'])) {
//            $model= new Tandempartner();
//            $this->view->title = 'Es wurden zum ersten Mal Tandempartner angegeben';
//        } else {
//            $model = Tandempartner::findOne(['benutzer_id' => $POST["Tandempartner"]['benutzer_id'], 'rolle' => $POST["Tandempartner"]['rolle']]);
//        }

//
//    }

    /**
     * Finds the Tandempartner model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id ID
     * @return Tandempartner the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Tandempartner::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }
}
