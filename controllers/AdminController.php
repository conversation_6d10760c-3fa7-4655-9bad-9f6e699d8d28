<?php

namespace app\controllers;

use Yii;
use yii\filters\AccessControl;
use yii\helpers\FileHelper;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\web\Response;

class AdminController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => false,
                        'roles' => ['?'],
                    ],
                    [
                        'actions' => ['index'],
                        'allow' => true,
                        'roles' => ['?'],
                    ],
                    [
                        'actions' => ['changelog', 'lastchange'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'matchCallback' => function () {
                            return Yii::$app->user->identity->isAdmin();
                        },
                    ],
                ],
            ],
        ];
    }

    public function actionChangelog()
    {
        echo "<pre>";
        passthru("git log");
        echo "</pre>";
    }

    public function actionChangelog_json()
    {
        exec("git log", $log);
        exec("git log -1 --format=%cd", $lastchange);
        exec("git branch | grep \*", $branch);
        return json_encode(['log' => $log, 'lastchange' => $lastchange, 'branch' => str_replace("* ", "", $branch)]);
    }

    public function actionLastchange()
    {
        echo "<pre>";
        passthru("git log -1 --format=%cd");
        echo "</pre>";
    }

    public function actionAutocomplete($field, $searchTerm)
    {
        /** ToDo für die einzelnen Projekte:
         *
         * Im Standard ist das eher Unsicher, man kann so zum beispiel alle Hashes aller Passworte auslesen wenn man möchte.
         * Sobald für die Action also jemand anderen als Admin berechtigt ist, sollte von der Anwendung sichergestellt werden,
         * dass nur auf bestimmte Felder zugergriffen werden kann
         *
         */

        list($model, $attribute) = explode("-", $field);
        if (strlen($model??"") < 4) {
            $model = strtoupper($model);
        }
        $modelname = 'app\models\\' . ucfirst($model);
        $instModel = new $modelname;
        $liste = $instModel::find()->select($attribute . ' as value')->where(['like', $attribute, $searchTerm])->distinct()->orderBy($attribute)->createCommand()->queryAll();
        Yii::$app->response->format = Response::FORMAT_JSON;
        return $liste;
    }

    public function actionInit()
    {
        $connection = Yii::$app->getDb();
        $connection->createCommand("SET foreign_key_checks = 0")->execute();
        $tables = $connection->schema->getTableNames();
        foreach ($tables as $table) {
            if (substr_count($table, "View") == 0) {
                $connection->createCommand()->dropTable($table)->execute();
            }
        }
        $connection->createCommand("SET foreign_key_checks = 1")->execute();
        Yii::$app->sql->parsefile("../sql/init.sql");
        echo Yii::$app->sql->log;
    }

    public function actionLogfile($file = '', $action = 'download')
    {
        $logdir = realpath(dirname(__FILE__) . '/../runtime/logs');
        if (!empty($file)) {
            if (file_exists($file)) {
                if ($action == 'download') {
                    Yii::$app->response->sendFile($file);
                } else if ($action == 'view') {
                    $content = file_get_contents($file);
                    return $this->render('logfileview', ['file' => $file, 'content' => $content]);
                }
            } else {
                throw new NotFoundHttpException('Datei nicht gefunden ' . $file);
            }
        }
        return $this->render('logfiles', ['files' => FileHelper::findFiles($logdir)]);
    }

    public function actionInfo()
    {
        return $this->render("gitinfo");
    }

}
