<?php

namespace app\controllers;

use app\common\components\ExcelHelper;
use app\models\LabelManager;
use app\models\Service;
use app\models\ServiceNdrteams;
use app\models\Standort;
use app\models\Produktionsmittel;
use app\models\ProduktionsmittelStandort;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Yii;
use yii\web\Controller;
use app\models\UploadFile;
use yii\web\UploadedFile;
use yii\filters\AccessControl;
use yii\filters\VerbFilter;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;

class ExcelController extends Controller
{

    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'delete' => ['post'],
                ],
            ],
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'allow' => false,
                        'roles' => ['?'],
                    ],
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'matchCallback' => function ($rule, $action) {
                            return Yii::$app->user->identity->isAdmin();
                        },
                    ],
                ],
            ],
        ];
    }

    public function actions()
    {
        return [
            'error' => [
                'class' => 'yii\web\ErrorAction',
            ],
        ];
    }

    public function actionTest()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('A1', 'Hello World !');

        $writer = new Xlsx($spreadsheet);
        header("Content-Type:   application/vnd.ms-excel; charset=utf-8");
        header("Content-Disposition: attachment; filename=abc.xlsx");  //File name extension was wrong
        header("Expires: 0");
        header("Cache-Control: must-revalidate, post-check=0, pre-check=0");
        header("Cache-Control: private", false);
        //header('Content-type: application/vnd.ms-excel');
        //header('Content-Disposition: attachment; filename="123' . $data['filename'] . '.xlsx"');
        $writer->save('php://output');
        exit;
        //echo "<a href='/test.xlsx'>klick</a>";
    }

    public function actionDownload($excelData)
    {
        return $this->_writeExcel($excelData);
    }

    function _writeExcel($data)
    {
        $objPHPExcel = new Spreadsheet();
        $objPHPExcel->getDefaultStyle()->getFont()->setName('Calibri');
        $objPHPExcel->getDefaultStyle()->getFont()->setSize(8);
        $objWriter = new Xlsx($objPHPExcel);

        $objSheet = $objPHPExcel->getActiveSheet();
        $objSheet->setTitle($data['filename']);

        if (!empty($data['rows']) && empty($data['columns'])) {
            $data['columns'] = array_keys($data['rows'][0]);
        }

        $r = 1;
        $c = 1;

        if (!empty($data['columns'])) {
            foreach ($data['columns'] as $value) {
                $cell = Coordinate::stringFromColumnIndex($c) . $r;
                $objSheet->getStyle($cell)->getFont()->setSize(10);
                $objSheet->setCellValue($cell, $value);
                $objSheet->getColumnDimension(Coordinate::stringFromColumnIndex($c))->setAutoSize(true);
                $c++;
            }
        }
        $r++;
        if (!empty($data['rows'])) {
            foreach ($data['rows'] as $cells) {
                $c = 1;
                foreach ($cells as $key => $value) {
                    $cell = Coordinate::stringFromColumnIndex($c) . $r;
                    $objPHPExcel->getActiveSheet()->setCellValue($cell, $value);
                    $c++;
                }
                $r++;
            }
        } else {
            $cell = Coordinate::stringFromColumnIndex(1) . $r;
            $objPHPExcel->getActiveSheet()->setCellValue($cell, "Keine Daten übergeben!");
        }
        header('Content-type: application/vnd.ms-excel');
        header('Content-Disposition: attachment; filename="' . $data['filename'] . '.xlsx"');
        $objWriter->save('php://output');
        exit;
    }

    public function actionDump($model, $typ = null)
    {
        $nodump = [
            'EditBenutzer_ID',
            'DeleteBenutzer_ID',
            'EditDatum',
            'DeleteDatum',
            'Deleted',
            'Inaktiv',
        ];

        $condition = [
            'and',
            ['Deleted' => 0],
        ];

        $filename = $model;
        if (!empty($model)) {
            $modelname = "app\models\\" . $model;
            $inst_model = new $modelname;
        } else {
            die("You shall not pass!");
        }

        $filename = $model;
        if (!empty($model)) {
            $modelname = "app\models\\" . $model;
            $inst_model = new $modelname;
        } else {
            die("You shall not pass!");
        }

        $rows = [];
        foreach ($inst_model->find()->where($condition)->all() as $single_model) {
            $row = [];
            foreach ($single_model->attributes as $key => $attr) {
                if (!in_array($key, $nodump) && substr_count($key, '_ID') == 0) {
                    $row[$key] = $attr;
                }
            }
            $rows[] = $row;
        }
        $this->_writeExcel(['filename' => $filename, 'rows' => $rows]);
    }

    public function actionUpload()
    {
        ini_set("memory_limit","2048M");
        ini_set("max_execution_time","600");
        $model = new UploadFile();
        $errors = [];

        if (Yii::$app->request->isPost) {
            $model->datei = UploadedFile::getInstance($model, 'datei');
            if ($uploadFile = $model->upload()) {
                $exceldata = ExcelHelper::_readExcel("../runtime/temp/" . $uploadFile);
                $cnt = 0;

                /**
                 * ToDo Verarbeiten der Excel Daten
                 */

                return $this->render('ready', ['data' => $exceldata, 'cnt' => $cnt, 'errors' => $errors]);
            }
        }

        return $this->render('upload', ['model' => $model]);
    }

    public function actionSqlfile()
    {
        $inputFileName = '../upload/tables.xls';
        require_once('../vendor/PHPExcel/Classes/PHPExcel.php');
        require_once('../vendor/PHPExcel/Classes/PHPExcel/IOFactory.php');

        try {
            $inputFileType = \PHPExcel_IOFactory::identify($inputFileName);
            $objReader = \PHPExcel_IOFactory::createReader($inputFileType);
            $objPHPExcel = $objReader->load($inputFileName);
        } catch (Exception $e) {
            die('Error loading file "' . pathinfo($inputFileName, PATHINFO_BASENAME) . '": ' . $e->getMessage());
        }

        $sheet = $objPHPExcel->getSheet(0);
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();

        $tables = [];

        for ($row = 2; $row <= $highestRow; $row++) {
            $data = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, null, true, false);
            $d = $data[0];
            if (!empty($d[0])) {
                if (empty($d[2]) && empty($d[3]) && empty($d[5]) && empty($d[6]) && empty($d[7]) && empty($d[8]) && empty($d[9]) && empty($d[10]) && empty($d[11])) {
                    if (!empty($table)) {
                        $tables[] = $table;
                    }
                    $table = ["name" => $d[0]];
                } else {
                    if (substr_count($d[0], "_ID") == 0) {
                        $table['fields'][] = ["name" => $d[0], "label" => $d[1], "type" => $d[5]];
                    } else {
                        $table['fields'][] = ["name" => $d[0], "label" => "FK " . str_replace("_ID", "", $d[0]), "type" => "fk", "fk_table" => str_replace("_ID", "", $d[0])];
                    }
                }
            }
        }

        /*        for ($row = 1; $row <= $highestRow; $row++) {
          $data = $sheet->rangeToArray('A' . $row . ':' . $highestColumn . $row, NULL, TRUE, FALSE);
          $d = $data[0];
          if (!empty($d[0])) {
          if (empty($d[1])) {
          if (!empty($table))
          $tables[] = $table;
          $table = Array("name" => $d[0]);
          } else {
          $table['fields'][] = Array("name" => $d[0] . $d[1], "type" => "fk", "fk_table" => $d[0]);
          }
          } else {
          if (!empty($d[1])) {
          $typ = "String";
          if (!empty($d[4]))
          $typ = $d[4];
          if (substr_count($d[1], 'Datensatz in dieser') == 0)
          $table['fields'][] = Array("name" => $d[1], "type" => $typ);
          }
          }
          }
         */
// wichtig, die letzte Tabelle auch übernehmen!
        if (!empty($table)) {
            $tables[] = $table;
        }
        $sql = "";

        if (!empty($tables)) {
            foreach ($tables as $table) {
                $sql .= "----------------------------------------- " . $table['name'];
                $sql .= "\n\nDROP TABLE IF EXISTS " . $table['name'] . ";\n";
                $sql .= "\nCREATE TABLE " . $table['name'] . " (";
                foreach ($table['fields'] as $field) {
                    $nullable = "";
                    $typ = "VARCHAR(255)";
                    if ($field['type'] == "ja/nein") {
                        $typ = "BOOLEAN";
                        $nullable = "";
                    }
                    if ($field['type'] == "fk") {
                        $typ = "INT";
                        $nullable = "";
                    }
                    if ($field['type'] == "Datum") {
                        $typ = "DATE";
                        $nullable = "";
                    }
                    if ($field['type'] == "Langtext") {
                        $typ = "VARCHAR(4000)";
                        $nullable = "";
                    }
                    if ($field['name'] == "ID") {
                        $typ = "INT";
                        $nullable = "NOT NULL AUTO_INCREMENT";
                    }
                    if (substr_count($field['type'], "Auswahl") > 0) {
                        $typ = "ENUM (" . trim(str_replace(["Auswahl:", "/"], ["", ","], $field['type'])) . ")";
                        $nullable = "";
                    }
                    if ($field['name'] == "Name") {
                        $field['name'] = $table['name'];
                    }
                    if ($field['type'] != "automatisch") {
                        $sql .= "\n\t" . $field['name'] . " " . $typ . " " . $nullable . " COMMENT '" . $field['label'] . "',";
                    }
                }
                $sql .= "\n\tPRIMARY KEY (ID) ";
                $sql .= "\n) ENGINE=InnoDB  DEFAULT CHARSET=utf8;";
//$sql.="\n\nALTER TABLE ".$table['name']." ADD PRIMARY KEY (ID);";
                $sql .= "\n\n\n";
            }
        }

        return $this->render('index', ['excel_content' => $data]);
    }

}
