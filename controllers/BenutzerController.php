<?php

namespace app\controllers;

use app\models\Konfiguration;
use Yii;
use app\models\Benutzer;
use app\models\Kompetenzen;
use app\models\BenutzerKompetenzen;
use app\models\Hobbies;
use app\models\BenutzerHobbies;
use app\models\Eigenschaften;
use app\models\BenutzerEigenschaften;
use app\models\Standort;
use app\models\BenutzerStandort;
use app\models\BenutzerGesuchterStandort;
use app\models\Taetigkeitsfeld;
use app\models\BenutzerTaetigkeit;
use app\models\BenutzerGesuchtesTaetigkeitsfeld;
use app\models\Generationen;
use app\models\BenutzerGeneration;
use app\models\BenutzerGesuchteGeneration;
use app\models\Fuehrungskraft;
use app\models\BenutzerGesuchteFuehrungskraft;
use app\models\Geschlecht;
use app\models\BenutzerGesuchtesGeschlecht;
use app\models\WieOft;
use app\models\BenutzerGesuchterWieOft;
use app\models\WieTreffen;
use app\models\BenutzerGesuchterWieTreffen;
use app\models\BenutzerSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;
use yii\filters\AccessControl;

/**
 * BenutzerController implements the CRUD actions for Benutzer model.
 */
class BenutzerController extends Controller
{
	/**
	 * @inheritdoc
	 */
	public function behaviors()
	{
		return [
			'access' => [
				'class' => AccessControl::class,
				'rules' => [
					[
						'actions' => ["password"],
						'allow' => true,
						'roles' => ['?'],
					],
					[
						'allow' => true,
						'roles' => ['@'],
						'matchCallback' => function ($rule, $action) {
							return Yii::$app->user->isAdmin || Yii::$app->user->hatRolle("Benutzeradmin");
						},
					],
				],
			],
			'verbs' => [
				'class' => VerbFilter::class,
				'actions' => [
					'delete' => ['POST'],
				],
			],
		];
	}

	/**
	 * Lists all Benutzer models.
	 *
	 * @return mixed
	 */
	public function actionIndex()
	{
		$searchModel = new BenutzerSearch();
		$dataProvider = $searchModel->search(Yii::$app->request->queryParams);

		return $this->render('index', [
			'searchModel' => $searchModel,
			'dataProvider' => $dataProvider,
		]);
	}

	/**
	 * Displays a single Benutzer model.
	 *
	 * @param integer $id
	 *
	 * @return mixed
	 * @throws NotFoundHttpException if the model cannot be found
	 */
	public function actionView($id)
	{
		return $this->render('view', [
			'model' => $this->findModel($id),
		]);
	}

	/**
	 * Creates a new Benutzer model.
	 * If creation is successful, the browser will be redirected to the 'view' page.
	 *
	 * @return mixed
	 */
	public function actionCreate()
	{
		return $this->actionUpdate(0);
	}

	public function actionCreateProfile() {
	Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
		try {

			if (Yii::$app->request->isPost) {
				$rawBody = Yii::$app->request->getRawBody();
				$data = json_decode($rawBody, true);

				$fuehrungskraft = $data['fuehrungskraft'] ?? null;
				
				if($fuehrungskraft === true) {
					$fuehrungskraft = 1;
				} else if($fuehrungskraft === false) {
					$fuehrungskraft = 0;
				}

				$geschlecht = $data['geschlecht'] ?? null;

				// if ($fuehrungskraft === null || empty($fuehrungskraft)) {
				// 	$fuehrungskraft = null;
				// } 

				if ($geschlecht === null || empty($geschlecht)) {
					$geschlecht = null;
				} 

				
				$errors = [];

				$benutzerId = Yii::$app->user->id;
				$model = Benutzer::find()->where(['id' => $benutzerId])->one();

				$model->fuehrungskraft = $fuehrungskraft;
				$model->geschlechtId = $geschlecht;

				if (!$model->save()) {
					$errors[] = $model->errors;
				}
			

				if (empty($errors)) {
					return [
						'success' => true,
						'message' => 'Benutzer wurden erfolgreich erstellt.',
						
					];
				} else {
					Yii::$app->response->statusCode = 400; 
					return [
						'success' => false,
						'message' => 'Fehler beim Speichern der Benutzer.',
						'errors' => $errors,
					];
				}
				
			} else {
				throw new \Exception('Ungültige Anfrage.');
			}
		} catch (\Exception $e) {
			return [
				'success' => false,
				'message' => 'Ein Fehler ist aufgetreten.',
				'error' => $e->getMessage(),
			];
		}
	}

	public function actionBenutzerGesuchteFuehrungskraft() {
		Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
		try {

			if (Yii::$app->request->isPost) {
				$rawBody = Yii::$app->request->getRawBody();
				$data = json_decode($rawBody, true);

				$fuehrungskraft = $data['fuehrungskraft'] ?? null;

				if ($fuehrungskraft === null || empty($fuehrungskraft)) {
					$fuehrungskraft = null;
				}

				if($fuehrungskraft === true) {
					$fuehrungskraft = 1;
				} else if($fuehrungskraft === false) {
					$fuehrungskraft = 0;
				} else {
					$fuehrungskraft = 2;
				}
				
				$errors = [];

				$benutzerId = Yii::$app->user->id;

				$model = new BenutzerGesuchteFuehrungskraft();
				$model->benutzerId = $benutzerId;
				$model->fuehrungskraftId = $fuehrungskraft;
				

				if (!$model->save()) {
					$errors[] = $model->errors;
				}
			

				if (empty($errors)) {
					return [
						'success' => true,
						'message' => 'Benutzer gesuchte führungskraft wurden erfolgreich erstellt.',
						
					];
				} else {
					Yii::$app->response->statusCode = 400; 
					return [
						'success' => false,
						'message' => 'Fehler beim Speichern der Benutzer gesuchte Führungskraft.',
						"fuehrungskraft" => $fuehrungskraft,
						'errors' => $errors,
					];
				}
				
			} else {
				throw new \Exception('Ungültige Anfrage.');
			}
		} catch (\Exception $e) {
			return [
				'success' => false,
				'message' => 'Ein Fehler ist aufgetreten.',
				'error' => $e->getMessage(),
			];
		}
	}

	public function actionBenutzerGesuchtesGeschlecht() {
		Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
		try {

			if (Yii::$app->request->isPost) {
				$rawBody = Yii::$app->request->getRawBody();
				$data = json_decode($rawBody, true);

				$geschlecht = $data['geschlecht'] ?? null;

				if ($geschlecht === null || empty($geschlecht)) {
					$geschlecht = null;
				} 
				
				$errors = [];

				$benutzerId = Yii::$app->user->id;
				$model = new BenutzerGesuchtesGeschlecht();
				$model->geschlechtId = $geschlecht;
				$model->benutzerId = $benutzerId;

				if (!$model->save()) {
					$errors[] = $model->errors;
				}
			

				if (empty($errors)) {
					return [
						'success' => true,
						'message' => 'Benutzer gesuchte führungskraft wurden erfolgreich erstellt.',
						
					];
				} else {
					Yii::$app->response->statusCode = 400; 
					return [
						'success' => false,
						'message' => 'Fehler beim Speichern der Benutzer.',
						'errors' => $errors,
					];
				}
				
			} else {
				throw new \Exception('Ungültige Anfrage.');
			}
		} catch (\Exception $e) {
			return [
				'success' => false,
				'message' => 'Ein Fehler ist aufgetreten.',
				'error' => $e->getMessage(),
			];
		}
	}

	

	/**
	 * Updates an existing Benutzer model.
	 * If update is successful, the browser will be redirected to the 'view' page.
	 *
	 * @param integer $id
	 *
	 * @return mixed
	 * @throws NotFoundHttpException if the model cannot be found
	 */
	public function actionUpdate($id)
	{

		if (empty($id)) {
			$model = new Benutzer();
			$this->view->title = "Neuer Benutzer";
		} else {
			$model = $this->findModel($id);
			$this->view->title = 'Benutzer ' . $model->fullname . " bearbeiten";
		}
		$this->view->params['breadcrumbs'][] = ['label' => 'Benutzerverwaltung', 'url' => ['index']];
		$this->view->params['breadcrumbs'][] = $this->view->title;
//        $mod = $model->load(Yii::$app->request->post());
//        var_dump($mod); exit;
//        $res = $model->save();
//        var_dump($res); exit;
		if ($model->load(Yii::$app->request->post()) && ($res = $model->save(false))) {
//echo "saveRollen";var_dump($_REQUEST['Benutzer']['rollenIds']);exit;
			$model->saveRollen($_REQUEST['Benutzer']['rollenIds']);
			return $this->redirect(["index"]);
		}
		return $this->render('update', [
			'model' => $model,
		]);
	}

	/**
	 * Löscht einen Nutzer sowie alle seiner zugeordneten Rollen.
	 *
	 * @param $id
	 *
	 * @return \yii\web\Response
	 * @throws NotFoundHttpException
	 * @throws \Throwable
	 * @throws \yii\db\StaleObjectException
	 */
	public function actionDelete($id)
	{
		$model = $this->findModel($id);
		if ($model->isAdmin()) {
			Yii::$app->session->addFlash("error", "Admins können nicht gelöscht werden!");
		} else {
			//löscht nun veraltete Verknüpfungen zwischen Benutzer und Rolle
			$this->findModel($id)->deleteAllRoles();
			$this->findModel($id)->delete();
			Yii::$app->session->addFlash("success", "Benutzer " . $model->displayname . " gelöscht");
		}

		return $this->redirect(['index']);
	}

	/**
	 * Finds the Benutzer model based on its primary key value.
	 * If the model is not found, a 404 HTTP exception will be thrown.
	 *
	 * @param integer $id
	 *
	 * @return Benutzer the loaded model
	 * @throws NotFoundHttpException if the model cannot be found
	 */
	protected function findModel($id)
	{
		if (($model = Benutzer::findOne($id)) !== null) {
			return $model;
		}

		throw new NotFoundHttpException('The requested page does not exist.');
	}

	public function actionPassword($id = 0, $newpwd = "")
	{
		$model = new Benutzer();
		$action = Yii::$app->request->post("action");
		if (!empty($id) && !empty($newpwd)) {
			$model = Benutzer::findOne($id);
			$model->passwort = $newpwd;
		} else if (!Yii::$app->user->isGuest) {
			$model->email = Yii::$app->user->identity->email;
		}
		switch ($action) {
			case "reset":
				if (Yii::$app->user->identity->isAdmin()) {
					$model = Benutzer::findOne(Yii::$app->request->post("id"));
					if ($model->resetPassword()) {
						Yii::$app->session->addFlash("success", "Passwort wurde zurückgesetzt, Mail wurde gesendet");
					} else {
						Yii::$app->session->addFlash("error", "Irgend etwas ist schief gelaufen <BR>" . $model->getErrorstring());
					}
					return $this->asJson(["err" => 0, "refresh" => true]);
				} else {
					die("Hacking attempt?!");
				}
				break;
			case "save":
				$dbmodel = Benutzer::findByUsername(Yii::$app->request->post("Benutzer")['email']);
				if (empty($dbmodel)) {
					$dbmodel->addError("email", "Benutzer existiert nicht");
				} else {
					$model = new Benutzer();
					$model->load(Yii::$app->request->post());
					if (empty($model->passwort)) {
						$model->addError("passwort", "Bitte das aktuelle Passwort eingeben");
					} else if (!$model->validatePassword($dbmodel->passwort)) {
						$model->addError("passwort", "Altes Passwort stimmt nicht");
					} else if (empty($model->passwort_neu1)) {
						$model->addError("passwort_neu1", "Bitte ein gültiges Passwort eingeben.");
					} else if ($model->passwort_neu1 != $model->passwort_neu2) {
						$model->addError("passwort_neu2", "Passworte stimmen nicht überein");
					} else {
						$dbmodel->setPassword($model->passwort_neu1);
						if ($dbmodel->save()) {
							Yii::$app->session->addFlash("success", "Neues Passwort wurde gespeichert");
						} else {
							Yii::$app->session->addFlash("danger", "Passwort kann nicht gespeichert werden");
						}
					}
				}
				break;
			default:

				break;
		}

		return $this->render("resetform", ["model" => $model]);
	}
}
