<?php

namespace app\controllers;

use Yii;
use app\models\Titles;
use app\models\TitlesSearch;
use yii\filters\AccessControl;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * TitlesController implements the CRUD actions for Titles model.
 */
class TitlesController extends Controller
{
    /**
     * {@inheritdoc}
     */
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'actions' => ['get'],
                        'allow' => true,
                        'roles' => ['?', '@'],
                    ],
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'matchCallback' => function ($rule, $action) {
                            return Yii::$app->user->hatRolle("Fachbereichadmin");
                        },
                    ],
                ],
            ],
        ];
    }


    /**
     * Lists all Titles models.
     *
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new TitlesSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single Titles model.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new Titles model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     *
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new Titles();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('create', [
            'model' => $model,
        ]);
    }

    /**
     * Updates an existing Titles model.
     * If update is successful, the browser will be redirected to the 'view' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        }

        return $this->render('update', [
            'model' => $model,
        ]);
    }

    /**
     * Deletes an existing Titles model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     *
     * @param integer $id
     *
     * @return mixed
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the Titles model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     *
     * @param integer $id
     *
     * @return Titles the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Titles::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('The requested page does not exist.');
    }

    public function actionGet()
    {
        $elementid = Yii::$app->request->post("elementid");
        $title = Titles::find()->where(["=", "elementid", $elementid])->one();
        if (empty($title)) {
            $label = "";
            $found = 0;
        } else {
            $label = $title->title;
            $found = 1;
        }
        return $this->asJson(["isAdmin" => Yii::$app->user->hatRolle("Fachbereichadmin") ? 1 : 0, "found" => $found, "title" => $label]);
    }

    public function actionSet()
    {
        $elementid = Yii::$app->request->post("elementid");
        $title = Titles::find()->where(["=", "elementid", $elementid])->one();
        if (empty($title)) {
            $title = new Titles();
        }
        $title->elementid = $elementid;
        $title->title = Yii::$app->request->post("title");
        $saved = $title->save();
        return $this->asJson(["isAdmin" => Yii::$app->user->hatRolle("Fachbereichadmin") ? 1 : 0, "err" => $saved ? 0 : 1, "title" => $title->title]);
    }
}
