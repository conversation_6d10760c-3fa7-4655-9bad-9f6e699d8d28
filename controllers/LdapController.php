<?php

namespace app\controllers;

use app\models\Benutzer;
use app\models\Domaene;
use Yii;
use yii\filters\AccessControl;
use yii\web\Controller;

class LdapController extends Controller
{
    public function behaviors()
    {
        return [
            'access' => [
                'class' => AccessControl::class,
                'rules' => [
                    [
                        'actions' => ['alive'],
                        'allow' => true,
                        'roles' => ['?'],
                    ],
                    [
                        'actions' => ['index', 'searchperson', 'myentry', 'test', 'saveperson'],
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                    [
                        'allow' => true,
                        'roles' => ['@'],
                        'matchCallback' => function ($rule, $action) {
                            return Yii::$app->user->identity->isAdmin();
                        },
                    ],
                ],
            ],
        ];
    }

    public function actionIndex()
    {
        return $this->actionAlive();
    }

    public function actionAlive()
    {
        if (!Yii::$app->ldap->bind_admin()) {
            echo "Fehler!";
            echo Yii::$app->ldap->dumpError();
        } else {
            echo "Erfolgreich verbunden";
        }
    }

    public function actionMyentry()
    {
        Yii::$app->ldap->bind_admin();
        $data = Yii::$app->ldap->search_account(Yii::$app->user->identity->Benutzername);
//        Yii::$app->debug->dump(Yii::$app->ldap->result);
        return "<pre>" . var_export(Yii::$app->ldap->resultArray, true) . "</pre>";
    }

    public function actionTest($email = "<EMAIL>")
    {
        Yii::$app->ldap->bind_admin();
        $data = Yii::$app->ldap->search_account($email);
        echo "<pre>" . var_export(Yii::$app->ldap->resultArray, true) . "</pre>";
        Yii::$app->ldap->dumpError();
    }

    public function actionSearchperson($term = null)
    {
        Yii::$app->ldap->bind_admin();
        if (empty($term)) {
            $term = Yii::$app->request->post('term');
        }
        if (!$data = Yii::$app->ldap->search_person($term)) {
            Yii::$app->ldap->dumpError();
        } else {
            $data = Yii::$app->ldap->resultArray;
            foreach ($data as $key => $person) {
                /*
                 * Nur Anwender anzeigen, keine Admin und Testaccounts!
                 *
                 */
                if (empty($data[$key]['mail']) || substr_count($key, 'OU=PERSONEN') == 0) {
                    unset($data[$key]);
                } else {
                    if (isset($data[$key]['displayname'])) {
                        $_array = explode(',', $data[$key]['displayname']??"");
                        !isset($_array[1]) ? $data[$key]['label'] = "$_array[0] auswählen" : $data[$key]['label'] = "$_array[1] $_array[0] auswählen";
                    }
                }
            }
            sort($data);
            return json_encode($data);
        }
    }

    public function actionDemo($term = null)
    {
        $data = [];
        $data[] = ['displayname' => 'Detlef Dingensbums', 'mail' => '<EMAIL>', 'label' => 'Detlef Dingens auswählen'];
        $data[] = ['displayname' => 'Peter Bumsflocke', 'mail' => '<EMAIL>', 'label' => 'Peter Dingens auswählen'];
        $data[] = ['displayname' => 'Paul Bumsflocke', 'mail' => '<EMAIL>', 'label' => 'Paul Dingens auswählen'];
        $data[] = ['displayname' => 'Paula Bumsflocke', 'mail' => '<EMAIL>', 'label' => 'Paula Dingens auswählen'];
        $data[] = ['displayname' => 'Sonja Superdinger', 'mail' => '<EMAIL>', 'label' => 'Sonja Dingens auswählen'];
        $data[] = ['displayname' => 'Sabine Sabbelkopp', 'mail' => '<EMAIL>', 'label' => 'Sabine Dingens auswählen'];
        $data[] = ['displayname' => 'Arne Alleswisser', 'mail' => '<EMAIL>', 'label' => 'Arne Dingens auswählen'];
        if (!empty($term)) {
            foreach ($data as $key => $person) {
                if (!substr_count(strtolower($person['displayname']), strtolower($term))) {
                    unset($data[$key]);
                }
            }
        }
        sort($data);
        return json_encode($data);
    }

    public function actionDemosave($term = null)
    {
        return json_encode(['errors' => [], 'id' => 666, 'msg' => 'Speichern erfolgt!', 'err' => 0]);
    }

    public function actionSaveperson()
    {
        $requestData = Yii::$app->request->post();

        if (!empty($requestData['employeeid'])) {
            $user = Benutzer::findByEmployeeid($requestData['employeeid']);
        }
        if (empty($user)) {
            $user = Benutzer::findByUsername($requestData['mail']);
        }
        if (empty($user)) {
            $user = Benutzer::findByUsername($requestData['samaccountname']);
        }
        if (empty($user)) {
            $user = new Benutzer;
        }
        $user->Benutzername = $requestData['samaccountname'];
        $user->Domaene_ID = empty(Domaene::ID($requestData['st'])) ? 0 : Domaene::ID($requestData['st']);
        $user->Vorname = !empty($requestData['givenname']) ? $requestData['givenname'] : '';
        $user->Nachname = !empty($requestData['sn']) ? $requestData['sn'] : '';
        $user->EMail = !empty($requestData['mail']) ? $requestData['mail'] : '';
        $user->employeeid = !empty($requestData['employeeid']) ? $requestData['employeeid'] : '';
        $user->setPassword(uniqid());

        if (!$user->save()) {
            return json_encode(['errors' => $user->errors, 'id' => $user->ID, 'msg' => 'Speichern fehlgeschlagen!', 'err' => 1]);
        }

        return json_encode(['errors' => [], 'id' => $user->ID, 'msg' => 'Speichern erfolgreich', 'err' => 0]);
    }
}

