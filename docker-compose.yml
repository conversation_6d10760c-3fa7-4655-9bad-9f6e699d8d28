services:
  php:
    image: ${DOCKER_IMAGE:-harbor.ifs.k8s.ndr-net.de/siv/yii2:php81apache}
    environment:
      - PHP_IDE_CONFIG=${PHP_IDE_CONFIG}
    restart: unless-stopped
    container_name: ${CONTAINER_NAME:-mentoring}
    volumes:
      - ~/.composer-docker/cache:/root/.composer/cache:delegated
      - ./:/app:delegated
    ports:
      - '${DOCKER_PORT:-8023}:80'
    extra_hosts:
      - "host.docker.internal:host-gateway"
networks:
  default:
    external: true
    name: client-bridge