<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Checkboxradio - Radio Group</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js" data-modules="controlgroup">
		$( "input" ).checkboxradio();
		$( "fieldset" ).controlgroup();
	</script>
</head>
<body>
<div class="widget">

	<h2>Radio Group</h2>
	<fieldset>
		<legend>Select a Location: </legend>
		<label for="radio-1">New York</label>
		<input type="radio" name="radio-1" id="radio-1">
		<label for="radio-2">Paris</label>
		<input type="radio" name="radio-1" id="radio-2">
		<label for="radio-3">London</label>
		<input type="radio" name="radio-1" id="radio-3">
	</fieldset>
</div>

<div class="demo-description">
<p>Example markup using the controlgroup widget to create a radio group.</p>
</div>
</body>
</html>
