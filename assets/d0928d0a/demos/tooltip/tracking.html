<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>jQuery UI Tooltip - Track the mouse</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<style>
	label {
		display: inline-block;
		width: 5em;
	}
	</style>
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js">
		$( document ).tooltip({
			track: true
		});
	</script>
</head>
<body>

<p><a href="#" title="That's what this widget is">Tooltips</a> can be attached to any element. When you hover
the element with your mouse, the title attribute is displayed in a little box next to the element, just like a native tooltip.</p>
<p>But as it's not a native tooltip, it can be styled. Any themes built with
<a href="https://jqueryui.com/themeroller/" title="ThemeRoller: jQuery UI's theme builder application">ThemeRoller</a>
will also style tooltips accordingly.</p>
<p>Tooltips are also useful for form elements, to show some additional information in the context of each field.</p>
<p><label for="age">Your age:</label><input id="age" title="We ask for your age only for statistical purposes."></p>
<p>Hover the field to see the tooltip.</p>

<div class="demo-description">
<p>Here the tooltips are positioned relative to the mouse, and follow the mouse while it moves above the element, using the <code>track</code> option.</p>
</div>
</body>
</html>
