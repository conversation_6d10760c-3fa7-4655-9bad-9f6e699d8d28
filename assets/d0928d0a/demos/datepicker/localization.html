<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>jQuery UI Datepicker - Localize calendar</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js" data-modules="i18n/datepicker-ar i18n/datepicker-fr i18n/datepicker-he i18n/datepicker-zh-TW">
		$( "#datepicker" ).datepicker( $.datepicker.regional[ "fr" ] );
		$( "#locale" ).on( "change", function() {
			$( "#datepicker" ).datepicker( "option",
				$.datepicker.regional[ $( this ).val() ] );
		});
	</script>
</head>
<body>

<p>Date: <input type="text" id="datepicker">&nbsp;
	<select id="locale">
		<option value="ar">Arabic (&#8235;(&#1575;&#1604;&#1593;&#1585;&#1576;&#1610;&#1577;</option>
		<option value="zh-TW">Chinese Traditional (&#32321;&#39636;&#20013;&#25991;)</option>
		<option value="">English</option>
		<option value="fr" selected="selected">French (Fran&ccedil;ais)</option>
		<option value="he">Hebrew (&#8235;(&#1506;&#1489;&#1512;&#1497;&#1514;</option>
	</select></p>

<div class="demo-description">
<p>Localize the datepicker calendar language and format (English / Western formatting is the default).  The datepicker includes built-in support for languages that read right-to-left, such as Arabic and Hebrew.</p>
</div>
</body>
</html>
