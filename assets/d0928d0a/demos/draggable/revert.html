<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>jQuery UI Draggable - Revert position</title>
	<link rel="stylesheet" href="../../themes/base/all.css">
	<link rel="stylesheet" href="../demos.css">
	<style>
	#draggable, #draggable2 { width: 100px; height: 100px; padding: 0.5em; float: left; margin: 0 10px 10px 0; }
	</style>
	<script src="../../external/requirejs/require.js"></script>
	<script src="../bootstrap.js">
		$( "#draggable" ).draggable({ revert: true });
		$( "#draggable2" ).draggable({ revert: true, helper: "clone" });
	</script>
</head>
<body>

<div id="draggable" class="ui-widget-content">
	<p>Revert the original</p>
</div>

<div id="draggable2" class="ui-widget-content">
	<p>Revert the helper</p>
</div>

<div class="demo-description">
<p>Return the draggable (or it's helper) to its original location when dragging stops with the boolean <code>revert</code> option.</p>
</div>
</body>
</html>
