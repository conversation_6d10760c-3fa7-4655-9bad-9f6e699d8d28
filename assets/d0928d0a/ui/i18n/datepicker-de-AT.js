/* German/Austrian initialisation for the jQuery UI date picker plugin. */
/* Based on the de initialisation. */

( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional[ "de-AT" ] = {
	closeText: "Schließen",
	prevText: "Zurück",
	nextText: "Vor",
	currentText: "Heute",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "April", "<PERSON>", "Juni",
	"Juli", "August", "September", "Okto<PERSON>", "November", "Dezember" ],
	monthNamesShort: [ "Jän", "Feb", "Mär", "Apr", "Mai", "Jun",
	"Jul", "Aug", "Sep", "Okt", "Nov", "Dez" ],
	dayNames: [ "<PERSON>nta<PERSON>", "<PERSON><PERSON>", "<PERSON>nst<PERSON>", "Mitt<PERSON><PERSON>", "Donnerstag", "Freitag", "Samstag" ],
	dayNamesShort: [ "So", "Mo", "Di", "Mi", "Do", "Fr", "Sa" ],
	dayNamesMin: [ "So", "Mo", "Di", "Mi", "Do", "Fr", "Sa" ],
	weekHeader: "KW",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional[ "de-AT" ] );

return datepicker.regional[ "de-AT" ];

} );
