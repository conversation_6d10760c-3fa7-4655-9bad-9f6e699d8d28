/* Bulgarian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON>. */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.bg = {
	closeText: "затвори",
	prevText: "назад",
	nextText: "напред",
	nextBigText: "&#x3E;&#x3E;",
	currentText: "днес",
	monthNames: [ "Януари", "Февруари", "Март", "Април", "Май", "Юни",
	"Юли", "Август", "Септември", "Октомври", "Ноември", "Декември" ],
	monthNamesShort: [ "Яну", "Фев", "Мар", "Апр", "Ма<PERSON>", "Юн<PERSON>",
	"Ю<PERSON><PERSON>", "Авг", "Сеп", "Окт", "Нов", "Дек" ],
	dayNames: [ "Неделя", "Понеделник", "Вторник", "Сряда", "Четвъртък", "Петък", "Събота" ],
	dayNamesShort: [ "Нед", "Пон", "Вто", "Сря", "Чет", "Пет", "Съб" ],
	dayNamesMin: [ "Не", "По", "Вт", "Ср", "Че", "Пе", "Съ" ],
	weekHeader: "Wk",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.bg );

return datepicker.regional.bg;

} );
