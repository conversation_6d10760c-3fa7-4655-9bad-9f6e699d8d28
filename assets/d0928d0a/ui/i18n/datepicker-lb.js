/* Luxembourgish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> <<EMAIL>> */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.lb = {
	closeText: "Fäerdeg",
	prevText: "Zréck",
	nextText: "Weider",
	currentText: "Haut",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "August", "September", "Oktober", "November", "Dezember" ],
	monthNamesShort: [ "Jan", "Feb", "<PERSON><PERSON>e", "<PERSON>br", "<PERSON><PERSON>", "<PERSON>",
	"<PERSON>", "Aug", "Sep", "<PERSON><PERSON>", "<PERSON>", "De<PERSON>" ],
	dayNames: [
		"<PERSON>ndeg",
		"Méindeg",
		"Dënschdeg",
		"Mëttwoch",
		"Donneschdeg",
		"Freideg",
		"Samschdeg"
	],
	dayNamesShort: [ "Son", "Méi", "Dën", "Mët", "Don", "Fre", "Sam" ],
	dayNamesMin: [ "So", "Mé", "Dë", "Më", "Do", "Fr", "Sa" ],
	weekHeader: "W",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.lb );

return datepicker.regional.lb;

} );
