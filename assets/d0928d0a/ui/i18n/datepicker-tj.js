/* <PERSON><PERSON><PERSON> (UTF-8) initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON> (said<PERSON><EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.tj = {
	closeText: "Идома",
	prevText: "Қафо",
	nextText: "Пеш",
	currentText: "Имрӯз",
	monthNames: [ "Январ", "Феврал", "Март", "Апрел", "Май", "Июн",
	"Июл", "Август", "Сентябр", "Октябр", "Ноя<PERSON>р", "Декабр" ],
	monthNamesShort: [ "<PERSON><PERSON><PERSON>", "<PERSON>е<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ма<PERSON>", "<PERSON>ю<PERSON>",
	"<PERSON><PERSON><PERSON>", "Авг", "Сен", "Окт", "Но<PERSON>", "Дек" ],
	dayNames: [ "якшанбе", "душанбе", "сешанбе", "чоршанбе", "панҷшанбе", "ҷумъа", "шанбе" ],
	dayNamesShort: [ "якш", "душ", "сеш", "чор", "пан", "ҷум", "шан" ],
	dayNamesMin: [ "Як", "Дш", "Сш", "Чш", "Пш", "Ҷм", "Шн" ],
	weekHeader: "Хф",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.tj );

return datepicker.regional.tj;

} );
