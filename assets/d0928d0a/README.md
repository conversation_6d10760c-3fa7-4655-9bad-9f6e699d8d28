# [jQuery UI](https://jqueryui.com/) - Interactions and Widgets for the web

_**Note:** jQuery UI is in maintenance-only mode. Please read [the project status blog post](https://blog.jqueryui.com/2021/10/jquery-maintainers-update-and-transition-jquery-ui-as-part-of-overall-modernization-efforts/) for more information._

jQuery UI is a curated set of user interface interactions, effects, widgets, and themes built on top of jQuery. Whether you're building highly interactive web applications, or you just need to add a date picker to a form control, jQuery UI is the perfect choice.

If you want to use jQuery UI, go to [jqueryui.com](https://jqueryui.com) to get started, [jqueryui.com/demos/](https://jqueryui.com/demos/) for demos, [api.jqueryui.com](https://api.jqueryui.com/) for API documentation, or the [Using jQuery UI Forum](https://forum.jquery.com/using-jquery-ui) for discussions and questions.

If you want to report a bug/issue, please visit [the GitHub issues page](https://github.com/jquery/jquery-ui/issues). Archive of older bug reports is kept for historical reasons in read-only mode at [bugs.jqueryui.com](https://bugs.jqueryui.com). If any of them still matters to you, please open a bug about it on GitHub, linking to the legacy [bugs.jqueryui.com](https://bugs.jqueryui.com) issue for context.

If you are interested in helping develop jQuery UI, you are in the right place.
To discuss development with team members and the community, visit the [Developing jQuery UI Forum](https://forum.jquery.com/developing-jquery-ui) or [#jqueryui-dev on irc.freenode.net](https://irc.jquery.org/).


## For Contributors

If you want to help and provide a patch for a bugfix or new feature, please take
a few minutes and look at [our Getting Involved guide](https://wiki.jqueryui.com/w/page/35263114/Getting-Involved).
In particular check out the [Coding standards](https://wiki.jqueryui.com/w/page/12137737/Coding-standards)
and [Commit Message Style Guide](https://contribute.jquery.org/commits-and-pull-requests/#commit-guidelines).

In general, fork the project, create a branch for a specific change and send a
pull request for that branch. Don't mix unrelated changes. You can use the commit
message as the description for the pull request.

For more information, see the [contributing page](CONTRIBUTING.md).

## Running the Unit Tests

Run the unit tests manually with appropriate browsers and any local web server. See our [environment setup](CONTRIBUTING.md#environment-minimum-required) and [information on running tests](CONTRIBUTING.md#running-the-tests).

You can also run the unit tests `npm run test:unit -- --help`.
