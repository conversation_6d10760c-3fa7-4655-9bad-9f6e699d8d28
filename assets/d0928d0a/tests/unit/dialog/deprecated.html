<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI Dialog Test Suite</title>

	<script src="../../../external/requirejs/require.js"></script>
	<script src="../../lib/css.js" data-modules="core button dialog"></script>
	<script src="../../lib/bootstrap.js" data-widget="dialog" data-deprecated="true"></script>
</head>
<body>

<div id="qunit"></div>
<div id="qunit-fixture">
	<div id="dialog1"></div>
	<div id="dialog2"></div>
	<div id="form-dialog" title="Profile Information">
		<!-- create a spacer to ensure there's enough space to scroll -->
		<div style="height: 250px;">...</div>
		<fieldset>
			<legend>Please share some personal information</legend>
			<label for="favorite-animal">Your favorite animal</label><input id="favorite-animal">
			<label for="favorite-color">Your favorite color</label><input id="favorite-color">
		</fieldset>
		<div role="group" aria-describedby="section2">
			<p id="section2">Some more (optional) information</p>
			<label for="favorite-food">Favorite food</label><input id="favorite-food">
		</div>
	</div>
	<div class="wrap" id="wrap1"></div>
	<div class="wrap" id="wrap2"></div>
</div>
</body>
</html>
