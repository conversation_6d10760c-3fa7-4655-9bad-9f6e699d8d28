define( [
	"qunit",
	"jquery",
	"lib/helper"
], function( QUnit, $, helper ) {
"use strict";

return $.extend( helper, {
	shouldDrop: function( assert ) {

		// Todo: actually implement this
		assert.ok( true, "missing test - untested code is broken code" );
	},

	shouldNotDrop: function( assert ) {

		// Todo: actually implement this
		assert.ok( true, "missing test - untested code is broken code" );
	}
} );

} );
