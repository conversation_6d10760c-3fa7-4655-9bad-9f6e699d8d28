define( [
	"lib/common",
	"ui/widgets/accordion"
], function( common ) {

common.testWidget( "accordion", {
	defaults: {
		active: 0,
		animate: {},
		classes: {
			"ui-accordion-header": "ui-corner-top",
			"ui-accordion-header-collapsed": "ui-corner-all",
			"ui-accordion-content": "ui-corner-bottom"
		},
		collapsible: false,
		disabled: false,
		event: "click",
		header: function( elem ) {
			return elem
				.find( "> li > :first-child" )
				.add(
					elem.find( "> :not(li)" )

						// Support: jQuery <3.5 only
						// We could use `.even()` but that's unavailable in older jQuery.
						.filter( function( i ) {
							return i % 2 === 0;
						} )
				);
		},
		heightStyle: "auto",
		icons: {
			"activeHeader": "ui-icon-triangle-1-s",
			"header": "ui-icon-triangle-1-e"
		},

		// Callbacks
		activate: null,
		beforeActivate: null,
		create: null
	}
} );

} );
