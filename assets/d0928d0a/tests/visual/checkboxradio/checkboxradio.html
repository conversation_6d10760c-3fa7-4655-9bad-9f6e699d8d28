<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>jQuery UI - Checkboxes</title>
	<link rel="stylesheet" href="../../../themes/base/all.css">
	<script src="../../../external/requirejs/require.js"></script>
	<script src="../../../demos/bootstrap.js">
		var checkboxes = $( "form input" ).checkboxradio();

		$( ".controls input, .controls select" ).on( "change keyup", function() {
			var option = $( this ).attr( "name" ),
				value = $( this ).val();

			if ( $( this ).is( "[type=checkbox]" ) ) {
				value = $( this ).is( ":checked" );
			}
			if ( option != "label" || value !== "" ) {
				checkboxes.checkboxradio( "option", option, value );
			}
		} );
		$( ".controls > button" ).on( "click", function() {
			if ( this.id !== "create" ) {
				checkboxes.checkboxradio( this.id );
			} else {
				checkboxes.checkboxradio();
			}
		} );
	</script>
	<style>
	#format { margin-top: 2em; }
	</style>
</head>
<body>
<h2>
	Easy way to toggle through various combinations of options and states to make sure none lead to
	a broken appearence.
</h2>
<div class="controls">
	<button id="create">Create</button>
	<button id="destroy">Destroy</button>
	<button id="enable">Enable</button>
	<button id="disable">Disable</button>
	<button id="refresh">Refresh</button>
	<input type="checkbox" id="icon" name="icon" checked><label for="icon">Icon</label>
	<input type="checkbox" id="disabled" name="disabled"><label for="disabled">Disabled</label>
	<label for="label">Label<input type="text" id="label" name="label"></label>
</div>
<form>
	<input type="checkbox" id="checkbox-1">
	<label for="checkbox-1">Checkbox widget sample</label>
	<input type="checkbox" id="checkbox-2"><label for="checkbox-2">Checkbox widget sample</label>

	<label for="radio-1">Radio widget sample <input type="radio" id="radio-1" name="radio" checked></label>
	<input type="radio" id="radio-2" name="radio"><label for="radio-2"><span>boom</span>Radio widget sample 2</label>
	<button type="reset">Reset</button>
</form>
</body>
</html>
