<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<title>Tooltip Visual Test: Animations</title>
	<link rel="stylesheet" href="../../../themes/base/all.css">
	<style>
	pre {
		width: 250px;
		border: 1px solid #000;
		padding: .5em;
	}
	</style>
	<script src="../../../external/requirejs/require.js"></script>
	<script src="../../../demos/bootstrap.js"
		data-modules="effect effect-explode effect-bounce effect-blind effect-drop">
		$( "pre" ).each(function( index, elem ) {
			$( elem )
				.attr( "title", "animated tooltips" )
				.tooltip( JSON.parse( $( elem ).text() ) );
		});
	</script>
</head>
<body>

<pre>{}</pre>
<pre>{
	"show": {
		"effect": "slideDown"
	},
	"hide": {
		"effect": "slideUp"
	}
}</pre>
<pre>{
	"show": {
		"effect": "explode"
	},
	"hide": {
		"effect": "explode"
	}
}</pre>
<pre>{
	"show": {
		"effect": "bounce"
	},
	"hide": {
		"effect": "blind"
	}
}</pre>
<pre>{
	"show": {
		"effect": "drop",
		"direction": "right"
	},
	"hide": {
		"effect": "drop",
		"direction": "right"
	}
}</pre>

</body>
</html>
