{"version": 3, "file": "filter-multi-select-bundle.js", "sources": ["../src/FilterMultiSelect.ts", "../src/filter-multi-select.ts"], "names": ["$", "class_1", "prototype", "show", "hide", "isHidden", "focus", "activate", "deactivate", "key", "e", "stopPropagation", "_this", "closeDropdown", "preventDefault", "decrementItemFocus", "focusItem", "incrementItemFocus", "refocusFilter", "div", "target", "contains", "this", "fmsFocusListener", "viewBar", "dispatchEvent", "MouseEvent", "fmsMousedownListener", "select", "t", "name", "Error", "array", "selectTarget", "find", "toArray", "numSelectedItems", "numOptions", "options", "length", "restrictSelection", "maxNumSelectedItems", "selectAllOption", "FilterMultiSelect", "RestrictedSelectAllOption", "args", "selectAllText", "UnrestrictedSelectAllOption", "filterInput", "type", "clearButton", "document", "createElement", "filter", "append", "items", "placeholderText", "selectedItems", "label", "textContent", "labelText", "customLabel", "hidden", "selectionCounter", "placeholder", "id", "disabled", "showing", "Array", "initialize", "map", "o", "i", "checkValue", "value", "SingleOption", "fms", "defaultSelected", "concat", "jsoptions", "undefined", "getValue", "get<PERSON><PERSON><PERSON>", "opts", "event", "CustomEvent", "detail", "v", "l", "cancelable", "className", "dropDown", "attachViewbarListeners", "log", "m", "attachDropdownListeners", "addEventListener", "numShown", "stopImmediatePropagation", "text", "clearFilterAndRefocus", "updateDropdownList", "for<PERSON>ach", "push", "filterText", "itemFocus", "classList", "setTabIndex", "isDisabled", "tabIndex", "isClosed", "removeEventListener", "documentKeydownListener", "documentClickListener", "add", "queueOption", "option", "indexOf", "getSelectedItemBadge", "unqueueOption", "update", "areAllSelected", "areSomeSelected", "markSelectAllNotDisabled", "markDeselect", "canSelect", "areOnlyDeselectedAlsoDisabled", "areAllDisabled", "reduce", "acc", "cur", "getOption", "NULL_OPTION", "_i", "_a", "deselect", "row", "toString", "nchbx", "checked", "checkbox", "labelFor", "closeButton", "innerHTML", "selectedItemBadge", "initiallyChecked", "setDisabledViewState", "isSelected", "selectNoDisabledCheck", "isActive", "dispatchSelectedEvent", "dispatchDeselectedEvent", "setEnabledViewState", "remove", "class_2", "indeterminate", "class_4", "markSelectAll", "filterMultiSelect", "$__default", "extend", "fn", "selectionLimit", "caseSensitive", "allowEnablingAndDisabling", "getRootElement", "methods", "includeDisabled", "applied", "selector", "s", "each"], "mappings": "iuCAqBMC,SAAAC,UAAAC,KAAA,YAAkBF,SAAAC,UAAAE,KAAA,YAkBxBH,SAAAC,UAAAG,SAAA,uBAjBIJ,SAAAC,UAAAI,MAAA,YACAL,SAAAC,UAAAK,SAAA,YACAN,SAAAC,UAAAM,WAAA,gLA+FaC,KACL,IAAK,MACDC,EAACC,iBACDC,OAAKC,eACL,qBAEAH,EAAAC,iBACAD,GAAAI,gBAIAF,OAAAG,oBACAH,OAAII,oCAGHL,iBACDD,GAAAI,uBAKIG,6DAGH,iCAMDL,MAAAM,eACA,mDA6CJN,MAAKO,MAALT,EAAeU,SAAfR,MAAAO,IAAAE,SAAAX,EAAAU,QAAA,wBAMAE,MAAAC,iBAAA,SAAAb,sBAMJA,GAACI,gBAEMF,OAAAY,QAAAC,cAAA,GAAAC,YAAA,UAGHJ,MAAAK,qBAAA,SAAAjB,sBAMJA,GAACI,gJAmiBGc,QAAAC,2BACGC,KAAA,CACH,KAAA,IAAAC,OAAA,mDACGD,KAAAA,QACHE,OAAkCC,aAAAC,KAAA,UAAAC,kFA+BtCb,MAAAc,iBAAA,+KAIA,IAAAC,YAAAf,KAAAgB,QAAwCC,MACxC,IAAAC,mBAAAlB,KAAAmB,oBAAA,GAAAnB,KAAAmB,oBAAAJ,UACAf,MAAKmB,oBAAsBD,kBAAAlB,KAAAmB,oBAAAJ,WAAA,CAC3Bf,MAAKoB,gBAALF,kBAAA,GAAAG,mBAC6BC,0BAAAtB,KAAAQ,KAAAe,KAAAC,eACrB,GAAAH,mBAAAI,4BAAAzB,KAAAQ,KAAAe,KAAAC,+DAKRxB,MAAA0B,YAAiBC,KAAO,mDACxB3B,MAAK4B,YAALC,SAAAC,cAAA,SACA9B,MAAA4B,YAAAD,KAAA,iFAGiB,MACjB3B,MAAA+B,OAAAC,OAAAhC,KAAwB0B,YAAa1B,KAAA4B,YAErC5B,MAAAiC,MAAAJ,SAAsBC,cAAA,8PASMA,cAAA,qCACGP,KAAAW,qBAC1BC,cAAAN,SAAAC,cAAA,mBAEQD,SAAAC,cAAA,aACRM,MAAAC,YAAAd,KAAAe,uDACEC,YAAA,CACHvC,KAAKoC,MAALI,OAAoB,2BAIAX,SAAQC,cAAR,kGAIxB9B,MAAKE,QAAL8B,OAAAhC,KAAAoC,MAAApC,KAAAyC,iBAAAzC,KAAA0C,YAAA1C,KAAAmC,+DAGSQ,GAAArC,OAAAqC,mGAGOrC,OAAAsC,yFAGhB5C,MAAK6C,QAAL,GAAAC,wBAIA9C,MAAA+C,kHApS8BC,IAAA,SAAAC,EAAAC,qBACTC,WAAAF,EAAAG,MAAAH,EAA6Bb,oCACjBiB,aAAAC,IAAAJ,EAAA1C,KAAAyC,EAAAb,MAAAa,EAAAG,MAAAH,EAAAM,gBAAAN,EAAAL,2FAGN,aACHK,EAAE,8LAOLO,OAAAC,sCAER,SAAAR,qCACQS,UAAA,kBAGb,KAAA,IAAAjD,OAAA,oBAAAwC,EAAAU,WAAA,KAAAV,EAAAW,WAAA,OAGR,OAAOC,mDAG2BzB,sBAE9B,KAAA,IAAA3B,OAAA,UAA4B2B,MAAA,yFAwFhC,GAAA0B,OAAW,GAAAC,aAAA3E,GACP4E,eAEIZ,MAAAa,EACA7B,MAAA8B,WAEG,KACPC,WAAA,qBAGJ,OAAAL,+CA0KJ,mEAGI9D,MAAAoB,gBAAA2B,yFAOShB,OAAOqC,UAAA,4BACPnC,MAAAmC,UAAA,2BACAC,SAAAD,UAAA,wDAITpE,MAAAmC,cAAmBiC,UAAnB,sBACSlE,QAAAkE,UAAA,4CACAhC,MAAMgC,UAAA,sEAIfpE,MAAAH,IAAAuE,UAAA,+TAeKE,wBAELtE,MAAAT,gBAII8B,mBAAAzC,UAAA2F,IAAA,SAAIC,EAAWpF,IAKIiC,mBAAAzC,UAAA6F,wBAAA,yBACvBzE,MAAK0B,YAALgD,iBAAA,QAAA,SAAAtF,0DAQI,IAAAuF,UAAArF,MAAAuD,QAAA5B,MAAyC,QAAO7B,EAAAD,8CAIpC6B,QAAA1B,MAACuD,QAAA,iMAeItD,wBA3BrB,KAmCAS,MAAA4B,YAAA8C,iBAAkC,QAAlC,SAAAtF,GAIIA,EAACwF,0BAAwC,IAAAC,MAAAvF,MAAAoC,YAAA0B,KACzC,IAAAyB,KAAS5D,OAAA,EAAT,CACI3B,MAAAwF,4BADJ,yBALJ,MAaIzD,mBAAAzC,UAAAmG,mBAAA,kFAIqBjG,qFAQrBkB,KAAKgB,QAALgE,QAAA,SAAA/B,EAAAC,yLAUQD,EAACpE,eACOoG,KAAA/B,oBAMpBlD,KAAKkF,WAALL,IACA7E,MAAA6C,QAAAA,sEAOA7C,KAAK0B,YAAL0B,MAAA,EACApD,MAAA+E,qFAIJ,mCAOI/E,MAAKmF,WAAa,EAOd9D,mBAAAzC,UAAA0F,uBAAA,0HAYO,kFA/6BHtE,KAAAqE,SAAAe,UAAArF,SAAA,QAGJsB,mBAAAzC,UAAAyG,YAAA,cACArF,KAAAsF,aAA2B,CAC3BtF,KAAAH,IAAS0F,UAAT,OAEA,GAAIvF,KAAAwF,WAAiB,CACjBxF,KAAAH,IAAS0F,SAAT,MADJ,CAGIvF,KAAAH,IAAS0F,UAAW,IAKxBlE,mBAAAzC,UAAAW,cAAA,mCAIKkG,oBAAoB,UAAAzF,KAAA0F,wBAAA,eACpBD,oBAAA,QAAAzF,KAAA2F,sBAAA,mEAGLrG,MAAK+F,mBAETrF,MAAAH,IAAA6E,iBAAA,YAAA1E,KAAAK,qBAAA,KAEQL,MAAAH,IAAA6E,iBAAA,QAAa1E,KAAAC,6GA4CcgB,OAAA,EAAA,6CAInB,IAAMjB,KAAAoB,gBAAAkE,cAAAtF,KAAAoB,gBAAArC,2GAEjBoG,UAAAjC,EAAAlD,KAAAgB,QAAAC,OAAA,EAAAjB,KAAAmF,UAAAjC,qEAMIiC,YAAA,EAAA,aACDnF,KAAImF,2BAID,IAAAnF,KAAAoB,gBAAAkE,cAAAtF,KAAAoB,gBAAArC,aAAAmE,GAAA,IAAAlD,KAAAgB,QACkBkC,GAAGoC,cAAAtF,KAAAgB,QAAAkC,GAAAnE,aAC5BmE,GAAK,EACTlD,MAAAmF,UAAAjC,oDAQQ,GAAAlD,KAAAmF,aAAA,EAAA,CACJnF,KAAKJ,oBADD,IAAAI,KAAAmF,aAAA,EAAA,CAEJnF,KAAKoB,gBAAgBpC,aAErBgB,KAAKgB,QAALhB,KAAAmF,WAAAnG,8DAqCG,GAAAgB,KAAA4C,SAAA,+EAMP5C,MAAAH,IAAA4F,oBAAA,QAAAzF,KAAAC,iBAEOD,MAAAqE,SAAAe,UAAAQ,IAAA,0BAEP5F,MAAA8E,uBAEOjD,UAAA6C,iBAAQ,UAAR1E,KAAA0F,wBAAA,yEAKHrE,mBAAAzC,UAAAiH,YAAA,SAAYC,QAChB,GAAA9F,KAAAgB,QAAA+E,QAAAD,UAAA,EAAA,MAEO9F,MAAAc,6DACekB,OAAA8D,OAAAE,wBAIlB3E,mBAAAzC,UAAAqH,cAAA,SAAAH,QACJ,GAAA9F,KAAAgB,QAAA+E,QAAAD,UAAA,EAAA,MAEO9F,MAAAc,wJAKHO,mBAAAzC,UAAAsH,OAAA,WACJ,GAAAlG,KAAAmG,iBAAA,qCAGInG,MAAA0C,YAAAF,OAAA,SAHJ,IAAAxC,KAAAoG,kBAAA,0CAOQpG,KAAAoB,gBAAAiF,0BACArG,MAAK0C,YAALF,OAAA,sFAOJxC,KAAKoB,gBAALkF,6IAyCAtG,KAAKuG,YAAA,0GAKLvG,KAAKgB,QAALe,OAAA,SAAAkB,2BAAA+B,QAAA,SAAA/B,uDASA5B,mBAAAzC,UAAAuH,eAAA,+KASG,MAAAnG,MAAAgB,QAAAgC,IACM,SAAAC,4EAGT5B,mBAAAzC,UAAA4H,8BAAA,WACJ,MAAAxG,MAAAgB,QAAAe,OAAA,SAAAkB,+BAGa,SAAAA,2EAGT5B,mBAAAzC,UAAA6H,eAAA,WACJ,MAAAzG,MAAAgB,QAAAgC,IAAA,SAAAC,2BAGSyD,OAAA,SAAQC,IAAAC,iQAcJxD,OACb,MAAApD,MAAA6G,UAAAzD,SAAA0D,mEAKA,IAAA,GAAAC,IAAA,EAAAC,GAAAhH,KAAAgB,QAAA+F,GAAAC,GAAA/F,OAAA8F,KAAA,gDAKA,MAAAD,uEAIA,GAAA9G,KAAAsF,aAAA,MACAtF,MAAA6G,UAAAzD,OAAA9C,qEAIA,GAAAN,KAAAsF,aAAA,MACAtF,MAAA6G,UAAAzD,OAAA6D,ynGAiUmB,0MAgBftE,IAAAnC,KAAA,IAAA0G,IAAAC,cACAC,OAAKzE,GAAA,6CAEKnC,KAAAA,2EAGD6G,QAAA,oBACCzE,SAAAA,kHA2Cd5C,MAAAH,IAASmC,OAAThC,KAAAsH,SAAAtH,KAAAuH,oCAC0BzF,cAAA,wCAE1B9B,MAAAwH,YAAAC,UAAA,iCACwB5F,SAAAC,cAAA,iIAIe9B,KAAAwH,YACvCxH,MAAA4C,SAAAA,qBACa,sCAGMxD,2DAQnBY,MAAAH,IAAAuE,UAAA,8BACApE,MAAAsH,SAAAlD,UAAA,qFAzWIpE,MAAA0H,kBAAuBtD,UAAvB,MACA,IAAApE,KAAA2H,iBAAA,8BAGA,GAAA3H,KAA4B4C,SAA5B,CACI5C,KAAA4H,uBAEJ5H,KAAAsD,IAAA4C,+DAEK7G,qBAC6BC,MAAAgG,cAAAhG,MAA0BgE,IAAAgC,aAAA,CACpDlG,EAAAI,iCAQeqI,aAAA,sCAKf,GAAAlD,UAAArF,MAAAgE,IAAAT,QAAA5B,8BAGIqC,IAAAwB,0BAEX,KACD9E,MAAAsH,SAAA5C,iBAAA,QAAA,SAAAtF,YAIcD,mGAJd,KAgBAa,MAAAwH,YAAA9C,iBAAA,QAAA,SAA4CtF,uGAQ9BkE,IAAAkC,WAAA,WACG5F,kBATjB,KAaAI,MAAAsH,SAAA/B,UAAA,CACAvF,MAAAwH,YAAiBjC,UAAW,4BAIhC,WAEI,GAAIvF,KAAAsF,aAAJ,MACAtF,MAAK8H,uBACL9H,MAAKsD,IAAL4C,kDAIJ,WACI,IAAAlG,KAAAsD,IAAciD,cAAAvG,KAAA+H,WAAd,MACA/H,MAAAsH,SAAAD,QAAA,IACArH,MAAAsD,IAAAuC,YAAA7F,KAEAA,MAAAsD,IAAA0E,sBAAAhI,kCA1OJ,WACI,GAAAA,KAAAsF,aAAA,MACAtF,MAAAsH,SAAAD,QAAA,kCAEArH,MAAAsD,IAAA2E,wBAAAjI,KACAA,MAAAsD,IAAS4C,8CAITlG,KAAA4C,SAAyB,KACzB5C,MAAAkI,qBACAlI,MAAAsD,IAAA4C,gDAIJ,WACIlG,KAAAsH,SAAA1E,SAAA,KACA5C,MAAK0H,kBAALtC,UAAA+C,OAAA,uDAGOvF,SAAA,iIAQP5C,MAAK0H,kBAALtC,UAAAQ,IAAA,0EA2FkByB,6DAINC,SAAA1E,mDAIZ,MAAA5C,MAAYH,IAGTuI,SAAAxJ,UAAAoH,qBAAP,qEA6GA,WAGI,MAAAhG,MAAYuH,SAAAlF,wCAIhB,WACI,MAAArC,MAAAsH,SAAAlE,8BAIJ,WACIpD,KAAAH,IAAA2C,OAAA,8BAIJ,WACIxC,KAAKH,IAAL2C,OAAA,4CAGA,MAAAxC,MAAAH,IAAA2C,iEAUJ4F,SAAAxJ,UAAAmJ,SAAA,+LAcI/H,MAAA4H,oMAqCS,EAAApH,KAAA4B,MAAA,GAAA,MAAA,QAAApC,gGAMTA,KAAAsH,SAAAD,QAAwB,IACxBrH,MAAKsH,SAASe,cAAd,sDAGArI,KAAKsH,SAALD,QAAA,uIAMcgB,cAAA,8DAGAhB,QAAA,WACTC,SAAAe,cAAA,2CAGL,GAAIrI,KAAAsF,aAAJ,gBACStE,QAAAe,OAAA,SAAAkB,UAAuBA,EAAA4E,gCAClB5E,2GAKdjD,MAAAsD,IAAAtC,QAAAe,OAAA,SAAAkB,2BAAA+B,QAAA,SAAA/B,wBAEAjD,MAAAsD,IAAA4C,8CAKAlG,KAAK4C,SAAL,KACA5C,MAAAsH,SAAA1E,SAAA,iCAGJ,WACI5C,KAAA4C,SAAgB,svBAqBpB0F,SAAA1J,UAAAgF,SAAA,gKAIA0E,SAAA1J,UAAAE,KAAA,oGAIAwJ,SAAA1J,UAAA2J,cAAA,iZCtoBMC,kBAAA,SAAAjH,MACV,GAAAzB,QAAAE,IAGAuB,MAAAkH,WAAA,WAAAC,UAAAD,WAAA,WAAAE,GAAAH,kBAAAjH,KAAAA,KAEA,UAAWA,MAAKW,kBAAZ,YAAJX,KAAAW,gBAAA,4BAA4CX,MAAA2D,aAA2B,YAAA3D,KAAA2D,WAAA,QACvE,UAAW3D,MAAKC,gBAAkB,YAAlCD,KAAAC,cAAA,sBAA+CD,MAAAe,YAAA,YAAAf,KAAAe,UAAA,EAC/C,UAAWf,MAAKqH,iBAAZ,YAAJrH,KAAAqH,eAAA,WAA2CrH,MAAAsH,gBAAA,YAAAtH,KAAAsH,cAAA,KAC3C,UAAWtH,MAAKuH,4BAA8B,YAA9CvH,KAAAuH,0BAAA,cAAgDvH,MAAAU,QAAsB,YAAAV,KAAAU,MAAA,GAAAa,MAEtE,IAAI0F,mBAAA,GAAAnH,mBAA0CvB,OAAAyB,KAC9C,IAAA+B,KAAAmF,WAAA,WAAAD,kBAAAO,yCAKA,IAAMC,6BACkB5F,0tBAkCe6F,iBAAA,GAAAA,sBAAA,GAAA,CAAAA,gBAAA,yEAKtCvK,YAAAA,WAACiK,GAADH,kBAAAU,QAAAjE,KAAA+D,QAGA,OAAAA,SAMLtK,YAAAA,WAAE,WAEE,GAAIyK,gBAA4BV,YAAA,WAAAE,GAA6BH,kBAAAW,WAA7B,YAAA,6BAAAV,WAAA,WAAAE,GAAgIH,kBAAAW,QAEhK,IAAIC,GAACX,WAAA,WAAAU,SACLC,GAAEC,KAAK,SAAAnG,EAAA9D,GACDV,WAAAA,WAAEU,GAAFoJ,oFAWR9J,YAAAA,WAACiK,GAADH,kBAAuCW,SAAvCzF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IDtFI,EAAA,OAAA,CAAA,SAAA,CAAA,IAAA,GAAA,YAAA,EAAA;;IAAkB,EAAA,OAAA,CAAA,SAAA,CAAA,IAAA,GAAA,YAAA,EAAA;;IAkBxB,EAAA,OAAA,CAAA,SAAA,CAAA,QAAA,GAAA,YAAA;;OAAA;;IAjBI,EAAA,OAAA,CAAA,SAAA,CAAA,KAAA,GAAA,YAAA,EAAA;;IACA,EAAA,OAAA,CAAA,SAAA,CAAA,QAAA,GAAA,YAAA,EAAA;;IACA,EAAA,OAAA,CAAA,SAAA,CAAA,UAAA,GAAA,YAAA,EAAA;;;;;;;;;;;;;mBA+FY,CAAC;IACL,aAAK,KAAL;IACI,UAAA,CAAC,gBAAD;;IACA,UAAA,KAAI,CAAC,aAAL;;IACA;;;IAEA,UAAA,iBAAA;IACA,UAAA,gBAAA;;IAIA,UAAA,wBAAA;;IACA,UAAA,KAAI,UAAJ;;;;;eAGC;IACD,UAAA,gBAAA;;mBAKI;;;;;;;iBAGH;;IAED;;;;;IAIA,UAAA,mBAAA;;IACA;;;;;;cA6CJ,KAAI,CAAC,GAAL,OAAe,MAAf;;;;;IAMA,SAAA,gBAAA;;;IAMJ,MAAA,CAAC,eAAD;;IAEO,MAAA,aAAA,cAAA,wBAAA;SARH;;IAWA,SAAA,oBAAA;;;IAMJ,MAAA,CAAC,eAAD;SANI;;;;;;;;YAyiBA;;;aACG;IACH,mEAAA;IACH;;aAAM;YACH,KAAK,GAA6B,iBAAA,SAAA,SAAA;;;IA+BtC,yBAAA,IAAA;;;IAIA,kCAAwC,MAAxC;IACA,iGAAA;IACA,SAAK,mBAAL,GAA2B,6DAA3B;;IACA,SAAK,eAAL,4CAC6B,6DACrB,iDAAA,KAAA,MAAA,oBAAA,CAFR;;;IAOA,oBAAA,CAAiB,IAAjB,GAAwB,MAAxB;;IACA,SAAK,WAAL,mCAAA;IACA,oBAAA,KAAA,WAAA;;6CAGiB;IACjB,eAAA,OAAA,MAAwB,WAAxB,EAAqC,gBAArC;;IAEA,cAAA,WAAqB,CAAC,oBAAtB;;;;;;;;;oCAS4B;uCACG;aAC1B;;qBAEQ,sBAAA,OAAA;aACR;;;aACE;IACH,WAAK,KAAL,OAAA,GAAoB,IAApB;;;;gCAIoB,QAAQ,cAAR,OAAA;;;;IAIxB,SAAK,OAAL,OAAA,WAAA,uBAAA,kBAAA,oBAAA;;iBAGS;;;wBAGO;;;IAGhB,SAAK,OAAL,cAAA;;;IAIA,mBAAA;;;;qCApS8B;2BACT,sBAA4B,CAAC;uCACjB;;;;uBAGN,EAAA;sBACH,CAAC,CAAC,CAAD;;;;;;8BAOJ;;qBAER,WAAA;;;4BACQ;;;IAGb,uFAAA;;;IAGR,WAAO,IAAP;;;sDAGkC;;IAE9B,kCAA4B,8CAA5B;;;;;IAwFJ,aAAW;IACP,MAAA,QAAQ;mBAAA;IAEJ,QAAA,QAFI;IAGJ,QAAA;IAHI;iBAKD;IACP,MAAA;;UAPJ;IAUA,gBAAA;IACH;;+CAyKD,YAAA;;;;IAGI,wBAAA,WAAA;;;aAOS,OAAO;aACP;aACA;;IAIT,sBAAA,CAAmB,SAAnB,mBAAA;aACS;aACA,MAAM;;IAIf,YAAA,UAAA,iCAAA;;;;;;;;;;;;;;aAeK;IAEL,sBAAA;IACH;;IAGO,EAAA,iBAAA,CAAA,SAAA,CAAA,GAAA,aAAI,GAAW;OAAf;;IAKmB,EAAA,iBAAA,CAAA,SAAA,CAAA,uBAAA;;;IACvB,SAAK,WAAL,iBAAA,QAAA;;;;;;IAQI,yCAAA;;IAAyC,cAAO,KAAP;;;8BAI7B,cAAC;;;;;;;IAMI;;;;;;;;;;;;qBASD,CAAC;IACR;;;IApBgC;SAR7C,MAAA;IAmCA,oBAAA,iBAAA,CAAkC,OAAlC;;IAII,MAAA,CAAC,yBAAD;IAAyC,wCAAA;;IACzC,cAAQ,CAAC,UAAT;IACI,QAAA,2BAAA;WADJ;;;SALJ,MAAA;OApCuB;;IAiDnB,EAAA,iBAAA,CAAA,SAAA,CAAA,kBAAA;;;;+BAIqB;;;;;;;;IAQrB,WAAK,OAAL,QAAA;;;;;;;;IAMC,OAND;;;;;IAUQ,UAAA,CAAC,KAAD;sBACQ;;;;IAIf;IACJ;;IACD,SAAK,UAAL,OAAA;IACA,gBAAA,UAAA;IACH,GA/BO;;;;IAqCJ,SAAK,WAAL,MAAA,KAAA;IACA,2BAAA;;;;kDAIJ,YAAA;;;IAOI,SAAK,SAAL,IAAkB,CAAlB,CAPJ;IAWC;;IAGO,EAAA,iBAAA,CAAA,SAAA,CAAA,sBAAA;;;;;;;iBAYO;;;;IAKd,GAjBO;;;gBAn6BI,aAAA,UAAA,SAAA,OAAA;;;IAGJ,EAAA,iBAAA,CAAA,SAAA,CAAA,WAAA;YACA,eAAA,IAA2B;IAC3B,cAAA,CAAS,QAAT,KAAA;;IAEA,UAAI,aAAA,EAAJ,EAAqB;IACjB,gBAAA,CAAS,QAAT,IAAA;WADJ;IAGI,gBAAA,CAAS,QAAT,GAAoB,EAApB;;;OAPJ;;IAYA,EAAA,iBAAA,CAAA,SAAA,CAAA,aAAA;;;gBAII,CAAC,oBAAoB;gBACrB,CAAC;;;IAGL,MAAA,KAAI,CAAC,WAAL;;;IAEJ,YAAA,iBAAA,YAAA,2BAAA,MAAA;IAEQ,YAAA,iBAAA,QAAA,EAAa,qBAAb;OAZJ;;;2CAwD+B;;;;;uBAInB,MAAM,oBAAA,WAAA;;;aAEjB;;;;iBAMI;;gBACD,KAAI;;;;kBAID,4GACkB,GAAG,2CAA5B,IACA,IAAI,CAAC;;;IACT,kBAAA,IAAA;;;;IAQQ,6BAAA;IACJ,WAAK,aAAL;SADI;IAEJ,WAAK,eAAL,CAAqB,KAArB;;IAEA,WAAK,OAAL,eAAA,OAAA;IACH;;;;IAoCM,qBAAA;;;IAMP,YAAA,oBAAA,QAAA,uBAAA;IAEO,iBAAA,UAAA,IAAA,OAAA;;IAEP,8BAAA;IAEO,IAAA,yBAAA,CAAQ,SAAR,8BAAA,MAAA;;;;IAKH,EAAA,iBAAA,CAAA,SAAA,CAAA,WAAA,aAAY,QAAc;IAC9B,0CAAA;IAEO,yBAAA;kDACe;OAJlB;;IAQA,EAAA,iBAAA,CAAA,SAAA,CAAA,aAAA;IACJ,0CAAA;IAEO,yBAAA;;OAHH;;IAQA,EAAA,iBAAA,CAAA,SAAA,CAAA,MAAA;IACJ,6BAAA;;IAGI,sBAAA,OAAA,OAAA;IACH,KAJD;;IAOQ,4BAAA,yBAAA;IACA,aAAK,WAAL,OAAA,OAAA;;;;;;IAOJ,WAAK,eAAL,aAAA;;;;;;;;;;YAyCA,MAAK;;;;;;;IAKL,WAAK,OAAL,OAAA;;WAAA,SAAA;;WAAA;;;;OA9DA;;IAuEA,EAAA,iBAAA,CAAA,SAAA,CAAA,cAAA;;;;;;OAAA;;;IASG,4BACM,WAAA;;;;iBADN;;;IAIH,EAAA,iBAAA,CAAA,SAAA,CAAA,6BAAA;IACJ;;eAGa,WAAA;;;;gBAHb;OADI;;IAOA,EAAA,iBAAA,CAAA,SAAA,CAAA,cAAA;IACJ;;WAGS,iBAAQ;;gBAHjB;OADI;;;;;;;;;;wDAkBS;IACb,gDAAA;;;;IAKA,sCAAA,gBAAA,MAAA;;;;;;;;IAKA,sBAAA;;;;IAIA,yBAAA;IACA,kBAAA,MAAA,QAAA;;;;IAIA,yBAAA;IACA,kBAAA,MAAA,UAAA;;;;;;;;;;IAsoBP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IArU0B;IAAA,cAAA;;;;;;cAgBf;cACA,KAAK;;wBAEK;;;wBAGD;wBACC;;;;IA2Cd,cAAA,CAAS,MAAT,cAAA,eAAA;qCACyB,CAAC;;IAE1B,sBAAA,UAAA,YAAA;mCACwB,sBAAA,OAAA;;;wCAIe;IACvC,mBAAA,WAAA;wBACa;;;6CAGM;;;;;;IAQnB,cAAA,UAAA,iCAAA;IACA,mBAAA,UAAA,yCAAA;;IAzWI,4BAAA,CAAuB,SAAvB,SAAA;;IACA,+BAAA;;;;IAGA,eAA4B,QAA5B;IACI,iCAAA;IACH;;IACD,cAAA,OAAA;;aAEK;;gBAC6B,gBAAA,YAA0B;IACpD,UAAA,gBAAA;;;;sBAQe;;;;;;IAKf,+CAAA;;;mBAGI;;IAEX;IACD,mBAAA,iBAAA,QAAA;;sBAIc;;;;;;;;WAJd,MAAA;IAgBA,sBAAA,iBAAA,QAAA,YAA4C;;;;;;uBAQ9B;wBACG;;WATjB,MAAA;IAaA,mBAAA,SAAA,KAAA;IACA,sBAAA,CAAiB,QAAjB,GAA4B,EAA5B;;;mCAIJ,YAAA;IAEI,UAAI,eAAA,EAAJ;IACA,WAAK,qBAAL;IACA,WAAK,GAAL,OAAA;;;kDAIJ,YAAA;IACI,oBAAc,+BAAd;IACA,mBAAA,QAAA,OAAA;IACA,cAAA,YAAA,KAAA;IAEA,cAAA,sBAAA,KAAA;;;qCA1OJ,YAAA;IACI,2BAAA;IACA,mBAAA,QAAA,QAAA;;IAEA,cAAA,wBAAA,KAAA;IACA,cAAA,CAAS,MAAT;;;;IAIA,mBAAA,GAAyB,KAAzB;IACA,8BAAA;IACA,cAAA,OAAA;IAEH;;gDAED,YAAA;IACI,mBAAA,SAAA,QAAA;IACA,WAAK,iBAAL,UAAA,OAAA,WAAA;;;;eAGO;;;IAGV;;;;IAKG,WAAK,iBAAL,UAAA,IAAA,WAAA;;;;+BA2FkB;;;;sBAIN;;;;IAIZ,kBAAY,GAAZ;;;IAGG,IAAA,OAAA,CAAA,SAAA,CAAA,oBAAA,GAAP,YAAA;;SAAO;;qCA6GP,YAAA;IAGI,kBAAY,oBAAZ;;;qCAIJ,YAAA;IACI,gCAAA;;;iCAIJ,YAAA;IACI,cAAA,OAAA,QAAA;;;iCAIJ,YAAA;IACI,WAAK,GAAL,OAAA,OAAA;;;;IAGA,4BAAA;IACH;;;;;;IASD,IAAA,OAAA,CAAA,SAAA,CAAA,QAAA,GAAA,YAAA;;SAAA;;;;;;;;;;;;IAcI,+BAAA;;;;OAkJe;;;;;;;;6CA7GN;;;;IAEZ;;;IAIG,mBAAA,QAAA,GAAwB,IAAxB;IACA,WAAK,QAAL,CAAc,aAAd,QAAA;;;;IAGA,WAAK,QAAL,QAAA,QAAA;;;;;;wBAMc;;;;wBAGA;eACT;;;;IAGL,UAAI,eAAA,EAAJ;mBACS;oBAAuB,YAAA;+BAClB;;;;;;;;IAKd,cAAA,QAAA,OAAA;;WAAA,SAAA;;WAAA;IAEA,cAAA,OAAA;;;;IAKA,WAAK,QAAL,QAAA;IACA,mBAAA,SAAA,QAAA;IACH;;oCAED,YAAA;IACI,mBAAA,GAAgB,IAAhB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqBJ,IAAA,OAAA,CAAA,SAAA,CAAA,QAAA,GAAA,YAAA;;SAAA;;;;;;;;IAIA,IAAA,OAAA,CAAA,SAAA,CAAA,IAAA,GAAA,YAAA,EAAA;;;;;;;;IAIA,IAAA,OAAA,CAAA,SAAA,CAAA,aAAA,GAAA,YAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;IC9pBR;;;;;;;;;;;;;;;;;;IAkBG;;6BAMW;IACV,mBAAA;;IAGA,EAAA,8FAAA;;IAEA,MAAI,OAAO,IAAI,CAAC,eAAZ,gBAAJ;UAA4C,sBAAA,KAA2B;IACvE,MAAI,OAAO,IAAI,CAAC,aAAZ,KAA8B,WAAlC;UAA+C,qBAAA;IAC/C,MAAI,OAAO,IAAI,CAAC,cAAZ,gBAAJ;UAA2C,yBAAA;IAC3C,MAAI,OAAO,IAAI,CAAC,yBAAZ,KAA0C,WAA9C;UAAgD,iBAAA,KAAsB;IAEtE,MAAI,0CAA0C,aAA9C;IACA,qEAAA;;IAKA,MAAM,OAAO;6BACW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4CAkCe;IAAA,UAAA,eAAA,KAAA,KAAA,CAAA,EAAA;IAAA,QAAA,eAAA,GAAA,IAAA;;;;;OAnCvC;;IAwCC,EAAAhF,qBAAC,GAAD,kBAAA,QAAA,KAAA,QAAA;IAGA,gBAAA;;;;AAMLA,yBAAC,CAAC,YAAA;IACE;IACA,MAAI,QAAQ,GAAoB,gCAA6B,0BAA7B,gBAAA,+BAAA,4BAAgI,0BAAhK,CAFF;;IAIE,MAAI,CAAC,kCAAL;IACA,EAAA,CAAC,CAAC,IAAF,CAAO,WAAA,GAAA;IACD,IAAAA,qBAAC,CAAC,CAAD,CAAD,kBAAA;IACL,GAFD;IAGH,CARA,CAAD;;;;AAiBEA,yBAAC,GAAD,kBAAA,CAAuC,QAAvC,YAAA", "sourcesContent": ["/*! \n *  Multiple select dropdown with filter jQuery plugin.\n *  Copyright (C) 2022  <PERSON>  github.com/andreww1011\n *\n *  This library is free software; you can redistribute it and/or\n *  modify it under the terms of the GNU Lesser General Public\n *  License as published by the Free Software Foundation; either\n *  version 2.1 of the License, or (at your option) any later version.\n * \n *  This library is distributed in the hope that it will be useful,\n *  but WITHOUT ANY WARRANTY; without even the implied warranty of\n *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n *  Lesser General Public License for more details.\n * \n *  You should have received a copy of the GNU Lesser General Public\n *  License along with this library; if not, write to the Free Software\n *  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301\n *  USA\n */\nimport $, { map } from 'jquery';\n\nconst NULL_OPTION = new class implements Option {\n    initialize(): void {}\n    select(): void {}\n    deselect(): void {}\n    enable(): void {}\n    disable(): void {}\n    isSelected(): boolean {return false;}\n    isDisabled(): boolean {return true;}\n    getListItem(): HTMLElement {return document.createElement('div');}\n    getSelectedItemBadge(): HTMLElement {return document.createElement('div');}\n    getLabel(): string {return 'NULL_OPTION'}\n    getValue(): string {return 'NULL_OPTION'}\n    show(): void {}\n    hide(): void {}\n    isHidden(): boolean {return true;}\n    focus(): void {}\n    activate(): void {}\n    deactivate(): void {}\n}  \n\ninterface Option {\n    initialize(): void;\n    select(): void;\n    deselect(): void;\n    enable(): void;\n    disable(): void;\n    isSelected(): boolean;\n    isDisabled(): boolean;\n    getListItem(): HTMLElement;\n    getSelectedItemBadge(): HTMLElement;\n    getLabel(): string;\n    getValue(): string;\n    show(): void;\n    hide(): void;\n    isHidden(): boolean;\n    focus(): void;\n    activate(): void;\n    deactivate(): void;\n}\n\ninterface SelectAllOption extends Option {\n    markSelectAll(): void;\n    markSelectPartial(): void;\n    markSelectAllNotDisabled(): void;\n    markDeselect(): void;\n}\n\nconst DEBUG = false;\n\nexport default class FilterMultiSelect {\n\n    private static SingleOption = class implements Option {\n        protected div: HTMLDivElement;\n        protected checkbox: HTMLInputElement;\n        protected labelFor: HTMLLabelElement;\n        protected closeButton: HTMLButtonElement;\n        protected selectedItemBadge: HTMLSpanElement;\n        protected fms: FilterMultiSelect;\n        protected active: boolean;\n        protected disabled: boolean;\n        private initiallyChecked: boolean;\n    \n        constructor(fms: FilterMultiSelect, row: number, name:string, label: string, value: string, checked: boolean, disabled: boolean) {\n            this.fms = fms;\n            this.div = document.createElement('div');\n            this.checkbox = document.createElement('input');\n            this.checkbox.type = 'checkbox';\n            let id: string = name + '-' + row.toString();\n            let nchbx: string = id + '-chbx';\n            this.checkbox.id = nchbx;\n            this.checkbox.name = name;\n            this.checkbox.value = value;\n            this.initiallyChecked = checked;\n            this.checkbox.checked = false;\n            this.checkbox.disabled = disabled;\n            this.labelFor = document.createElement('label');\n            this.labelFor.htmlFor = nchbx;\n            this.labelFor.textContent = label;\n            this.div.append(this.checkbox, this.labelFor);\n            this.closeButton = document.createElement('button');\n            this.closeButton.type = 'button';\n            this.closeButton.innerHTML = '&times;';\n            this.selectedItemBadge = document.createElement('span');\n            this.selectedItemBadge.setAttribute('data-id',id);\n            this.selectedItemBadge.textContent = label;\n            this.selectedItemBadge.append(this.closeButton);\n            this.disabled = disabled;\n            this.active = true;\n        }\n    \n        private log(m: string, e: Event):void {\n            if (DEBUG) {\n                console.log(e.timeStamp + \" - \" + m + \":\" + e.type + \":\" + e.target)\n            }\n        }\n    \n        public initialize(): void {\n            this.div.className = 'dropdown-item custom-control';\n            this.checkbox.className = 'custom-control-input custom-checkbox';\n            this.labelFor.className = 'custom-control-label';\n            this.selectedItemBadge.className = 'item';\n            if (this.initiallyChecked) {\n                this.selectNoDisabledCheck();\n            }\n            if (this.disabled) {\n                this.setDisabledViewState();\n            }\n            this.fms.update();\n            this.checkbox.addEventListener('change', (e: Event) => {\n                e.stopPropagation();\n                if (this.isDisabled() || this.fms.isDisabled()) {\n                    e.preventDefault();\n                    return;\n                }\n                if (DEBUG) {\n                    this.log('checkbox',e);\n                }\n                \n                if (this.isSelected()) {\n                    this.select();\n                } else {\n                    this.deselect();\n                }\n                let numShown = this.fms.showing.length;\n                if (numShown === 1) {\n                    this.fms.clearFilterAndRefocus();\n                }\n            }, true);\n            this.checkbox.addEventListener('keyup', (e: KeyboardEvent) => {\n                if (DEBUG) {\n                    this.log('checkbox',e);\n                }\n                switch (e.key) {\n                    case \"Enter\":\n                        e.stopPropagation();\n                        this.checkbox.dispatchEvent(new MouseEvent('click'));\n                        break;\n                    default:\n                        break;\n                }\n            }, true)\n            this.closeButton.addEventListener('click', (e: Event) => {\n                e.stopPropagation();\n                if (this.isDisabled() || this.fms.isDisabled()) return;\n                if (DEBUG) {\n                    this.log('closeButton',e);\n                }\n                this.deselect();\n                if (!this.fms.isClosed()) {\n                    this.fms.refocusFilter();\n                }\n            }, true);\n            this.checkbox.tabIndex = -1;\n            this.closeButton.tabIndex = -1;\n        }\n    \n        public select(): void {\n            if (this.isDisabled()) return;\n            this.selectNoDisabledCheck();\n            this.fms.update();\n        }\n\n        private selectNoDisabledCheck(): void {\n            if (!this.fms.canSelect() || !this.isActive()) return;\n            this.checkbox.checked = true;\n            this.fms.queueOption(this);\n            this.fms.dispatchSelectedEvent(this);\n        }\n    \n        public deselect(): void {\n            if (this.isDisabled()) return;\n            this.checkbox.checked = false;\n            this.fms.unqueueOption(this);\n            this.fms.dispatchDeselectedEvent(this);\n            this.fms.update();\n        }\n    \n        public enable(): void {\n            this.disabled = false;\n            this.setEnabledViewState();\n            this.fms.update();\n        }\n\n        private setEnabledViewState(): void {\n            this.checkbox.disabled = false;\n            this.selectedItemBadge.classList.remove('disabled');\n        }\n    \n        public disable(): void {\n            this.disabled = true;\n            this.setDisabledViewState();\n            this.fms.update();\n        }\n\n        private setDisabledViewState(): void {\n            this.checkbox.disabled = true;\n            this.selectedItemBadge.classList.add('disabled');\n        }\n    \n        public isSelected(): boolean {\n            return this.checkbox.checked;\n        }\n    \n        public isDisabled(): boolean {\n            return this.checkbox.disabled;\n        }\n    \n        public getListItem(): HTMLElement {\n            return this.div;\n        }\n    \n        public getSelectedItemBadge(): HTMLElement {\n            return this.selectedItemBadge;\n        }\n    \n        public getLabel(): string {\n            return this.labelFor.textContent;\n        }\n    \n        public getValue(): string {\n            return this.checkbox.value;\n        }\n    \n        public show(): void {\n            this.div.hidden = false;\n        }\n    \n        public hide(): void {\n            this.div.hidden = true;\n        }\n    \n        public isHidden(): boolean {\n            return this.div.hidden;\n        }\n\n        public focus(): void {\n            this.labelFor.focus();\n        }\n\n        isActive(): boolean {\n            return this.active;\n        }\n\n        public activate(): void {\n            this.active = true;\n            if (!this.disabled) {\n                this.setEnabledViewState();\n            }\n        }\n\n        public deactivate(): void {\n            this.active = false;\n            this.setDisabledViewState();\n        }\n    }\n\n    private static createOptions(fms: FilterMultiSelect, name: string, htmlOptions: Array<HTMLOptionElement>, jsOptions: Array<[label:string, value:string, selected?:boolean, disabled?:boolean]>): Array<Option> {\n        let htmloptions =  htmlOptions.map((o, i) => {\n            FilterMultiSelect.checkValue(o.value, o.label);\n            return new FilterMultiSelect.SingleOption(fms, i, name, o.label, o.value, o.defaultSelected, o.disabled);\n        });\n        let j = htmlOptions.length;\n        let jsoptions = jsOptions.map((o, i) => {\n            let label: string = o[0];\n            let value: string = o[1];\n            let selected: boolean = o[2];\n            let disabled: boolean = o[3];\n            FilterMultiSelect.checkValue(value, label);\n            return new FilterMultiSelect.SingleOption(fms, j+i, name, label, value, selected, disabled);\n\n        });\n        let opts = htmloptions.concat(jsoptions);\n        let counts: any = {};\n        opts.forEach((o) => {\n            let v: string = o.getValue();\n            if (counts[v] === undefined) {\n                counts[v] = 1;\n            } else {\n                throw new Error(\"Duplicate value: \" + o.getValue() + \" (\" + o.getLabel() + \")\");\n            }\n        });\n        return opts;\n    }\n\n    private static checkValue(value:string, label:string):void {\n        if (value === \"\") {\n            throw new Error(\"Option \" + label + \" does not have an associated value.\");\n        }\n    }\n\n    private static UnrestrictedSelectAllOption = class extends FilterMultiSelect.SingleOption implements SelectAllOption {\n        constructor(fms: FilterMultiSelect, name: string, label: string) {\n            super(fms,-1,name,label,'',false,false); //magic number\n            this.checkbox.indeterminate = false;\n        }\n\n        public markSelectAll(): void {\n            this.checkbox.checked = true;\n            this.checkbox.indeterminate = false;\n        }\n\n        public markSelectPartial(): void {\n            this.checkbox.checked = false;\n            this.checkbox.indeterminate = true;\n        }\n\n        public markSelectAllNotDisabled(): void {\n            this.checkbox.checked = true;\n            this.checkbox.indeterminate = true;\n        }\n\n        public markDeselect(): void {\n            this.checkbox.checked = false;\n            this.checkbox.indeterminate = false;\n        }\n\n        public select(): void {\n            if (this.isDisabled()) return;\n            this.fms.options.filter((o) => !o.isSelected())\n                .forEach((o) => o.select());\n            this.fms.update();\n        }\n    \n        public deselect(): void {\n            if (this.isDisabled()) return;\n            this.fms.options.filter((o) => o.isSelected())\n                .forEach((o) => o.deselect());\n            this.fms.update();\n        }\n\n        public enable(): void {\n            this.disabled = false;\n            this.checkbox.disabled = false;\n        }\n    \n        public disable(): void {\n            this.disabled = true;\n            this.checkbox.disabled = true;\n        }\n    }\n\n    private static RestrictedSelectAllOption = class implements SelectAllOption {\n        private usao: SelectAllOption;\n        \n        constructor(fms: FilterMultiSelect, name: string, label: string) {\n            this.usao = new FilterMultiSelect.UnrestrictedSelectAllOption(fms,name,label);\n        }\n        \n        initialize(): void {this.usao.initialize();}\n        select(): void {}\n        deselect(): void {this.usao.deselect();}\n        enable(): void {}\n        disable(): void {}\n        isSelected(): boolean {return false;}\n        isDisabled(): boolean {return true;}\n        getListItem(): HTMLElement {return document.createElement('div');}\n        getSelectedItemBadge(): HTMLElement {return document.createElement('div');}\n        getLabel(): string {return 'RESTRICTED_SELECT_ALL_OPTION'}\n        getValue(): string {return 'RESTRICTED_SELECT_ALL_OPTION'}\n        show(): void {}\n        hide(): void {}\n        isHidden(): boolean {return true;}\n        focus(): void {}\n        markSelectAll(): void {}\n        markSelectPartial(): void {}\n        markSelectAllNotDisabled(): void {}\n        markDeselect(): void {}\n        activate(): void {}\n        deactivate(): void {}\n    }\n\n    public static EventType = {\n        SELECTED: \"optionselected\",\n        DESELECTED: \"optiondeselected\",\n    } as const;\n\n    private static createEvent(e: string, n: string, v: string, l: string): CustomEvent {\n        const event = new CustomEvent(e, {\n            detail: {\n                name: n,\n                value: v,\n                label: l\n            },\n            bubbles: true,\n            cancelable: true,\n            composed: false,\n        });\n        return event;\n    }\n\n    private options: Array<Option>;\n    private selectAllOption: SelectAllOption;\n    private div: HTMLDivElement;\n    private viewBar: HTMLDivElement;\n    private placeholder: HTMLSpanElement;\n    private selectedItems: HTMLSpanElement;\n    private dropDown: HTMLDivElement;\n    private filter: HTMLDivElement;\n    private filterInput: HTMLInputElement;\n    private clearButton: HTMLButtonElement;\n    private items: HTMLDivElement;\n    private caseSensitive: boolean;\n    private disabled: boolean;\n    private allowEnablingAndDisabling: boolean;\n    private filterText: string;\n    private showing: Array<number>;\n    private itemFocus: number;\n    private name: string;\n    private label: HTMLSpanElement;\n    private maxNumSelectedItems: number;\n    private numSelectedItems: number;\n    private selectionCounter: HTMLSpanElement;\n\n    constructor (selectTarget: JQuery<HTMLElement>, args: Args) {        \n        let t = selectTarget.get(0);\n        if (!(t instanceof HTMLSelectElement)) {\n            throw new Error(\"JQuery target must be a select element.\");\n        }\n        let select: HTMLSelectElement = t;\n        let name: string = select.name;\n        if (!name) {\n            throw new Error(\"Select element must have a name attribute.\");\n        }\n        this.name = name;\n        let array: Array<HTMLOptionElement> = selectTarget.find('option').toArray();\n        this.options = FilterMultiSelect.createOptions(this, name, array, args.items);\n\n        // restrict selection\n        this.numSelectedItems = 0;\n        this.maxNumSelectedItems =  !select.multiple ? 1 : \n                                    args.selectionLimit > 0 ? args.selectionLimit :\n                                    parseInt(select.getAttribute('multiple')) > 0 ? parseInt(select.getAttribute('multiple')) :\n                                    0; //magic number \n        const numOptions: number = this.options.length;\n        const restrictSelection: boolean = this.maxNumSelectedItems > 0 && this.maxNumSelectedItems < numOptions;\n        this.maxNumSelectedItems = restrictSelection ? this.maxNumSelectedItems : numOptions + 1;  //magic number\n        this.selectAllOption = restrictSelection ? \n                new FilterMultiSelect.RestrictedSelectAllOption(this, name, args.selectAllText) : \n                new FilterMultiSelect.UnrestrictedSelectAllOption(this, name, args.selectAllText);\n\n        // filter box\n        this.filterInput = document.createElement('input');\n        this.filterInput.type = 'text';\n        this.filterInput.placeholder = args.filterText;\n        this.clearButton = document.createElement('button');\n        this.clearButton.type = 'button';\n        this.clearButton.innerHTML = '&times;';\n        this.filter = document.createElement('div');\n        this.filter.append(this.filterInput, this.clearButton);\n\n        // items\n        this.items = document.createElement('div');\n        this.items.append(this.selectAllOption.getListItem());\n        this.options.forEach((o: Option) => this.items.append(o.getListItem()));\n\n        // dropdown list\n        this.dropDown = document.createElement('div');\n        this.dropDown.append(this.filter, this.items);\n\n        // placeholder\n        this.placeholder = document.createElement('span');\n        this.placeholder.textContent = args.placeholderText;\n        this.selectedItems = document.createElement('span');\n\n        // label\n        this.label = document.createElement('span');\n        this.label.textContent = args.labelText;\n        let customLabel: boolean = args.labelText.length != 0;\n        if (!customLabel) {\n            this.label.hidden = true;\n        }\n\n        // selection counter\n        this.selectionCounter = document.createElement('span');\n        this.selectionCounter.hidden = !restrictSelection;\n\n        // viewbar\n        this.viewBar = document.createElement('div');\n        this.viewBar.append(this.label, this.selectionCounter, this.placeholder, this.selectedItems);\n\n        this.div = document.createElement('div');\n        this.div.id = select.id;\n        this.div.append(this.viewBar, this.dropDown);\n\n        this.caseSensitive = args.caseSensitive;\n        this.disabled = select.disabled;\n        this.allowEnablingAndDisabling = args.allowEnablingAndDisabling;\n        this.filterText = '';\n        this.showing = new Array<number>();\n        this.itemFocus = -2; //magic number\n\n        this.initialize();\n    }\n\n    private initialize(): void {\n        this.options.forEach(o => o.initialize());\n        this.selectAllOption.initialize();\n        \n        \n        this.filterInput.className = 'form-control';\n        this.clearButton.tabIndex = -1;\n\n        this.filter.className = 'filter dropdown-item'\n        this.items.className = 'items dropdown-item';\n        this.dropDown.className = 'dropdown-menu';\n\n        this.placeholder.className = 'placeholder';\n        this.selectedItems.className = 'selected-items';\n        this.viewBar.className = 'viewbar form-control dropdown-toggle';\n        this.label.className = 'col-form-label mr-2 text-dark';\n        this.selectionCounter.className = 'mr-2';\n\n        this.div.className = 'filter-multi-select dropdown';\n        if (this.maxNumSelectedItems > 1) {\n            let v: string = this.maxNumSelectedItems >= this.options.length ? \"\" : this.maxNumSelectedItems.toString();\n            this.div.setAttribute('multiple',v);\n        } else {\n            this.div.setAttribute('single',\"\");\n        }\n\n        if (this.isDisabled()) {\n            this.disableNoPermissionCheck();\n        }\n\n        this.attachDropdownListeners();\n        this.attachViewbarListeners();\n        \n        this.closeDropdown();\n    }\n\n    private log(m: string, e: Event):void {\n        if (DEBUG) {\n            console.log(e.timeStamp + \" - \" + m + \":\" + e.type + \":\" + e.target);\n        }\n    }\n\n    private attachDropdownListeners(): void {\n        this.filterInput.addEventListener('keyup',(e: KeyboardEvent) => {\n            if (DEBUG) {\n                this.log('filterInput',e);\n            }\n            e.stopImmediatePropagation();\n            this.updateDropdownList();\n            let numShown = this.showing.length;\n            switch(e.key) {\n                case \"Enter\":\n                    if (numShown === 1) {\n                        let o: Option = this.options[this.showing[0]]; //magic number\n                        if (!o.isDisabled()) {\n                            if (o.isSelected()) {\n                                o.deselect();\n                            } else {\n                                o.select();\n                            }\n                            this.clearFilterAndRefocus();\n                        }\n                    }\n                    break;\n                case \"Escape\":\n                    if (this.filterText.length > 0) {\n                        this.clearFilterAndRefocus();\n                    } else {\n                        this.closeDropdown();\n                    }\n                    break;\n                default:\n                    break;\n            }   \n        }, true);\n        this.clearButton.addEventListener('click', (e: MouseEvent) => {\n            if (DEBUG) {\n                this.log('clearButton',e);\n            }\n            e.stopImmediatePropagation();\n            let text = this.filterInput.value;\n            if (text.length > 0) {\n                this.clearFilterAndRefocus();\n            } else {\n                this.closeDropdown();\n            }\n        }, true);\n    }\n\n    private updateDropdownList(): void {\n        let text = this.filterInput.value;\n        if (text.length > 0) {\n            this.selectAllOption.hide();\n        } else {\n            this.selectAllOption.show();\n        }\n        let showing = new Array<number>();\n        if (this.caseSensitive) {\n            this.options.forEach((o: Option, i: number) => {\n                if (o.getLabel().indexOf(text) !== -1) { //magic number\n                    o.show();\n                    showing.push(i);\n                } else {\n                    o.hide();\n                }\n            });\n        } else {\n            this.options.forEach((o: Option, i: number) => {\n                if (o.getLabel().toLowerCase().indexOf(text.toLowerCase()) !== -1 ) { //magic number \n                    o.show();\n                    showing.push(i);\n                } else {\n                    o.hide();\n                }\n            });\n        }\n        this.filterText = text;\n        this.showing = showing;\n    }\n\n    private clearFilterAndRefocus(): void {\n        if (DEBUG) {\n            console.log('clear filter');\n        }\n        this.filterInput.value = '';\n        this.updateDropdownList();\n        this.refocusFilter();\n    }\n\n    private refocusFilter() {\n        if (DEBUG) {\n            console.log('refocus filter');\n        }\n        this.filterInput.focus();\n        this.itemFocus = -2; //magic number\n    }\n\n    private attachViewbarListeners(): void {\n        this.viewBar.addEventListener('click',(e) => {\n            if (DEBUG) {\n                this.log('viewBar',e);\n            }\n            if (this.isClosed()) {\n                this.openDropdown();\n            } else {\n                this.closeDropdown();\n            }\n        });\n    }\n\n    public isClosed(): boolean {\n        return !this.dropDown.classList.contains('show');\n    }\n\n    private setTabIndex(): void {\n        if (this.isDisabled()) {\n            this.div.tabIndex = -1;\n        } else {\n            if (this.isClosed()) {\n                this.div.tabIndex = 0;\n            } else {\n                this.div.tabIndex = -1;\n            }\n        }\n    }\n\n    private closeDropdown(): void {\n        if (DEBUG) {\n            console.log('close');\n        }\n        document.removeEventListener('keydown', this.documentKeydownListener, true);\n        document.removeEventListener('click', this.documentClickListener, true);\n        this.dropDown.classList.remove('show');\n        setTimeout(() => {\n            this.setTabIndex();    \n        }, 100); //magic number\n        this.div.addEventListener('mousedown', this.fmsMousedownListener, true);\n        this.div.addEventListener('focus', this.fmsFocusListener);\n    }\n\n    private documentKeydownListener = (e: KeyboardEvent) => {\n        if (DEBUG) {\n            this.log('document',e);\n            console.log(e.key);\n        }\n        switch(e.key) {\n            case \"Tab\":\n                e.stopPropagation();\n                this.closeDropdown();\n                break;\n            case \"ArrowUp\":\n                e.stopPropagation();\n                e.preventDefault();\n                if (DEBUG) {\n                    console.log(\"up\");\n                }\n                this.decrementItemFocus();\n                this.focusItem();\n                break;\n            case \"ArrowDown\":\n                e.stopPropagation();\n                e.preventDefault();\n                if (DEBUG) {\n                    console.log(\"down\");\n                }\n                this.incrementItemFocus();\n                this.focusItem();\n                break;\n            case \"Enter\":\n            case \"Spacebar\":\n            case \" \":\n                //swallow to allow checkbox change to work\n                break;\n            default:\n                //send key to filter\n                this.refocusFilter();\n                break;\n        }\n    };\n\n    private incrementItemFocus(): void {\n        if (this.itemFocus >= this.options.length - 1) return; \n        let i = this.itemFocus;\n        do {\n            i++;\n        } while ((i == -1 && (this.selectAllOption.isDisabled() || this.selectAllOption.isHidden())) || //magic number\n            (i >= 0 && i < this.options.length && (this.options[i].isDisabled() || this.options[i].isHidden())));\n        this.itemFocus = i > this.options.length - 1 ? this.itemFocus : i;\n        if (DEBUG) {\n            console.log(\"item focus: \"+ this.itemFocus);\n        }\n    }\n\n    private decrementItemFocus(): void {\n        if (this.itemFocus <= -2) return; //magic number\n        let i = this.itemFocus;\n        do {\n            i--;\n            \n        } while ((i == -1 && (this.selectAllOption.isDisabled() || this.selectAllOption.isHidden())) ||\n            (i >= 0 && (this.options[i].isDisabled() || this.options[i].isHidden())) &&\n            i > -2); //magic number\n        this.itemFocus = i; \n        if (DEBUG) {\n            console.log(\"item focus: \"+ this.itemFocus);\n        }\n    }\n\n    private focusItem(): void {\n        if (this.itemFocus === -2) {\n            this.refocusFilter();\n        } else if (this.itemFocus === -1) {\n            this.selectAllOption.focus();\n        } else {\n            this.options[this.itemFocus].focus();\n        }\n    }\n\n    private documentClickListener = (e: MouseEvent) => {\n        if (DEBUG) {\n            this.log('document',e);\n        }\n        if (this.div !== e.target && !this.div.contains(<Node>e.target)) {\n            this.closeDropdown();\n        }\n    };\n\n    private fmsFocusListener: EventListener = (e: FocusEvent) => {\n        if (DEBUG) {\n            this.log('div',e);\n        }\n        e.stopPropagation();\n        e.preventDefault();\n        this.viewBar.dispatchEvent(new MouseEvent('click'));\n    };\n\n    private fmsMousedownListener: EventListener = (e: MouseEvent) => {\n        if (DEBUG) {\n            this.log('div',e);\n        }\n        e.stopPropagation();\n        e.preventDefault();\n    }\n\n    private openDropdown() {\n        if (this.disabled) return;\n        if (DEBUG) {\n            console.log('open');\n        }\n        this.div.removeEventListener('mousedown', this.fmsMousedownListener, true);\n        this.div.removeEventListener('focus', this.fmsFocusListener);\n        this.dropDown.classList.add('show');\n        this.setTabIndex();\n        this.clearFilterAndRefocus();\n        document.addEventListener('keydown', this.documentKeydownListener, true);\n        document.addEventListener('click', this.documentClickListener, true);\n    }\n\n    private queueOption(option: Option): void {\n        if (this.options.indexOf(option) == -1) return;\n        this.numSelectedItems++;\n        $(this.selectedItems).append(option.getSelectedItemBadge());\n    }\n\n    private unqueueOption(option: Option): void {\n        if (this.options.indexOf(option) == -1) return;\n        this.numSelectedItems--;\n        $(this.selectedItems).children('[data-id=\"' + option.getSelectedItemBadge().getAttribute('data-id') + '\"]').remove();\n    }\n\n    private update(): void {\n        if (this.areAllSelected()) {\n            this.selectAllOption.markSelectAll();\n            this.placeholder.hidden = true;\n        } else if (this.areSomeSelected()) {\n            if (this.areOnlyDeselectedAlsoDisabled()) {\n                this.selectAllOption.markSelectAllNotDisabled();\n                this.placeholder.hidden = true;\n            } else {\n                this.selectAllOption.markSelectPartial();\n                this.placeholder.hidden = true;\n            }\n        } else {\n            this.selectAllOption.markDeselect();\n            this.placeholder.hidden = false;\n        }\n        if (this.areAllDisabled()) {\n            this.selectAllOption.disable();\n        } else {\n            this.selectAllOption.enable();\n        }\n        if (!this.canSelect()) {\n            this.options\n                .filter((o) => !o.isSelected())\n                .forEach((o) => o.deactivate());\n        } else {\n            this.options\n                .filter((o) => !o.isSelected())\n                .forEach((o) => o.activate());\n        }\n        this.updateSelectionCounter();\n    }\n\n    private areAllSelected(): boolean {\n        return this.options\n                .map((o) => o.isSelected())\n                .reduce((acc,cur) => acc && cur, true);\n    }\n\n    private areSomeSelected(): boolean {\n        return this.options\n                .map((o) => o.isSelected())\n                .reduce((acc,cur) => acc || cur, false);\n    }\n\n    private areOnlyDeselectedAlsoDisabled(): boolean {\n        return this.options\n                .filter((o) => !o.isSelected())\n                .map((o) => o.isDisabled())\n                .reduce((acc,cur) => acc && cur, true);\n    }\n\n    private areAllDisabled(): boolean {\n        return this.options\n                .map((o) => o.isDisabled())\n                .reduce((acc,cur) => acc && cur, true);\n    }\n\n    private isEnablingAndDisablingPermitted(): boolean {\n        return this.allowEnablingAndDisabling;\n    }\n\n    public getRootElement(): HTMLElement {\n        return this.div;\n    }\n\n    public hasOption(value: string): boolean {\n        return this.getOption(value) !== NULL_OPTION;\n    }\n\n    private getOption(value: string): Option {\n        for (const o of this.options) {\n            if (o.getValue() == value) {\n                return o;\n            }\n        }\n        return NULL_OPTION;\n    }\n\n    public selectOption(value: string): void {\n        if (this.isDisabled()) return;\n        this.getOption(value).select();\n    }\n\n    public deselectOption(value: string): void {\n        if (this.isDisabled()) return;\n        this.getOption(value).deselect();\n    }\n\n    public isOptionSelected(value: string): boolean {\n        return this.getOption(value).isSelected();\n    }\n\n    public enableOption(value: string): void {\n        if (!this.isEnablingAndDisablingPermitted()) return;\n        this.getOption(value).enable();\n    }\n\n    public disableOption(value: string): void {\n        if (!this.isEnablingAndDisablingPermitted()) return;\n        this.getOption(value).disable();\n    }\n\n    public isOptionDisabled(value: string): boolean {\n        return this.getOption(value).isDisabled();\n    }\n\n    public disable(): void {\n        if (!this.isEnablingAndDisablingPermitted()) return;\n        this.disableNoPermissionCheck();\n    }\n\n    private disableNoPermissionCheck(): void {\n        this.options.forEach((o) => this.setBadgeDisabled(o));\n        this.disabled = true;\n        this.div.classList.add('disabled');\n        this.viewBar.classList.remove('dropdown-toggle');\n        this.closeDropdown();\n    }\n\n    private setBadgeDisabled(o: Option):void {\n        o.getSelectedItemBadge().classList.add('disabled');\n    }\n\n    public enable(): void {\n        if (!this.isEnablingAndDisablingPermitted()) return;\n        this.options.forEach((o) => {\n            if (!o.isDisabled()) {\n                this.setBadgeEnabled(o);\n            }\n        });\n        this.disabled = false;\n        this.div.classList.remove('disabled');\n        this.setTabIndex(); \n        this.viewBar.classList.add('dropdown-toggle');\n    }\n\n    private setBadgeEnabled(o: Option):void {\n        o.getSelectedItemBadge().classList.remove('disabled');\n    }\n\n    public isDisabled(): boolean {\n        return this.disabled;\n    }\n\n    public selectAll(): void {\n        if (this.isDisabled()) return;\n        this.selectAllOption.select();\n    }\n\n    public deselectAll(): void {\n        if (this.isDisabled()) return;\n        this.selectAllOption.deselect();\n    }\n\n    private getSelectedOptions(includeDisabled = true): Array<Option> {\n        let a = this.options;\n        if (!includeDisabled) {\n            if (this.isDisabled()) {\n                return new Array();\n            }\n            a = a.filter((o) => !o.isDisabled());\n        }\n        a = a.filter((o) => o.isSelected());\n        return a;\n    }\n\n    public getSelectedOptionsAsJson(includeDisabled = true): string {\n        const data: any = {};\n        let a: Array<string> = this.getSelectedOptions(includeDisabled).map((o) => o.getValue());\n        data[this.getName()] = a;\n        let c = JSON.stringify(data, null, \"  \");\n        if (DEBUG) {\n            console.log(c);\n        }\n        return c;\n    }\n\n    public getName(): string {\n        return this.name;\n    }\n\n    private dispatchSelectedEvent(option: Option): void {\n        this.dispatchEvent(\n            FilterMultiSelect.EventType.SELECTED,\n            option.getValue(),\n            option.getLabel());\n    }\n\n    private dispatchDeselectedEvent(option: Option): void {\n        this.dispatchEvent(\n            FilterMultiSelect.EventType.DESELECTED,\n            option.getValue(),\n            option.getLabel());\n    }\n\n    private dispatchEvent(eventType: string, value: string, label: string): void {\n        let event: CustomEvent = FilterMultiSelect.createEvent(eventType, this.getName(), value, label);\n        this.viewBar.dispatchEvent(event);\n    }\n\n    private canSelect(): boolean {\n        return this.numSelectedItems < this.maxNumSelectedItems;\n    }\n\n    private updateSelectionCounter(): void {\n        this.selectionCounter.textContent = this.numSelectedItems + \"/\" + this.maxNumSelectedItems;\n    }\n}", "/*! \n *  Multiple select dropdown with filter jQuery plugin.\n *  Copyright (C) 2022  Andrew <PERSON>  github.com/andreww1011\n *\n *  This library is free software; you can redistribute it and/or\n *  modify it under the terms of the GNU Lesser General Public\n *  License as published by the Free Software Foundation; either\n *  version 2.1 of the License, or (at your option) any later version.\n * \n *  This library is distributed in the hope that it will be useful,\n *  but WITHOUT ANY WARRANTY; without even the implied warranty of\n *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU\n *  Lesser General Public License for more details.\n * \n *  You should have received a copy of the GNU Lesser General Public\n *  License along with this library; if not, write to the Free Software\n *  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301\n *  USA\n */\nimport $ from 'jquery';\nimport FilterMultiSelect from './FilterMultiSelect';\n\n// define the plugin function on the jQuery extension point.\n($.fn as any).filterMultiSelect = function (this: JQuery, args: Args): any {\n    let target = this;\n    // merge the global options with the per-call options.\n    args = $.extend({}, ($.fn as any).filterMultiSelect.args, args);\n\n    // factory defaults\n    if (typeof args.placeholderText === 'undefined') args.placeholderText = 'nothing selected';\n    if (typeof args.filterText === 'undefined') args.filterText = 'Filter';\n    if (typeof args.selectAllText === 'undefined') args.selectAllText = 'Select All';\n    if (typeof args.labelText === 'undefined') args.labelText = '';\n    if (typeof args.selectionLimit === 'undefined') args.selectionLimit = 0;\n    if (typeof args.caseSensitive === 'undefined') args.caseSensitive = false;\n    if (typeof args.allowEnablingAndDisabling === 'undefined') args.allowEnablingAndDisabling = true;\n    if (typeof args.items === 'undefined') args.items = new Array();\n    \n\n    let filterMultiSelect = new FilterMultiSelect(target, args);\n  \n    const fms = $(filterMultiSelect.getRootElement());\n    target.replaceWith(fms);\n    \n    var methods = {\n        hasOption: function(value: string):boolean {\n            return filterMultiSelect.hasOption(value);\n        },\n        selectOption: function(value: string):void {\n            filterMultiSelect.selectOption(value);\n        },\n        deselectOption: function(value: string):void {\n            filterMultiSelect.deselectOption(value);\n        },\n        isOptionSelected: function(value: string):boolean {\n            return filterMultiSelect.isOptionSelected(value);\n        },\n        enableOption: function(value: string):void {\n            filterMultiSelect.enableOption(value);\n        },\n        disableOption: function(value: string):void {\n            filterMultiSelect.disableOption(value);\n        },\n        isOptionDisabled: function(value: string):boolean {\n            return filterMultiSelect.isOptionDisabled(value);\n        },\n        enable: function():void {\n            filterMultiSelect.enable();\n        },\n        disable: function():void {\n            filterMultiSelect.disable();\n        },\n        selectAll: function():void {\n            filterMultiSelect.selectAll();\n        },\n        deselectAll: function():void {\n            filterMultiSelect.deselectAll();\n        },\n        getSelectedOptionsAsJson: function(includeDisabled = true):string {\n            return filterMultiSelect.getSelectedOptionsAsJson(includeDisabled);\n        }\n    };\n\n    // store applied element\n    ($.fn as any).filterMultiSelect.applied.push(methods);\n\n    return methods;\n};\n\n// activate plugin by targeting selector\n$(function () {\n    // factory defaults\n    let selector: string = typeof ($.fn as any).filterMultiSelect.selector === 'undefined' ? 'select.filter-multi-select' : ($.fn as any).filterMultiSelect.selector;\n    // target\n    let s: JQuery<HTMLElement> = $(selector);\n    s.each((i,e) => {\n        ($(e) as any).filterMultiSelect();\n    });\n});\n\n// store collection of applied elements\n($.fn as any).filterMultiSelect.applied = new Array();\n\n// define the plugin's global default selector.\n($.fn as any).filterMultiSelect.selector = undefined;\n\n// define the plugin's global default options.\n($.fn as any).filterMultiSelect.args = {};"]}