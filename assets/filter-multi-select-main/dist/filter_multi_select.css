/*! 
 *  Multiple select dropdown with filter jQuery plugin.
 *  Copyright (C) 2022  <PERSON>  github.com/andreww1011
 *
 *  This library is free software; you can redistribute it and/or
 *  modify it under the terms of the GNU Lesser General Public
 *  License as published by the Free Software Foundation; either
 *  version 2.1 of the License, or (at your option) any later version.
 * 
 *  This library is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 *  Lesser General Public License for more details.
 * 
 *  You should have received a copy of the GNU Lesser General Public
 *  License along with this library; if not, write to the Free Software
 *  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301
 *  USA
 */
:root {
    --fms-badge-text-color: white;
    --fms-badge-color: var(--primary)
}

.filter-multi-select.dropup, .filter-multi-select.dropdown {
    position: relative;
}

.filter-multi-select .dropdown-toggle::after {
    all: unset;
}

.filter-multi-select .dropdown-toggle:empty::after {
    all: unset;
}

.filter-multi-select > .dropdown-toggle::before {
    display: inline-block;
    margin-right: 0.255em;
    vertical-align: middle;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
}

.filter-multi-select > .dropdown-toggle:empty::before {
    margin-right: 0.255em;
}

.filter-multi-select > .viewbar {
    white-space: normal;
    font-size: 0.875rem;
    font-weight: 400;
    height: auto;
    cursor: pointer;
}

.filter-multi-select > .viewbar > .selected-items > .item {
    margin: .125rem .25rem .125rem 0;
    padding: 0px 0px 0px .5em;
    display: inline-flex;
    height: 1.875em;
    color: var(--fms-badge-text-color);
    background-color: var(--fms-badge-color);
    border-radius: 1.1em;
    align-items: center;
    vertical-align: baseline;
}

.filter-multi-select > .viewbar > .selected-items > .item > button {
    background-color: transparent;
    color: var(--fms-badge-text-color);
    border: 0;
    font-weight: 900;
    cursor: pointer;
}

.filter-multi-select > .viewbar > .selected-items > .item > button:hover {
    filter: contrast(50%);
}

.filter-multi-select > .viewbar > .selected-items > .item.disabled {
    display: inline-flex;
    padding: 0px .5em 0px .5em;
    filter: grayscale(80%) brightness(150%);
}

.filter-multi-select > .viewbar > .selected-items > .item.disabled > button {
    display: none;
}

.filter-multi-select > .dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0%;
    z-index: 1000;
    display: none;
    float: left;
    max-height: 50vh;
    min-width: 10rem;
    overflow-y: auto;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.875rem;
    text-align: left;
    list-style: none;
    background-color: #FFFFFF;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.25rem;
}

.filter-multi-select > .dropdown-menu.show {
    display: block;
}

.filter-multi-select > .dropdown-menu > .filter > input {
    font-size: 0.875rem;
}

.filter-multi-select > .dropdown-menu > .filter > button {
    position: absolute;
    border: 0;
    background-color: transparent;
    font-weight: 900;
    color: #ccc;
    right: 2rem;
    top: 1rem;
}

.filter-multi-select > .dropdown-menu > .filter > button:hover {
    color: #aaa;
}

.filter-multi-select .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.25rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}

.filter-multi-select .dropdown-item.disabled, .filter-multi-select .dropdown-item:disabled {
    color: #6c757d;
    pointer-events: none;
    background-color: transparent;
}

.filter-multi-select .dropdown-item:hover, .filter-multi-select .dropdown-item:focus  {
    background-color: inherit;
}

.filter-multi-select .dropdown-item.active, .filter-multi-select .dropdown-item:active {
    color: inherit;
}

.filter-multi-select .dropdown-item .custom-control-input {
    position: absolute;
    z-index: -1;
    opacity: 0;
}

.filter-multi-select .dropdown-item .custom-control-label {
    position: relative;
    margin-bottom: 0;
    vertical-align: top;
    display: inline-block;   
}

.filter-multi-select .dropdown-item .custom-control-label::before {
    border-radius: 0.25rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    position: absolute;
    top: 0.15625rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    pointer-events: none;
    content: "";
    background-color: #FFFFFF;
    border: #adb5bd solid 1px
}

.filter-multi-select .dropdown-item .custom-control-label::after {
    position: absolute;
    top: 0.15625rem;
    left: -1.5rem;
    display: block;
    width: 1rem;
    height: 1rem;
    content: "";
    background: no-repeat 50% / 50% 50%;
}

.filter-multi-select .dropdown-item .custom-checkbox:checked ~ .custom-control-label::before,
.filter-multi-select .dropdown-item .custom-checkbox:indeterminate ~ .custom-control-label::before {
    border-color: var(--fms-badge-color);
    background-color: var(--fms-badge-color);
}

.filter-multi-select .dropdown-item .custom-checkbox:checked:disabled ~ .custom-control-label::before,
.filter-multi-select .dropdown-item .custom-checkbox:indeterminate:disabled ~ .custom-control-label::before {
    border-color: var(--fms-badge-color);
    background-color: var(--fms-badge-color);
    filter: grayscale(80%) brightness(150%);
}

.filter-multi-select .dropdown-item .custom-checkbox:checked ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23FFFFFF' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e");
}

.filter-multi-select .dropdown-item .custom-checkbox:indeterminate ~ .custom-control-label::after {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23FFFFFF' d='M0 2h4'/%3e%3c/svg%3e");
}