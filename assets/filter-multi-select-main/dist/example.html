<!-- /*! 
 *  Multiple select dropdown with filter jQuery plugin.
 *  Copyright (C) 2022  Andrew <PERSON>  github.com/andreww1011
 *
 *  This library is free software; you can redistribute it and/or
 *  modify it under the terms of the GNU Lesser General Public
 *  License as published by the Free Software Foundation; either
 *  version 2.1 of the License, or (at your option) any later version.
 * 
 *  This library is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 *  Lesser General Public License for more details.
 * 
 *  You should have received a copy of the GNU Lesser General Public
 *  License along with this library; if not, write to the Free Software
 *  Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301
 *  USA
 */ -->
<!DOCTYPE html>
<html lang="en">

  <head>
    <meta charset="utf-8">
    <title>Filter Multi Select Plugin</title>
    <style>
      .notification {color: red; font-size: 85%;}
    </style>
  </head>

  <body>
    <!-- Load jQuery -->
    <script src="https://code.jquery.com/jquery-3.2.1.min.js" integrity="sha256-hwg4gsxgFZhOsEEamdOYGBf13FyQuiTwlAQgxVSNgt4=" crossorigin="anonymous"></script>
    <!-- Load Bootstrap -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">

    <!-- Load the plugin bundle. -->
    <link rel="stylesheet" href="filter_multi_select.css" />
    <script src="filter-multi-select-bundle.min.js"></script>

    <section class="alternate page-heading">
      <div class="container">
        <header>
          <h1>Dropdown Filter Multi-select jQuery plugin</h1>
        </header>
        <p class="lead"></p>
    </section>
    <hr>
    <div class="container">
      <p>Click below to open dropdown.<br />
        Use up/down arrow to focus item and space/enter to select.<br />
        Type to filter items. Press esc to clear or close.<br />
        If only one item is showing, press enter to select.<br />
        Tab/shift-tab to close and cycle to next dropdown.
      </p>
    </div>
    <form class="container" method="GET" id="form">
      <div class="row">
        <h4 class="col-12">Default Dropdown</h4>
      </div>
      <div class="row">
        <div class="col-2"></div>
        <div class="col-10" id="notifications">&nbsp;</div>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="animals">Animals</label>
        <div class="col-10">
          <select multiple name="animals" id="animals" class="filter-multi-select">
            <option value="bear">Bear</option>
            <option value="ant">Ant</option>
            <option value="salamander">Salamander</option>
            <option value="owl">Owl</option>
            <option value="frog">Frog</option>
            <option value="shark">Shark</option>
          </select>
        </div>
      </div>
      <div class="row">
        <h4 class="col-12">Dropdown with pre-sets and case-sensitive filtering</h4>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="shapes"></label>
        <div class="col-10">
          <select multiple name="shapes" id="shapes">
            <option value="1" selected>Square</option>
            <option value="2">circle</option>
            <option value="3">polygon</option>
            <option value="4">Ellipse</option>
            <option value="5">Triangle</option>
            <option value="6" label="rect" selected>Rectangle</option>
          </select>
        </div>
      </div>
      <div class="row">
        <h4 class="col-12">Dropdown with disabled items</h4>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="cars">Cars</label>
        <div class="col-10">
          <select multiple name="cars" id="cars">
            <option value="1" selected>Toyota</option>
            <option value="2">Honda</option>
            <option value="3">Ford</option>
            <option value="4" disabled >BMW</option>
            <option value="5" selected disabled label="Chevy">Chevrolet</option>
            <option value="6">Kia</option>
            <option value="7">Mercedes</option>
            <option value="8">Subaru</option>
          </select>
        </div>
      </div>
      <div class="row">
        <h4 class="col-12">Dropdown with all items disabled</h4>
        <div class="container">
          <button id="b1" type="button">enable Java</button>
          <button id="b2" type="button">disable Java</button>
        </div>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="programming_languages_1">Programming Languages</label>
        <div class="col-10">
          <select multiple name="programming_languages_1" id="programming_languages_1">
            <option value="1" disabled selected>Java</option>
            <option value="2" disabled>Python</option>
            <option value="3" disabled label="JS">Javascript</option>
            <option value="4" disabled>C++</option>
            <option value="5" disabled>Ruby</option>
            <option value="6" disabled>Fortran</option>
            <option value="7" disabled selected>Haskell</option>
            <option value="8" disabled>C#</option>
            <option value="9" disabled>Basic</option>
          </select>
        </div>
      </div>
      <div class="row">
        <h4 class="col-12">Disabled Dropdown</h4>
        <div class="container">
          <button id="b3" type="button">enable dropdown</button>
          <button id="b4" type="button">disable dropdown</button>
          <p>...but java is still disabled.<br /></p>
        </div>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="programming_languages_2">Programming Languages</label>
        <div class="col-10">
          <select multiple disabled name="programming_languages_2" id="programming_languages_2">
            <option value="1" disabled selected>Java</option>
            <option value="2">Python</option>
            <option value="3" label="JS">Javascript</option>
            <option value="4">C++</option>
            <option value="5">Ruby</option>
            <option value="6">Fortran</option>
            <option value="7" selected>Haskell</option>
            <option value="8">C#</option>
            <option value="9">Basic</option>
          </select>
        </div>
      </div>
      <div class="row">
        <h4 class="col-12">Dropdown with enabling/disabling not permitted</h4>
        <div class="container">
          <button id="b5" type="button">enable Java</button>
          <button id="b6" type="button">disable Java</button>
          <button id="b7" type="button">enable dropdown</button>
          <button id="b8" type="button">disable dropdown</button>
        </div>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="programming_languages_3">Programming Languages</label>
        <div class="col-10">
          <select multiple name="programming_languages_3" id="programming_languages_3">
            <option value="1" disabled selected>Java</option>
            <option value="2">Python</option>
            <option value="3" label="JS">Javascript</option>
            <option value="4">C++</option>
            <option value="5">Ruby</option>
            <option value="6">Fortran</option>
            <option value="7" selected>Haskell</option>
            <option value="8">C#</option>
            <option value="9">Basic</option>
          </select>
        </div>
      </div>
      <div class="row">
        <h4 class="col-12">Dropdown with items added via js</h4>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="cities">Cities</label>
        <div class="col-10">
          <select multiple name="cities" id="cities">
            <option value="1">London</option>
            <option value="2" selected >New York</option>
            <option value="3" selected disabled>Los Angeles</option>
            <option value="4" disabled>Paris</option>
          </select>
        </div>
      </div>
      <div class="row">
        <h4 class="col-12">Single Select</h4>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="colors">Colors</label>
        <div class="col-10">
          <select name="colors" id="colors">
            <option value="1">Red</option>
            <option value="2">Orange</option>
            <option value="3">Yellow</option>
            <option value="4" disabled>Green</option>
            <option value="5">Blue</option>
            <option value="6">Indigo</option>
            <option value="7">Violet</option>
          </select>
        </div>
      </div>
      <div class="row">
        <h4 class="col-12">Limit Selection Count</h4>
      </div>
      <div class="form-group row">
        <label class="col-2 col-form-label" for="trees">Trees</label>
        <div class="col-10">
          <select multiple name="trees" id="trees">
            <option value="1">Oak</option>
            <option value="2" selected disabled>Pine</option>
            <option value="3" selected >Maple</option>
            <option value="4" disabled>Poplar</option>
            <option value="5">Beech</option>
            <option value="6">Magnolia</option>
          </select>
        </div>
      </div>
      <button type="submit">Submit Form</button>
    </form>
    <div class="container">
      <button id="jsonbtn1">Get Json (include disabled)</button>
    </div>
    <div class="container">
      <span id="jsonresult1"></span>
    </div>
    <div class="container">
      <button id="jsonbtn2">Get Json (exclude disabled)</button>
    </div>
    <div class="container">
      <span id="jsonresult2"></span>
    </div>
    <script>
      // Use the plugin once the DOM has been loaded.
      $(function () {
        // Apply the plugin 
        var notifications = $('#notifications');
        $('#animals').on("optionselected", function(e) {
          createNotification("selected", e.detail.label);
        });
        $('#animals').on("optiondeselected", function(e) {
          createNotification("deselected", e.detail.label);
        });
        function createNotification(event,label) {
          var n = $(document.createElement('span'))
            .text(event + ' ' + label + "  ")
            .addClass('notification')
            .appendTo(notifications)
            .fadeOut(3000, function() {
              n.remove();
            });
        }
        var shapes = $('#shapes').filterMultiSelect({
          selectAllText: 'all...',
          placeholderText: 'click to select a shape',
          filterText: 'search',
          labelText: 'Shapes',
          caseSensitive: true,
        });
        var cars = $('#cars').filterMultiSelect();
        var pl1 = $('#programming_languages_1').filterMultiSelect();
        $('#b1').click((e) => {
          pl1.enableOption("1");
        });
        $('#b2').click((e) => {
          pl1.disableOption("1");
        });
        var pl2 = $('#programming_languages_2').filterMultiSelect();
        $('#b3').click((e) => {
          pl2.enable();
        });
        $('#b4').click((e) => {
          pl2.disable();
        });
        var pl3 = $('#programming_languages_3').filterMultiSelect({
          allowEnablingAndDisabling: false,
        });
        $('#b5').click((e) => {
		  pl3.enableOption("1");
        });
        $('#b6').click((e) => {
          pl3.disableOption("1");
        });
        $('#b7').click((e) => {
          pl3.enable();
        });
        $('#b8').click((e) => {
          pl3.disable();
        });
        var cities = $('#cities').filterMultiSelect({
          items: [["San Francisco","a"],
                  ["Milan","b",false,true],
                  ["Singapore","c",true],
                  ["Berlin","d",true,true],
                 ],
        });
        var colors = $('#colors').filterMultiSelect();
        var trees = $('#trees').filterMultiSelect({
          selectionLimit: 3,
        });
        $('#jsonbtn1').click((e) => {
           var b = true;
           $('#jsonresult1').text(JSON.stringify(getJson(b),null,"  "));
         });
         var getJson = function (b) {
           var result = $.fn.filterMultiSelect.applied
               .map((e) => JSON.parse(e.getSelectedOptionsAsJson(b)))
               .reduce((prev,curr) => {
                 prev = {
                   ...prev,
                   ...curr,
                 };
                 return prev;
               });
           return result;
         }
         $('#jsonbtn2').click((e) => {
           var b = false;
           $('#jsonresult2').text(JSON.stringify(getJson(b),null,"  "));
         });
        $('#form').on('keypress keyup', function(e) {
          var keyCode = e.keyCode || e.which;
          if (keyCode === 13) { 
            e.preventDefault();
            return false;
          }
        });
      });
    </script>
  </body>

</html>

