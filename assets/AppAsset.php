<?php
/**
 * @link http://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license http://www.yiiframework.com/license/
 */

namespace app\assets;

use rmrevin\yii\fontawesome\FA;
use yii\bootstrap4\BootstrapPluginAsset;
use yii\web\AssetBundle;
use yii\web\YiiAsset;

/**
 * Main application asset bundle.
 *
 * <AUTHOR> <<EMAIL>>
 * @since 2.0
 */
class AppAsset extends AssetBundle
{
    public $basePath = '@webroot';
    public $baseUrl = '@web';
    public $css = [
        'css/atomic.css',
        'css/site-new.css',
        'css/own.css',
        'css/bewertung.css',
        'css/inputicons.css',
        'css/loader.css',
        'css/form-styles.css'
    ];

    public $js = [
        'js/common.js',
        'js/wysiwyg.js',
        'js/tab.js',
        'js/dynamic_titles.js',
        'js/mathjax.js',
        'js/image.js',
        'js/jquery-ui.js',
    ];

    public $depends = [
        YiiAsset::class,
        BootstrapPluginAsset::class,
        //BootstrapFileinputAsset::class,
        \rmrevin\yii\fontawesome\AssetBundle::class,
        FiltermultiselectAsset::class,
        TinymceAsset::class,
        BootstrapdialogAsset::class,
        JqueryuiAsset::class
    ];
}
